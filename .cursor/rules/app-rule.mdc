---
description: Must Follow
globs: 
---

# Your rule content

- Always use venv environment to start the app. switch to venv by running command "source venv/bin/activate"
- App is started with command "python run.py"
- Stop any running process if required by running command "lsof -i :8080 | grep LISTEN | awk '{print $2}' | xargs kill -9 || true"
- Never create files with more than 2K lines of code, so it can be managed easily 
- Always update docs after each changes made
