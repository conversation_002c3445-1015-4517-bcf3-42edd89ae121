#!/usr/bin/env python3
"""
Import historical execution data from data.json into the database
"""

import os
import sys
import json
import logging

# Add the app directory to the Python path
script_dir = os.path.dirname(__file__)
app_dir = os.path.join(script_dir, 'app')
sys.path.insert(0, app_dir)

from utils.database import track_test_execution, init_db

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def import_execution_data_from_json(execution_id):
    """Import execution data from a data.json file into the database"""
    try:
        # Initialize database first
        init_db()
        logger.info("Database initialized")
        
        # Find the data.json file
        reports_dir = "/Users/<USER>/Documents/automation-tool/reports"
        execution_dir = os.path.join(reports_dir, execution_id)
        data_json_path = os.path.join(execution_dir, 'data.json')
        
        logger.info(f"Looking for data.json at: {data_json_path}")
        
        if not os.path.exists(data_json_path):
            logger.error(f"Data file not found: {data_json_path}")
            return False
            
        # Load the data.json file
        with open(data_json_path, 'r') as f:
            data = json.load(f)
            
        logger.info(f"Loaded data.json with {len(data.get('testCases', []))} test cases")
        
        # Extract suite information
        test_suite_id = data.get('id', execution_id)
        test_execution_id = execution_id
        imported_count = 0
        
        # Process each test case
        for test_case_idx, test_case in enumerate(data.get('testCases', [])):
            test_case_name = test_case.get('name', f'Test Case {test_case_idx}')
            # Clean up the test case name for filename
            clean_name = test_case_name.replace(' ', '_').replace('&', 'And').replace('\n', '').strip()
            test_case_filename = clean_name + '.json'
            test_case_id = test_case.get('id', f'test_case_{test_case_idx}')
            
            logger.info(f"Processing test case: {test_case_name}")
            
            # Import each step/action
            for step_idx, step in enumerate(test_case.get('steps', [])):
                action_id = step.get('action_id')
                action_type = step.get('action_type', 'unknown')
                step_status = step.get('status', 'unknown')
                retry_count = step.get('retry_count', 0)
                
                # Import this step into the database
                success = track_test_execution(
                    suite_id=test_suite_id,
                    test_idx=test_case_idx,
                    filename=test_case_filename,
                    status=step_status,
                    retry_count=retry_count,
                    max_retries=0,
                    error=step.get('error'),
                    in_progress=False,
                    step_idx=step_idx,
                    action_type=action_type,
                    action_params={'description': step.get('name', '')},
                    action_id=action_id,
                    test_case_id=test_case_id,
                    test_execution_id=test_execution_id
                )
                
                if success:
                    imported_count += 1
                else:
                    logger.warning(f"Failed to import step {step_idx} for test case {test_case_name}")
                    
        logger.info(f"Successfully imported {imported_count} execution steps to database")
        return True
        
    except Exception as e:
        logger.error(f"Error importing execution data: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return False

if __name__ == "__main__":
    execution_id = "testsuite_execution_20250627_181306"
    logger.info(f"Starting import for execution: {execution_id}")
    
    success = import_execution_data_from_json(execution_id)
    
    if success:
        logger.info("Import completed successfully!")
    else:
        logger.error("Import failed!")
        sys.exit(1)
