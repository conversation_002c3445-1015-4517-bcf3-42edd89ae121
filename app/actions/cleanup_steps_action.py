from .base_action import BaseAction
import logging
import traceback
import time

class CleanupStepsAction(BaseAction):
    """Handler for executing cleanup steps that always run regardless of test case status"""

    def execute(self, params):
        """
        Execute cleanup steps - these always run regardless of test case status

        Args:
            params: Dictionary containing:
                - test_case_id: ID of the test case to execute as cleanup
                - test_case_steps: Steps of the test case to execute

        Returns:
            dict: Result with status and message
        """
        if not self.controller:
            return {"status": "error", "message": "No device controller available"}

        # Get the test case ID from params
        test_case_id = params.get('test_case_id')
        test_case_steps = params.get('test_case_steps', [])

        if not test_case_id:
            return {"status": "error", "message": "No test case ID provided for cleanup"}

        self.logger.info(f"Starting cleanup steps execution for test case: {test_case_id}")

        # If steps are not provided, try to load them from the test case file
        if not test_case_steps:
            self.logger.info(f"No test case steps provided directly, attempting to load from file: {test_case_id}")
            try:
                # Import test_case_manager utilities with fallback
                try:
                    from ..utils.test_case_manager import TestCaseManager
                except ImportError:
                    try:
                        from utils.test_case_manager import TestCaseManager
                    except ImportError:
                        try:
                            from app.utils.test_case_manager import TestCaseManager
                        except ImportError:
                            # Fallback function if import fails
                            def TestCaseManager(*args, **kwargs):
                                return args[0] if args else None

                # Get the test cases directory
                try:
                    from config import DIRECTORIES
                    test_cases_dir = DIRECTORIES['TEST_CASES']
                except ImportError:
                    # Fallback to default directory
                    import os
                    test_cases_dir = '/Users/<USER>/Documents/automation-tool/test_cases'

                # Create a test case manager instance
                test_case_manager = TestCaseManager(test_cases_dir)

                # Load the test case
                test_case = test_case_manager.load_test_case(test_case_id)

                if test_case and 'actions' in test_case:
                    test_case_steps = test_case.get('actions', [])
                    self.logger.info(f"Loaded {len(test_case_steps)} steps from test case file: {test_case_id}")
                else:
                    self.logger.error(f"Failed to load test case or no actions found: {test_case_id}")
                    return {"status": "error", "message": f"Failed to load test case or no actions found: {test_case_id}"}
            except Exception as e:
                self.logger.error(f"Error loading test case: {e}")
                return {"status": "error", "message": f"Error loading test case: {str(e)}"}

        if not test_case_steps:
            return {"status": "error", "message": "No test case steps provided or loaded for cleanup"}

        # Create a new action factory instance
        try:
            from action_factory import ActionFactory
        except ImportError:
            try:
                from ..action_factory import ActionFactory
            except ImportError:
                import sys
                import os
                current_dir = os.path.dirname(os.path.abspath(__file__))
                parent_dir = os.path.dirname(current_dir)
                if parent_dir not in sys.path:
                    sys.path.insert(0, parent_dir)
                from action_factory import ActionFactory

        action_factory = ActionFactory(self.controller)

        # Execute all cleanup steps
        results = []
        success_count = 0
        step_index = 0
        step_retry_count = {}

        self.logger.info(f"Executing {len(test_case_steps)} cleanup steps")

        while step_index < len(test_case_steps):
            step = test_case_steps[step_index]

            # Initialize retry count for this step if not exists
            if step_index not in step_retry_count:
                step_retry_count[step_index] = 0

            try:
                # Get action type - try both 'type' and 'action_type' fields
                action_type = step.get('type') or step.get('action_type')
                action_id = step.get('action_id', '')

                # Log the step being executed
                self.logger.info(f"Executing cleanup step {step_index + 1}/{len(test_case_steps)}: {action_type or 'unknown'}")

                # Emit socket event for cleanup step start
                if hasattr(self.controller, 'socketio') and self.controller.socketio:
                    message = f'Executing cleanup step {step_index + 1}/{len(test_case_steps)}: {action_type or "unknown"}'
                    if action_id:
                        message += f' (action_id: {action_id})'

                    self.controller.socketio.emit('cleanup_step_result', {
                        'status': 'running',
                        'step_index': step_index,
                        'total_steps': len(test_case_steps),
                        'action_type': action_type,
                        'message': message,
                        'action_id': action_id,
                        'test_case_id': test_case_id
                    })

                # Execute the step using the action factory
                if not action_type:
                    self.logger.warning(f"Cleanup step {step_index + 1} has no action_type or type, skipping")

                    # Emit socket event for skipped step
                    if hasattr(self.controller, 'socketio') and self.controller.socketio:
                        self.controller.socketio.emit('cleanup_step_result', {
                            'status': 'warning',
                            'step_index': step_index,
                            'total_steps': len(test_case_steps),
                            'message': f"Cleanup step {step_index + 1} has no action_type or type, skipping",
                            'action_id': action_id,
                            'test_case_id': test_case_id
                        })

                    results.append({
                        "status": "warning",
                        "message": f"Cleanup step {step_index + 1} has no action_type or type, skipping",
                        "step_index": step_index
                    })
                    step_index += 1
                    continue

                # Skip hook actions during cleanup execution
                if action_type == 'hookAction' or step.get('type') == 'hookAction':
                    self.logger.info(f"Skipping hook action during cleanup execution (step {step_index + 1})")
                    results.append({
                        "status": "success",
                        "message": "Hook Action skipped during cleanup",
                        "step_index": step_index
                    })
                    success_count += 1
                    step_index += 1
                    continue

                # Skip cleanup steps within cleanup steps to prevent infinite loops
                if action_type == 'cleanupSteps':
                    self.logger.info(f"Skipping nested cleanup steps (step {step_index + 1})")
                    results.append({
                        "status": "success",
                        "message": "Nested cleanup steps skipped",
                        "step_index": step_index
                    })
                    success_count += 1
                    step_index += 1
                    continue

                # Execute the action
                result = action_factory.execute_action(action_type, step)

                # Add the step index to the result
                if isinstance(result, dict):
                    result['step_index'] = step_index
                    results.append(result)

                    # Check if the step succeeded
                    if result.get('status') == 'success':
                        # Step succeeded, count it and move to the next step
                        success_count += 1
                        # Reset retry count for this step since it succeeded
                        step_retry_count[step_index] = 0

                        # Emit socket event for successful step
                        if hasattr(self.controller, 'socketio') and self.controller.socketio:
                            self.controller.socketio.emit('cleanup_step_result', {
                                'status': 'success',
                                'step_index': step_index,
                                'total_steps': len(test_case_steps),
                                'message': f"Cleanup step {step_index + 1} completed successfully",
                                'action_id': action_id,
                                'test_case_id': test_case_id
                            })

                        step_index += 1
                    else:
                        # Step failed - for cleanup steps, we continue anyway but log the failure
                        self.logger.warning(f"Cleanup step {step_index + 1} failed: {result.get('message', 'Unknown error')}")

                        # Emit socket event for failed step
                        if hasattr(self.controller, 'socketio') and self.controller.socketio:
                            self.controller.socketio.emit('cleanup_step_result', {
                                'status': 'error',
                                'step_index': step_index,
                                'total_steps': len(test_case_steps),
                                'message': f"Cleanup step {step_index + 1} failed: {result.get('message', 'Unknown error')}",
                                'action_id': action_id,
                                'test_case_id': test_case_id
                            })

                        step_index += 1
                else:
                    self.logger.warning(f"Cleanup step {step_index + 1} returned non-dict result: {result}")

                    # Emit socket event for warning
                    if hasattr(self.controller, 'socketio') and self.controller.socketio:
                        self.controller.socketio.emit('cleanup_step_result', {
                            'status': 'warning',
                            'step_index': step_index,
                            'total_steps': len(test_case_steps),
                            'message': f"Cleanup step {step_index + 1} returned non-dict result",
                            'action_id': action_id,
                            'test_case_id': test_case_id
                        })

                    results.append({
                        "status": "warning",
                        "message": f"Cleanup step {step_index + 1} returned non-dict result: {result}",
                        "step_index": step_index
                    })
                    step_index += 1

                # Small delay between steps to prevent overwhelming the device
                time.sleep(0.1)

            except Exception as e:
                self.logger.error(f"Error executing cleanup step {step_index + 1}: {e}")
                self.logger.error(traceback.format_exc())
                results.append({
                    "status": "error",
                    "message": f"Error executing cleanup step {step_index + 1}: {str(e)}",
                    "step_index": step_index
                })
                # For cleanup steps, continue even if there's an error
                step_index += 1

        # Calculate final status
        total_steps = len(test_case_steps)
        failed_steps = total_steps - success_count

        if failed_steps == 0:
            final_status = "success"
            final_message = f"All {total_steps} cleanup steps completed successfully"
        else:
            final_status = "warning"  # Use warning instead of error for cleanup
            final_message = f"Cleanup completed with {failed_steps} failed steps out of {total_steps}"

        self.logger.info(f"Cleanup steps execution completed: {final_message}")

        return {
            "status": final_status,
            "message": final_message,
            "results": results,
            "total_steps": total_steps,
            "success_count": success_count,
            "failed_count": failed_steps
        }
