from .base_action import BaseAction
import logging
import traceback
import time

class HookAction(BaseAction):
    """Handler for hook actions that are executed when a step fails"""

    def execute(self, params):
        """
        Execute a hook action
        
        Args:
            params: Dictionary containing:
                - hook_type: Type of action to execute (e.g., 'tap', 'swipe')
                - hook_data: Data for the action
                
        Returns:
            dict: Result with status and message
        """
        if not self.controller:
            return {"status": "error", "message": "No device controller available"}
        
        self.logger.info(f"Executing hook action with params: {params}")
        
        # Get the hook type and data
        hook_type = params.get('hook_type')
        hook_data = params.get('hook_data', {})
        
        if not hook_type:
            return {"status": "error", "message": "No hook type provided"}
        
        # Create a new action with the hook type and data
        recovery_action = {
            'type': hook_type
        }
        
        # Handle specific action types
        if hook_type == 'tap':
            method = hook_data.get('method', 'coordinates')
            recovery_action['method'] = method
            
            if method == 'coordinates':
                recovery_action['x'] = hook_data.get('x', 0)
                recovery_action['y'] = hook_data.get('y', 0)
            elif method == 'locator':
                recovery_action['locator_type'] = hook_data.get('locator_type', '')
                recovery_action['locator_value'] = hook_data.get('locator_value', '')
            elif method == 'image':
                recovery_action['image_filename'] = hook_data.get('image_filename', '')
                recovery_action['threshold'] = hook_data.get('threshold', 0.7)
                recovery_action['timeout'] = hook_data.get('timeout', 10)
            elif method == 'text':
                recovery_action['text'] = hook_data.get('text', '')
                recovery_action['threshold'] = hook_data.get('threshold', 0.7)
                recovery_action['timeout'] = hook_data.get('timeout', 10)
        elif hook_type == 'doubleTap':
            method = hook_data.get('method', 'coordinates')
            recovery_action['method'] = method
            
            if method == 'coordinates':
                recovery_action['x'] = hook_data.get('x', 0)
                recovery_action['y'] = hook_data.get('y', 0)
            elif method == 'locator':
                recovery_action['locator_type'] = hook_data.get('locator_type', '')
                recovery_action['locator_value'] = hook_data.get('locator_value', '')
            elif method == 'image':
                recovery_action['image_filename'] = hook_data.get('image_filename', '')
                recovery_action['threshold'] = hook_data.get('threshold', 0.7)
                recovery_action['timeout'] = hook_data.get('timeout', 10)
        elif hook_type == 'swipe':
            recovery_action['start_x'] = hook_data.get('start_x', 0)
            recovery_action['start_y'] = hook_data.get('start_y', 0)
            recovery_action['end_x'] = hook_data.get('end_x', 0)
            recovery_action['end_y'] = hook_data.get('end_y', 0)
            recovery_action['duration'] = hook_data.get('duration', 300)
            recovery_action['count'] = hook_data.get('count', 1)
            recovery_action['interval'] = hook_data.get('interval', 0.5)
        elif hook_type == 'text':
            recovery_action['text'] = hook_data.get('text', '')
        elif hook_type == 'sendKeys':
            recovery_action['locator_type'] = hook_data.get('locator_type', '')
            recovery_action['locator_value'] = hook_data.get('locator_value', '')
            recovery_action['text'] = hook_data.get('text', '')
        elif hook_type == 'key':
            recovery_action['key'] = hook_data.get('key', '')
        elif hook_type == 'wait':
            recovery_action['duration'] = hook_data.get('duration', 5)
        elif hook_type == 'hideKeyboard':
            # No additional data needed
            pass
        elif hook_type == 'deviceBack':
            # No additional data needed
            pass
        elif hook_type == 'launchApp' or hook_type == 'restartApp' or hook_type == 'terminateApp':
            recovery_action['package_id'] = hook_data.get('package_id', '')
        elif hook_type == 'iosFunctions':
            recovery_action['function_name'] = hook_data.get('function_name', '')
            # Add other iOS function parameters
            for key, value in hook_data.items():
                if key != 'function_name':
                    recovery_action[key] = value
        elif hook_type == 'tapIfImageExists':
            recovery_action['image_filename'] = hook_data.get('image_filename', '')
            recovery_action['threshold'] = hook_data.get('threshold', 0.7)
            recovery_action['timeout'] = hook_data.get('timeout', 5)
        else:
            # For other action types, just copy all hook_data
            for key, value in hook_data.items():
                recovery_action[key] = value
        
        self.logger.info(f"Converted hook action to: {recovery_action}")
        
        # Get hook details for logging
        hook_details = self._get_hook_details(hook_type, hook_data)
        self.logger.info(f"Executing hook action: {hook_type} - {hook_details}")
        
        try:
            # Add a small delay before executing the hook action to ensure the UI is stable
            time.sleep(0.5)
            
            # Get the action factory with fallback imports
            try:
                from .action_factory import ActionFactory
            except ImportError:
                try:
                    from action_factory import ActionFactory
                except ImportError:
                    try:
                        from app.actions.action_factory import ActionFactory
                    except ImportError:
                        # Last resort - try importing from actions module
                        from actions.action_factory import ActionFactory

            action_factory = ActionFactory(self.controller)
            
            # Execute the recovery action
            result = action_factory.execute_action(hook_type, recovery_action)
            
            # Take a screenshot after the action
            screenshot_path = self.take_screenshot_after_action()
            
            # Process the result
            if isinstance(result, dict):
                success = result.get('status') == 'success'
                message = result.get('message', '')
                
                # Add hook details to the result
                result['hook_details'] = hook_details
                
                # Add screenshot URL if available
                if screenshot_path:
                    result['screenshot'] = screenshot_path
                
                return result
            elif isinstance(result, tuple):
                # If result is a tuple, assume it's (success, message, screenshot_url)
                success, message, screenshot_url = result
                
                # Return a dict with hook details
                return {
                    'success': success,
                    'message': message,
                    'screenshot': screenshot_url,
                    'hook_details': hook_details
                }
            else:
                # If result is not a tuple or dict, return a dict with the result and hook details
                return {
                    'success': bool(result),
                    'message': "Hook action executed" if result else "Hook action failed",
                    'hook_details': hook_details
                }
        except Exception as e:
            self.logger.error(f"Error executing hook action: {str(e)}")
            traceback.print_exc()
            return {
                'success': False,
                'message': f"Hook action execution failed: {str(e)}",
                'hook_details': hook_details
            }
    
    def _get_hook_details(self, hook_type, hook_data):
        """Get a human-readable description of the hook action"""
        if hook_type == 'tap':
            method = hook_data.get('method', 'coordinates')
            if method == 'coordinates':
                return f"tap at ({hook_data.get('x', 0)}, {hook_data.get('y', 0)})"
            elif method == 'locator':
                return f"tap on {hook_data.get('locator_type', '')}: '{hook_data.get('locator_value', '')}'"
            elif method == 'image':
                return f"tap on image '{hook_data.get('image_filename', '')}'"
            elif method == 'text':
                return f"tap on text '{hook_data.get('text', '')}'"
        elif hook_type == 'doubleTap':
            method = hook_data.get('method', 'coordinates')
            if method == 'coordinates':
                return f"double tap at ({hook_data.get('x', 0)}, {hook_data.get('y', 0)})"
            elif method == 'locator':
                return f"double tap on {hook_data.get('locator_type', '')}: '{hook_data.get('locator_value', '')}'"
            elif method == 'image':
                return f"double tap on image '{hook_data.get('image_filename', '')}'"
        elif hook_type == 'swipe':
            return f"swipe from ({hook_data.get('start_x', 0)}, {hook_data.get('start_y', 0)}) to ({hook_data.get('end_x', 0)}, {hook_data.get('end_y', 0)})"
        elif hook_type == 'text':
            return f"input text '{hook_data.get('text', '')}'"
        elif hook_type == 'sendKeys':
            return f"send keys '{hook_data.get('text', '')}' to {hook_data.get('locator_type', '')}: '{hook_data.get('locator_value', '')}'"
        elif hook_type == 'key':
            return f"press key '{hook_data.get('key', '')}'"
        elif hook_type == 'wait':
            return f"wait for {hook_data.get('duration', 5)} seconds"
        elif hook_type == 'hideKeyboard':
            return "hide keyboard"
        elif hook_type == 'deviceBack':
            return "press device back button"
        elif hook_type == 'launchApp':
            return f"launch app '{hook_data.get('package_id', '')}'"
        elif hook_type == 'restartApp':
            return f"restart app '{hook_data.get('package_id', '')}'"
        elif hook_type == 'terminateApp':
            return f"terminate app '{hook_data.get('package_id', '')}'"
        elif hook_type == 'iosFunctions':
            return f"iOS function '{hook_data.get('function_name', '')}'"
        elif hook_type == 'tapIfImageExists':
            return f"tap if image '{hook_data.get('image_filename', '')}' exists"
        else:
            return f"{hook_type} action"
