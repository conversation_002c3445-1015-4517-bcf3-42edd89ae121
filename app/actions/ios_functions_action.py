from .base_action import BaseAction
import logging
import traceback

class IosFunctionsAction(BaseAction):
    """Handler for iOS-specific functions using Airtest iOS API"""

    def execute(self, params):
        """
        Execute iOS-specific functions using Airtest iOS API

        Args:
            params: Dictionary containing:
                - function_name: Name of the iOS function to execute (e.g., 'home', 'lock', 'unlock')
                - function_params: Optional parameters for the function

        Returns:
            dict: Result with status and message
        """
        if not self.controller:
            return {"status": "error", "message": "No device controller available"}

        # Get the function name from params
        function_name = params.get('function_name')
        if not function_name:
            return {"status": "error", "message": "No function name provided"}

        self.logger.info(f"Executing iOS function: {function_name}")

        # Check if we have an Airtest device available
        if not hasattr(self.controller, 'airtest_device'):
            return {"status": "error", "message": "No Airtest device available. Make sure you're connected to an iOS device."}

        # Get the Airtest device
        airtest_device = self.controller.airtest_device

        try:
            # Execute the function based on the function name
            if function_name == 'home':
                # Press the home button
                airtest_device.home()
                return {"status": "success", "message": "Pressed home button"}

            elif function_name == 'lock':
                # Lock the device
                airtest_device.lock()
                return {"status": "success", "message": "Locked the device"}

            elif function_name == 'unlock':
                # Unlock the device
                airtest_device.unlock()
                return {"status": "success", "message": "Unlocked the device"}



            elif function_name == 'alert_accept':
                # Accept an alert
                airtest_device.alert_accept()
                return {"status": "success", "message": "Accepted alert"}

            elif function_name == 'alert_dismiss':
                # Dismiss an alert
                airtest_device.alert_dismiss()
                return {"status": "success", "message": "Dismissed alert"}

            elif function_name == 'alert_click':
                # Click a specific button in an alert
                button_text = params.get('button_text', '')
                if not button_text:
                    return {"status": "error", "message": "No button text provided for alert_click"}

                airtest_device.alert_click(button_text)
                return {"status": "success", "message": f"Clicked '{button_text}' button in alert"}

            elif function_name == 'alert_wait':
                # Wait for an alert to appear
                timeout = params.get('timeout', 2)
                result = airtest_device.alert_wait(timeout)
                if result:
                    return {"status": "success", "message": f"Alert appeared within {timeout} seconds"}
                else:
                    return {"status": "warning", "message": f"No alert appeared within {timeout} seconds"}



            elif function_name == 'alert_exists':
                # Check if an alert exists
                exists = airtest_device.alert_exists()
                if exists:
                    return {"status": "success", "message": "Alert exists"}
                else:
                    return {"status": "warning", "message": "No alert exists"}

            elif function_name == 'get_clipboard':
                # Get clipboard content
                wda_bundle_id = params.get('wda_bundle_id', None)
                content = airtest_device.get_clipboard(wda_bundle_id)
                return {"status": "success", "message": f"Clipboard content: {content}", "content": content}

            elif function_name == 'set_clipboard':
                # Set clipboard content
                content = params.get('content', '')
                wda_bundle_id = params.get('wda_bundle_id', None)
                airtest_device.set_clipboard(content, wda_bundle_id)
                return {"status": "success", "message": f"Set clipboard content to: {content}"}

            elif function_name == 'paste':
                # Paste clipboard content
                wda_bundle_id = params.get('wda_bundle_id', None)
                airtest_device.paste(wda_bundle_id)
                return {"status": "success", "message": "Pasted clipboard content"}

            elif function_name == 'text':
                # Input text
                text_content = params.get('text', '')
                enter = params.get('enter', True)

                if not text_content:
                    return {"status": "error", "message": "No text content provided for text function"}

                # Try using Appium driver first if available
                if hasattr(self.controller, 'driver') and self.controller.driver:
                    # Method 1: Use XCUITest's typeText method via mobile: commands
                    try:
                        self.logger.info(f"Method 1: Using XCUITest mobile:typeText to input text: '{text_content}'")

                        # Use the mobile: typeText command which is the most reliable for iOS
                        self.controller.driver.execute_script('mobile: typeText', {'text': text_content})

                        # Press Enter if requested
                        if enter:
                            try:
                                # Try to find and click the Return key
                                return_key = self.controller.driver.find_element(by='name', value='Return')
                                return_key.click()
                            except Exception as return_e:
                                self.logger.warning(f"Could not find Return key: {return_e}")
                                # Try sending a newline character
                                self.controller.driver.execute_script('mobile: typeText', {'text': "\n"})

                        return {"status": "success", "message": f"Input text using XCUITest mobile:typeText: {text_content}"}
                    except Exception as e1:
                        self.logger.warning(f"Method 1 failed to input text using XCUITest mobile:typeText: {e1}")

                        # Method 2: Try using active element
                        try:
                            self.logger.info(f"Method 2: Using active element to input text: '{text_content}'")

                            # Get the active element
                            active_element = self.controller.driver.switch_to.active_element

                            # Send keys to the active element
                            active_element.send_keys(text_content)

                            # Press Enter if requested
                            if enter:
                                active_element.send_keys("\n")

                            return {"status": "success", "message": f"Input text using active element: {text_content}"}
                        except Exception as e2:
                            self.logger.warning(f"Method 2 failed to input text using active element: {e2}")

                            # Method 3: Try using mobile: type (older method)
                            try:
                                self.logger.info(f"Method 3: Using mobile: type to input text: '{text_content}'")

                                # Use mobile: type command for iOS (older method)
                                self.controller.driver.execute_script('mobile: type', {'text': text_content})

                                # Press Enter if requested
                                if enter:
                                    self.controller.driver.execute_script('mobile: type', {'text': "\n"})

                                return {"status": "success", "message": f"Input text using mobile: type: {text_content}"}
                            except Exception as e3:
                                self.logger.warning(f"Method 3 failed to input text using mobile: type: {e3}")

                                # Method 4: Try using keyboard element
                                try:
                                    self.logger.info(f"Method 4: Using keyboard element to input text: '{text_content}'")

                                    # Find keyboard element
                                    keyboard = self.controller.driver.find_element(by='class name', value='XCUIElementTypeKeyboard')

                                    # Type text character by character
                                    for char in text_content:
                                        try:
                                            key = self.controller.driver.find_element(by='name', value=char)
                                            key.click()
                                        except Exception as key_e:
                                            self.logger.warning(f"Could not find key for character: {char}")

                                    # Press Enter if requested
                                    if enter:
                                        try:
                                            return_key = self.controller.driver.find_element(by='name', value='Return')
                                            return_key.click()
                                        except Exception as return_e:
                                            self.logger.warning(f"Could not find Return key: {return_e}")

                                    return {"status": "success", "message": f"Input text using keyboard element: {text_content}"}
                                except Exception as e4:
                                    self.logger.warning(f"Method 4 failed to input text using keyboard element: {e4}")
                                    # Fall back to Airtest method

                # Fall back to Airtest method
                try:
                    result = airtest_device.text(text_content, enter=enter)
                    if result:
                        return {"status": "success", "message": f"Input text using Airtest: {text_content}"}
                    else:
                        return {"status": "error", "message": f"Failed to input text: {text_content}"}
                except Exception as e:
                    self.logger.error(f"Failed to input text using both methods: {e}")
                    return {"status": "error", "message": f"Failed to input text: {str(e)}"}











            elif function_name == 'list_app':
                # List installed apps
                apps = airtest_device.list_app()

                if apps:
                    app_list = [f"{app['name']} ({app['bundle_id']})" for app in apps]
                    return {"status": "success", "message": f"Found {len(apps)} installed apps: {', '.join(app_list)}", "apps": apps}
                else:
                    return {"status": "warning", "message": "No installed apps found"}

            else:
                return {"status": "error", "message": f"Unsupported iOS function: {function_name}"}

        except Exception as e:
            self.logger.error(f"Error executing iOS function {function_name}: {e}")
            self.logger.error(traceback.format_exc())
            return {"status": "error", "message": f"iOS function execution failed: {str(e)}"}
