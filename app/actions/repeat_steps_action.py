from .base_action import BaseAction
import logging
import traceback
import time
import json
import os

class RepeatStepsAction(BaseAction):
    """Handler for repeating a set of steps multiple times"""

    def execute(self, params):
        """
        Execute a test case multiple times

        Args:
            params: Dictionary containing:
                - repeat_count: Number of times to repeat the steps
                - test_case_id: ID of the test case to repeat
                - test_case_steps: Steps of the test case to repeat
                - repeat_delay: Delay between repetitions in seconds

        Returns:
            dict: Result with status and message
        """
        if not self.controller:
            return {"status": "error", "message": "No device controller available"}

        # Get parameters
        repeat_count = params.get('repeat_count', 1)
        test_case_id = params.get('test_case_id')
        test_case_steps = params.get('test_case_steps', [])
        repeat_delay = params.get('repeat_delay', 0)

        # Validate parameters
        try:
            repeat_count = int(repeat_count)
            if repeat_count < 1 or repeat_count > 100:
                return {"status": "error", "message": "Repeat count must be between 1 and 100"}
        except (ValueError, TypeError):
            return {"status": "error", "message": "Invalid repeat count"}

        try:
            repeat_delay = float(repeat_delay)
            if repeat_delay < 0:
                repeat_delay = 0
        except (ValueError, TypeError):
            repeat_delay = 0

        if not test_case_id:
            return {"status": "error", "message": "No test case ID provided"}

        if not test_case_steps:
            return {"status": "error", "message": "No test case steps provided"}

        self.logger.info(f"Starting repeat steps action: {repeat_count} repetitions of test case {test_case_id}")

        # Import action factory here to avoid circular imports
        try:
            from .action_factory import ActionFactory
        except ImportError:
            try:
                from action_factory import ActionFactory
            except ImportError:
                try:
                    from app.actions.action_factory import ActionFactory
                except ImportError as e:
                    self.logger.error(f"Failed to import ActionFactory: {e}")
                    return {"status": "error", "message": "Failed to initialize action factory"}

        action_factory = ActionFactory(self.controller)

        results = []
        success_count = 0
        total_steps_executed = 0

        for repetition in range(repeat_count):
            self.logger.info(f"Starting repetition {repetition + 1} of {repeat_count}")
            
            repetition_results = []
            repetition_success = True
            
            # Execute each step in the test case
            for step_index, step in enumerate(test_case_steps):
                try:
                    # Get action type
                    action_type = step.get('type') or step.get('action_type')
                    if not action_type:
                        self.logger.warning(f"Step {step_index + 1} in repetition {repetition + 1} has no action type")
                        continue

                    self.logger.info(f"Executing step {step_index + 1} in repetition {repetition + 1}: {action_type}")

                    # Skip hook actions during normal execution
                    if action_type == 'hookAction' or step.get('type') == 'hookAction':
                        self.logger.info(f"Skipping hook action in repetition {repetition + 1}, step {step_index + 1}")
                        repetition_results.append({
                            "status": "success",
                            "message": "Hook Action skipped",
                            "step_index": step_index,
                            "repetition": repetition + 1
                        })
                        continue

                    # Execute the action
                    result = action_factory.execute_action(action_type, step)
                    total_steps_executed += 1

                    # Add metadata to result
                    if isinstance(result, dict):
                        result['step_index'] = step_index
                        result['repetition'] = repetition + 1
                        repetition_results.append(result)

                        # Check if step failed
                        if result.get('status') != 'success':
                            repetition_success = False
                            self.logger.error(f"Step {step_index + 1} failed in repetition {repetition + 1}: {result.get('message', 'Unknown error')}")
                            break  # Stop this repetition on failure
                    else:
                        self.logger.warning(f"Step {step_index + 1} in repetition {repetition + 1} returned non-dict result: {result}")
                        repetition_results.append({
                            "status": "warning",
                            "message": f"Step returned non-dict result: {result}",
                            "step_index": step_index,
                            "repetition": repetition + 1
                        })

                except Exception as e:
                    self.logger.error(f"Error executing step {step_index + 1} in repetition {repetition + 1}: {e}")
                    self.logger.error(traceback.format_exc())
                    repetition_results.append({
                        "status": "error",
                        "message": f"Step execution failed: {str(e)}",
                        "step_index": step_index,
                        "repetition": repetition + 1
                    })
                    repetition_success = False
                    break  # Stop this repetition on error

            # Add repetition results to overall results
            results.extend(repetition_results)
            
            if repetition_success:
                success_count += 1
                self.logger.info(f"Repetition {repetition + 1} completed successfully")
            else:
                self.logger.error(f"Repetition {repetition + 1} failed")

            # Add delay between repetitions (except after the last one)
            if repeat_delay > 0 and repetition < repeat_count - 1:
                self.logger.info(f"Waiting {repeat_delay} seconds before next repetition")
                time.sleep(repeat_delay)

        # Prepare final result
        if success_count == repeat_count:
            status = "success"
            message = f"All {repeat_count} repetitions completed successfully. Total steps executed: {total_steps_executed}"
        elif success_count > 0:
            status = "partial_success"
            message = f"{success_count} of {repeat_count} repetitions completed successfully. Total steps executed: {total_steps_executed}"
        else:
            status = "error"
            message = f"All {repeat_count} repetitions failed. Total steps executed: {total_steps_executed}"

        self.logger.info(f"Repeat steps action completed: {message}")

        return {
            "status": status,
            "message": message,
            "repetitions_completed": success_count,
            "total_repetitions": repeat_count,
            "total_steps_executed": total_steps_executed,
            "detailed_results": results
        }
