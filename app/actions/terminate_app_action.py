from .base_action import BaseAction
import logging

class TerminateAppAction(BaseAction):
    """Handler for terminating an app"""

    def execute(self, params):
        """
        Terminate an app

        Args:
            params: Dictionary containing:
                - package_id: ID of the app to terminate

        Returns:
            dict: Result with status and message
        """
        self.logger.info(f"[HOOK_DEBUG] TerminateAppAction.execute called with params: {params}")
        
        if not self.controller:
            self.logger.error("[HOOK_DEBUG] No device controller available")
            return {"status": "error", "message": "No device controller available"}

        # Get the package ID from params
        package_id = params.get('package_id')
        
        if not package_id:
            self.logger.error("[HOOK_DEBUG] No package ID provided")
            return {"status": "error", "message": "No package ID provided"}

        self.logger.info(f"Terminating app: {package_id}")
        
        try:
            # Use the device controller to terminate the app
            result = self.controller.terminate_app(package_id)
            
            if result:
                return {"status": "success", "message": f"App {package_id} terminated successfully"}
            else:
                return {"status": "error", "message": f"Failed to terminate app {package_id}"}
        except Exception as e:
            self.logger.error(f"Error terminating app {package_id}: {str(e)}")
            return {"status": "error", "message": f"Error terminating app: {str(e)}"}
