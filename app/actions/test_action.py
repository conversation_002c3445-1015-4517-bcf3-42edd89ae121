import logging

class TestAction:
    def __init__(self, controller):
        self.controller = controller
        self.logger = logging.getLogger(__name__)
    
    def set_controller(self, controller):
        self.controller = controller
    
    def execute(self, params):
        """Simple test action that always succeeds"""
        self.logger.info("Test action executed successfully")
        return {"status": "success", "message": "Test action completed"}
