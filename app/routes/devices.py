"""
Device management routes
"""
import os
import json
import subprocess
import sys
from pathlib import Path

# Add the app directory to the path
app_dir = str(Path(__file__).resolve().parent.parent)
if app_dir not in sys.path:
    sys.path.insert(0, app_dir)

from flask import Blueprint, jsonify, request
from utils.appium_device_controller import AppiumDeviceController

devices_bp = Blueprint('devices', __name__, url_prefix='/api/devices')

# Device controller will be initialized lazily
device_controller = None

def get_device_controller():
    """Get or create device controller with configured ports"""
    global device_controller
    if device_controller is None:
        try:
            # Add parent directory to path to import config
            parent_dir = str(Path(__file__).resolve().parent.parent.parent)
            if parent_dir not in sys.path:
                sys.path.insert(0, parent_dir)
            import config
            appium_port = getattr(config, 'APPIUM_PORT', 4723)
            wda_port = getattr(config, 'WDA_PORT', 8100)
            device_controller = AppiumDeviceController(appium_port=appium_port, wda_port=wda_port)
        except ImportError:
            device_controller = AppiumDeviceController()
    return device_controller

@devices_bp.route('', methods=['GET'])
def get_devices():
    """Get all connected devices"""
    try:
        # Get iOS devices using idevice_id
        devices = []
        try:
            idevice_output = subprocess.run(
                ['idevice_id', '-l'],
                capture_output=True,
                text=True,
                check=True
            ).stdout

            # Parse idevice_id output
            for line in idevice_output.strip().split('\n'):
                if line.strip():
                    device_id = line.strip()

                    # Get device name
                    try:
                        device_name = subprocess.run(
                            ['idevicename', '-u', device_id],
                            capture_output=True,
                            text=True
                        ).stdout.strip()

                        # Get iOS version
                        ios_version = subprocess.run(
                            ['ideviceinfo', '-u', device_id, '-k', 'ProductVersion'],
                            capture_output=True,
                            text=True
                        ).stdout.strip()

                        # Get device model
                        device_model = subprocess.run(
                            ['ideviceinfo', '-u', device_id, '-k', 'ProductType'],
                            capture_output=True,
                            text=True
                        ).stdout.strip()

                        devices.append({
                            'id': device_id,
                            'udid': device_id,
                            'name': device_name or f"iOS Device ({device_id})",
                            'platform': 'iOS',
                            'osVersion': ios_version,
                            'model': device_model,
                            'status': 'Online',
                            'type': 'real',
                            'wda_url': f'http://localhost:{get_device_controller().wda_port}'  # Configured WebDriverAgent URL
                        })
                    except Exception as e:
                        # Add with limited info
                        devices.append({
                            'id': device_id,
                            'udid': device_id,
                            'name': f"iOS Device ({device_id})",
                            'platform': 'iOS',
                            'osVersion': 'Unknown',
                            'model': 'Unknown',
                            'status': 'Online',
                            'type': 'real',
                            'wda_url': f'http://localhost:{get_device_controller().wda_port}'  # Configured WebDriverAgent URL
                        })
        except Exception as e:
            # Log the error but continue
            print(f"Error getting iOS devices: {e}")

        return jsonify({"devices": devices})
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@devices_bp.route('/<device_id>', methods=['GET'])
def get_device(device_id):
    """Get a specific device by ID"""
    try:
        # Try to get device info
        device_info = {
            'id': device_id,
            'udid': device_id,
            'platform': 'iOS',  # Assuming iOS device
            'status': 'Unknown'
        }
        
        try:
            # Get device name
            device_name = subprocess.run(
                ['idevicename', '-u', device_id],
                capture_output=True,
                text=True
            ).stdout.strip()
            device_info['name'] = device_name or f"iOS Device ({device_id})"
            
            # Get iOS version
            ios_version = subprocess.run(
                ['ideviceinfo', '-u', device_id, '-k', 'ProductVersion'],
                capture_output=True,
                text=True
            ).stdout.strip()
            device_info['osVersion'] = ios_version
            
            # Get device model
            device_model = subprocess.run(
                ['ideviceinfo', '-u', device_id, '-k', 'ProductType'],
                capture_output=True,
                text=True
            ).stdout.strip()
            device_info['model'] = device_model
            
            # Check if device is online
            device_info['status'] = 'Online'
        except Exception as e:
            print(f"Error getting detailed info for iOS device {device_id}: {e}")
        
        return jsonify(device_info)
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@devices_bp.route('/scan', methods=['POST'])
def scan_devices():
    """Scan for available devices"""
    try:
        # Trigger a rescan of devices
        return get_devices()
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@devices_bp.route('/connect', methods=['POST'])
def connect_device():
    """Connect to a device"""
    try:
        device_info = request.json
        # In a real implementation, you would connect to the device here
        return jsonify({"success": True, "message": "Device connected successfully"})
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@devices_bp.route('/<device_id>/disconnect', methods=['POST'])
def disconnect_device(device_id):
    """Disconnect a device"""
    try:
        # In a real implementation, you would disconnect the device here
        return jsonify({"success": True, "message": f"Device {device_id} disconnected successfully"})
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@devices_bp.route('/<device_id>/restart', methods=['POST'])
def restart_device(device_id):
    """Restart a device"""
    try:
        # In a real implementation, you would restart the device here
        return jsonify({"success": True, "message": f"Device {device_id} restarted successfully"})
    except Exception as e:
        return jsonify({"error": str(e)}), 500 