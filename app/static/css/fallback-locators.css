/**
 * Styles for fallback locators
 */

.fallback-locators-container {
    border: 1px solid #e9ecef;
    border-radius: 0.25rem;
    padding: 1rem;
    margin-bottom: 1rem;
    background-color: #f8f9fa;
    display: block !important; /* Force display */
    min-height: 100px; /* Ensure minimum height */
    position: relative; /* For absolute positioning of empty state */
}

.fallback-locator {
    background-color: #fff;
    border: 1px solid #dee2e6 !important;
    border-left: 4px solid #0d6efd !important;
    border-radius: 0.25rem;
    transition: all 0.3s ease;
}

.fallback-locator:hover {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

@keyframes highlight-new-element {
    0% {
        background-color: rgba(13, 110, 253, 0.2);
        transform: translateY(10px);
        opacity: 0.7;
    }
    100% {
        background-color: #fff;
        transform: translateY(0);
        opacity: 1;
    }
}

.highlight-animation {
    animation: highlight-new-element 1s ease-out;
}

/* Styles for different fallback types */
.fallback-locator[data-type="coordinates"] {
    border-left-color: #fd7e14 !important;
}

.fallback-locator[data-type="image"] {
    border-left-color: #20c997 !important;
}

.fallback-locator[data-type="text_on_screen"] {
    border-left-color: #6f42c1 !important;
}

/* Empty state message */
.fallback-empty-state {
    text-align: center;
    padding: 2rem;
    color: #6c757d;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
}

.fallback-empty-state i {
    font-size: 2rem;
    margin-bottom: 1rem;
    display: block;
}
