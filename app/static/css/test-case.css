/* Test case success/failure indicators */
.test-case-container {
    margin-bottom: 15px;
    border-radius: 4px;
    overflow: hidden;
}

.test-case-header.success {
    background-color: rgba(40, 167, 69, 0.1) !important;
    border-left: 4px solid #28a745;
}

.test-case-header.error {
    background-color: rgba(220, 53, 69, 0.1) !important;
    border-left: 4px solid #dc3545;
}

.test-case-header.success .collapse-icon,
.test-case-header.success h6 {
    color: #28a745;
}

.test-case-header.error .collapse-icon,
.test-case-header.error h6 {
    color: #dc3545;
}

/* Add hover states for success/error test case headers */
.test-case-header.success:hover {
    background-color: rgba(40, 167, 69, 0.2) !important;
}

.test-case-header.error:hover {
    background-color: rgba(220, 53, 69, 0.2) !important;
}

/* Make sure test case headers remain visible */
.test-case-header {
    display: flex !important;
    background-color: #f8f9fa;
    cursor: pointer;
    transition: all 0.2s ease;
    border-bottom: 1px solid #dee2e6;
    padding: 10px 15px; /* More compact padding */
    align-items: center;
    justify-content: space-between;
}

.test-case-header:hover {
    background-color: #e9ecef;
}

.test-case-header h6 {
    margin: 0;
    color: #495057;
    font-weight: 600;
    font-size: 0.95rem; /* Slightly smaller font */
}

.test-case-header .collapse-icon {
    color: #6c757d;
    transition: transform 0.2s ease;
    font-size: 0.9rem;
}

/* Test case header left section */
.test-case-header .d-flex:first-child {
    align-items: center;
    flex-grow: 1;
}

/* Test case header right section */
.test-case-header .d-flex:last-child {
    align-items: center;
    gap: 8px;
}

/* Compact button styling in headers */
.test-case-header .btn {
    padding: 4px 8px !important;
    font-size: 0.8rem;
    border-radius: 4px;
}

/* Badge styling in headers */
.test-case-header .badge {
    font-size: 0.75rem;
    padding: 4px 8px;
}

/* Labels styling */
.test-case-header .badge.bg-secondary {
    background-color: #6c757d !important;
    font-size: 0.7rem;
    padding: 2px 6px;
    margin-left: 4px;
}

/* Action item status styles */
.action-item {
    position: relative;
    transition: all 0.2s ease;
    padding: 6px 12px !important;
    border: none !important;
    border-bottom: 1px solid #f1f1f1 !important;
    margin-bottom: 0 !important;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.action-item:last-child {
    border-bottom: none !important;
}

.action-item.executing {
    background-color: #fff3cd;
    border-left: 4px solid #ffc107 !important;
}

.action-item.success {
    background-color: #d4edda;
    border-left: 4px solid #28a745 !important;
}

.action-item.error {
    background-color: #f8d7da;
    border-left: 4px solid #dc3545 !important;
}

/* Action content improvements */
.action-item .action-content {
    display: flex;
    align-items: center;
    flex-grow: 1;
    font-size: 0.9rem;
    gap: 8px;
}

.action-item .action-content .badge {
    font-size: 0.75rem;
    padding: 2px 6px;
}

.action-item .action-content .step-number {
    min-width: 24px;
    text-align: center;
    font-size: 0.75rem;
}

/* Action buttons improvements */
.action-item .action-buttons {
    display: flex;
    gap: 4px;
    align-items: center;
}

.action-item .action-buttons .btn {
    padding: 3px 6px !important;
    font-size: 0.75rem;
    line-height: 1.2;
}

/* Drag indicator styling */
.action-item .drag-indicator {
    color: #adb5bd;
    cursor: grab;
    font-size: 0.8rem;
}

.action-item:hover .drag-indicator {
    color: #6c757d;
}

.action-status {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    margin-right: 10px;
    border-radius: 50%;
}

.action-status i {
    font-size: 16px;
}

.action-item.success .action-status i {
    color: #28a745;
}

.action-item.error .action-status i {
    color: #dc3545;
}

/* Current test case highlight */
.test-case-container.current-test-case {
    border: 2px solid #007bff;
    box-shadow: 0 0 10px rgba(0, 123, 255, 0.3);
    position: relative;
}

.test-case-container.current-test-case::before {
    content: "Current Test Case";
    position: absolute;
    top: -10px;
    right: 10px;
    background-color: #007bff;
    color: white;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
    z-index: 1;
}

/* Executing action highlight */
.action-item.executing-highlight {
    background-color: #cce5ff !important;
    border-left: 4px solid #007bff !important;
    box-shadow: 0 0 5px rgba(0, 123, 255, 0.5);
    animation: pulse-blue 1.5s infinite;
}

@keyframes pulse-blue {
    0% {
        background-color: #cce5ff;
    }
    50% {
        background-color: #e6f2ff;
    }
    100% {
        background-color: #cce5ff;
    }
}