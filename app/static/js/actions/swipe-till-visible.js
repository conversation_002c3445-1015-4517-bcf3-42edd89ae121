// swipe-till-visible.js - UI components and handlers for the Swipe Till Visible action

/**
 * Initializes event listeners for the Swipe Till Visible action
 * This is the main entry point for this module
 */
function initSwipeTillVisibleAction() {
    console.log('Initializing SwipeTillVisible action handlers');

    document.addEventListener('DOMContentLoaded', function() {
        // Initialize event listeners when DOM is ready
        setupSwipeTillVisibleEventListeners();
    });
}

/**
 * Set up all event listeners for the Swipe Till Visible action form
 */
function setupSwipeTillVisibleEventListeners() {
    // Direction selector
    const directionSelector = document.getElementById('swipeTillVisibleDirection');
    if (directionSelector) {
        directionSelector.addEventListener('change', function() {
            updateSwipeTillVisibleCoordinates(this.value);
        });

        // Initialize with default direction
        updateSwipeTillVisibleCoordinates(directionSelector.value || 'up');
    }

    // Locator type selector
    const locatorTypeSelector = document.getElementById('swipeTillVisibleLocatorType');
    if (locatorTypeSelector) {
        locatorTypeSelector.addEventListener('change', function() {
            const isText = this.value === 'text';
            const valueDiv = document.getElementById('swipeTillVisibleLocatorValueDiv');
            const textDiv = document.getElementById('swipeTillVisibleTextDiv');

            if (valueDiv) valueDiv.style.display = isText ? 'none' : 'block';
            if (textDiv) textDiv.style.display = isText ? 'block' : 'none';
        });
    }

    // Load reference images on page load
    if (window.app && typeof window.app.loadReferenceImages === 'function') {
        window.app.loadReferenceImages('swipeTillVisible');
    }

    // Refresh images button
    const refreshImagesBtn = document.getElementById('refreshSwipeTillVisibleImages');
    if (refreshImagesBtn) {
        refreshImagesBtn.addEventListener('click', function() {
            if (window.app && typeof window.app.loadReferenceImages === 'function') {
                window.app.loadReferenceImages('swipeTillVisible');
            }
        });
    }

    // Initialize slider value displays
    initSliderValueDisplays();

    console.log('SwipeTillVisible event listeners set up successfully');
}

/**
 * Update swipe coordinates based on selected direction
 * @param {string} direction - The swipe direction (up, down, left, right, custom)
 */
function updateSwipeTillVisibleCoordinates(direction) {
    console.log('Updating swipe coordinates for direction:', direction);

    // Get slider elements
    const startX = document.getElementById('swipeTillVisibleStartX');
    const startY = document.getElementById('swipeTillVisibleStartY');
    const endX = document.getElementById('swipeTillVisibleEndX');
    const endY = document.getElementById('swipeTillVisibleEndY');

    if (!startX || !startY || !endX || !endY) {
        console.error('Some slider elements not found');
        return;
    }

    // Set values based on direction
    switch (direction) {
        case 'up':
            startX.value = 50;
            startY.value = 70;
            endX.value = 50;
            endY.value = 30;
            break;
        case 'down':
            startX.value = 50;
            startY.value = 30;
            endX.value = 50;
            endY.value = 70;
            break;
        case 'left':
            startX.value = 70;
            startY.value = 50;
            endX.value = 30;
            endY.value = 50;
            break;
        case 'right':
            startX.value = 30;
            startY.value = 50;
            endX.value = 70;
            endY.value = 50;
            break;
        case 'custom':
            // Leave values as they are
            break;
    }

    // Update displayed values
    updateSliderDisplayValue('swipeTillVisibleStartX');
    updateSliderDisplayValue('swipeTillVisibleStartY');
    updateSliderDisplayValue('swipeTillVisibleEndX');
    updateSliderDisplayValue('swipeTillVisibleEndY');
}

/**
 * Initialize display values for all sliders
 */
function initSliderValueDisplays() {
    // Set up listeners for all range sliders
    [
        'swipeTillVisibleStartX',
        'swipeTillVisibleStartY',
        'swipeTillVisibleEndX',
        'swipeTillVisibleEndY'
    ].forEach(id => {
        const slider = document.getElementById(id);
        if (slider) {
            // Update on input change
            slider.addEventListener('input', function() {
                updateSliderDisplayValue(id);
            });

            // Initial update
            updateSliderDisplayValue(id);
        }
    });
}

/**
 * Update the display value for a specific slider
 * @param {string} sliderId - The ID of the slider element
 */
function updateSliderDisplayValue(sliderId) {
    const slider = document.getElementById(sliderId);
    const valueDisplay = document.getElementById(`${sliderId}Value`);

    if (slider && valueDisplay) {
        valueDisplay.textContent = slider.value;
    }
}

// Initialize the SwipeTillVisible action
initSwipeTillVisibleAction();