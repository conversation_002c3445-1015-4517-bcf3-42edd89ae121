// ElementInteractions.js - Module for handling element interactions

class ElementInteractions {
    constructor(app) {
        this.app = app;
        this.currentInspectedElement = null;
    }



    async inspectElement(x, y) {
        try {
            if (!this.app.isConnected) {
                this.app.logAction('error', 'Cannot inspect elements - no device connected');
                return;
            }

            // Show loading
            this.app.showLoading('Analyzing UI element...');

            // --- REMOVED Faulty Coordinate Scaling Logic ---
            // The coordinates (x, y) received are already relative to the original screenshot pixels.
            // The backend will handle converting these to logical points if necessary (e.g., for iOS scaling).
            const finalX = Math.round(x);
            const finalY = Math.round(y);
            // --- End REMOVED ---

            this.app.logAction('info', `Inspecting element at screenshot pixel coordinates (${finalX}, ${finalY})...`);

            // Get elements at the original screenshot pixel position using the API endpoint
            // Backend will handle scaling if needed.
            const result = await this.app.fetchApi(`element/at_position`, 'POST', { x: finalX, y: finalY });

            // Hide loading
            this.app.hideLoading();

            // Log the result for debugging
            console.log("Element inspection result:", result);

            // Get the element inspector modal element
            const elementInspectorModal = document.getElementById('elementInspectorModal');

            // Make sure Bootstrap is loaded before initializing the modal
            if (typeof bootstrap === 'undefined') {
                console.error("Bootstrap is not defined - loading from CDN");
                this.app.logAction('warning', 'Loading Bootstrap library...');

                // Create a script element to load Bootstrap
                const script = document.createElement('script');
                script.src = 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js';
                script.onload = () => {
                    console.log("Bootstrap loaded dynamically");
                    // Now initialize the modal
                    this.showElementInspectorModal(elementInspectorModal, result, finalX, finalY);
                };
                script.onerror = () => {
                    this.app.logAction('error', 'Failed to load Bootstrap library');
                };
                document.head.appendChild(script);
            } else {
                // Bootstrap is already loaded, initialize the modal directly
                // Pass original pixel coords to modal function for consistency in logging/display if needed
                this.showElementInspectorModal(elementInspectorModal, result, finalX, finalY);
            }
        } catch (error) {
            this.app.hideLoading();
            console.error("Inspection error:", error);
            this.app.logAction('error', `Element inspection error: ${error.message}`);
        }
    }

    async showElementInspectorModal(modalElement, result, x, y) {
        try {
            // Initialize the Bootstrap modal - ALWAYS create new instance
            let modal;
            try {
                // Ensure any previous instance is disposed if it exists
                const existingInstance = bootstrap.Modal.getInstance(modalElement);
                if (existingInstance) {
                    existingInstance.dispose();
                }
                modal = new bootstrap.Modal(modalElement, {
                    backdrop: true,
                    keyboard: true,
                    focus: false  // Disable automatic focus handling
                });
            } catch (e) {
                console.error("Error initializing Bootstrap modal for elementInspectorModal:", e);
                this.app.logAction('error', 'Failed to initialize element inspector.');
                return;
            }

            if (result.success) {
                // Log using the original pixel coordinates received by this function
                this.app.logAction('success', `Retrieved UI element near pixel coordinates (${x}, ${y})`);
                this.populateElementInspector(result); // Pass the whole result which contains element data

                // Reset App Source and Session Info tabs to loading state initially
                const appSourceContainer = document.getElementById('appSourceContainer');
                const sessionIdDisplay = document.getElementById('sessionId');
                const sessionCapabilitiesDisplay = document.getElementById('sessionCapabilities');

                if (appSourceContainer) appSourceContainer.innerHTML = '<div class="text-center p-3"><span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Loading app source...</div>';
                if (sessionIdDisplay) sessionIdDisplay.textContent = 'Loading...';
                if (sessionCapabilitiesDisplay) sessionCapabilitiesDisplay.innerHTML = '<div class="text-center p-3"><span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Loading capabilities...</div>';

                // Add event listener for when modal is shown
                modalElement.addEventListener('shown.bs.modal', async () => {
                    // --- Load App Source ---
                    try {
                        console.log("Modal shown, loading app source...");
                        const source = await this.app.loadAppSource(); // Await the result
                        console.log("App source loaded, data received:", source ? "Data received" : "No data", source ? `(length: ${source.length})` : "");

                        // Look for the correct container element
                        const appSourceContainer = document.getElementById('appSourceContainer');
                        console.log("App source container element found:", appSourceContainer ? "Yes" : "No");

                        if (appSourceContainer) {
                            if (source) {
                                // Use <pre> and <code> for formatted XML, escape it first
                                console.log("Preparing to render app source");
                                const escapedSource = this.app.escapedXml(source);
                                console.log(`Escaped source (showing first 100 chars): ${escapedSource.substring(0, 100)}...`);
                                appSourceContainer.innerHTML = `<pre><code class="language-xml">${escapedSource}</code></pre>`;
                                console.log("App source rendering complete");
                            } else {
                                console.log("No source data returned from loadAppSource");
                                appSourceContainer.innerHTML = '<div class="alert alert-warning"><i class="bi bi-exclamation-triangle-fill me-2"></i>App source not available.</div>';
                            }
                        } else {
                            console.error("Could not find appSourceContainer element to update");
                        }
                    } catch (error) {
                        console.error("Error loading app source:", error);
                        const appSourceContainer = document.getElementById('appSourceContainer');
                        if (appSourceContainer) appSourceContainer.innerHTML = `<div class="alert alert-danger"><i class="bi bi-x-circle-fill me-2"></i>Error loading app source: ${error.message}</div>`;
                    }

                    // --- Load Session Info ---
                    try {
                        console.log("Modal shown, loading session info...");
                        const sessionInfo = await this.app.loadSessionInfo(); // Await the result
                        console.log("Session info loaded:", sessionInfo);

                        if (sessionIdDisplay) {
                            if (sessionInfo && sessionInfo.id) {
                                sessionIdDisplay.textContent = sessionInfo.id;
                            } else {
                                sessionIdDisplay.innerHTML = '<span class="text-muted">Not Available</span>';
                            }
                        }

                        if (sessionCapabilitiesDisplay) {
                            if (sessionInfo && sessionInfo.capabilities && Object.keys(sessionInfo.capabilities).length > 0) {
                                let capabilitiesHtml = '<table class="table table-sm table-striped table-bordered">';
                                capabilitiesHtml += '<thead><tr><th>Capability</th><th>Value</th></tr></thead><tbody>';

                                // Sort capabilities alphabetically for better readability
                                const sortedCapabilities = Object.keys(sessionInfo.capabilities).sort();

                                for (const key of sortedCapabilities) {
                                    const value = sessionInfo.capabilities[key];
                                    let displayValue;

                                    if (typeof value === 'object' && value !== null) {
                                        // Format objects nicely
                                        displayValue = JSON.stringify(value, null, 2);
                                    } else {
                                        displayValue = String(value);
                                    }

                                    capabilitiesHtml += `<tr>
                                        <td class="font-monospace text-nowrap">${this.app.escapeHtml(key)}</td>
                                        <td class="font-monospace">${this.app.escapeHtml(displayValue)}</td>
                                    </tr>`;
                                }

                                capabilitiesHtml += '</tbody></table>';

                                // Check for additional capabilities sections to display
                                if (sessionInfo.detailed_capabilities) {
                                    capabilitiesHtml += `<h6 class="mt-4 mb-3">Detailed Capabilities</h6>
                                    <div class="alert alert-info">
                                        <pre class="mb-0">${this.app.escapeHtml(JSON.stringify(sessionInfo.detailed_capabilities, null, 2))}</pre>
                                    </div>`;
                                }

                                // Display device info if available
                                if (sessionInfo.device_info) {
                                    capabilitiesHtml += `<h6 class="mt-4 mb-3">Device Information</h6>
                                    <table class="table table-sm table-striped table-bordered">
                                    <thead><tr><th>Property</th><th>Value</th></tr></thead><tbody>`;

                                    const deviceInfo = sessionInfo.device_info;
                                    const sortedDeviceProps = Object.keys(deviceInfo).sort();

                                    for (const key of sortedDeviceProps) {
                                        capabilitiesHtml += `<tr>
                                            <td class="font-monospace">${this.app.escapeHtml(key)}</td>
                                            <td class="font-monospace">${this.app.escapeHtml(String(deviceInfo[key]))}</td>
                                        </tr>`;
                                    }

                                    capabilitiesHtml += '</tbody></table>';
                                }

                                // Display active app info if available
                                if (sessionInfo.active_app) {
                                    capabilitiesHtml += `<h6 class="mt-4 mb-3">Active Application</h6>
                                    <table class="table table-sm table-striped table-bordered">
                                    <thead><tr><th>Property</th><th>Value</th></tr></thead><tbody>`;

                                    const appInfo = sessionInfo.active_app;
                                    const sortedAppProps = Object.keys(appInfo).sort();

                                    for (const key of sortedAppProps) {
                                        capabilitiesHtml += `<tr>
                                            <td class="font-monospace">${this.app.escapeHtml(key)}</td>
                                            <td class="font-monospace">${this.app.escapeHtml(String(appInfo[key]))}</td>
                                        </tr>`;
                                    }

                                    capabilitiesHtml += '</tbody></table>';
                                }

                                sessionCapabilitiesDisplay.innerHTML = capabilitiesHtml;
                            } else {
                                sessionCapabilitiesDisplay.innerHTML = '<div class="alert alert-warning"><i class="bi bi-exclamation-triangle-fill me-2"></i>Session capabilities not available.</div>';
                            }
                        }
                    } catch (error) {
                        console.error("Error loading session info:", error);
                        if (sessionIdDisplay) sessionIdDisplay.innerHTML = '<span class="text-danger">Error</span>';
                        if (sessionCapabilitiesDisplay) sessionCapabilitiesDisplay.innerHTML = `<div class="alert alert-danger"><i class="bi bi-x-circle-fill me-2"></i>Error loading session info: ${error.message}</div>`;
                    }
                }, { once: true }); // Run this data loading logic only once when shown

                modal.show();

                // Restore the cursor to default after modal is shown
                document.body.style.cursor = 'default';

                // Make sure the deviceScreen cursor is also reset
                if (this.app.deviceScreen) {
                    this.app.deviceScreen.style.cursor = 'crosshair'; // Keep crosshair for inspection
                }

                // Handle modal close events to clean up
                modalElement.addEventListener('hidden.bs.modal', () => {
                    // Fix any body classes or styles that might be left by Bootstrap
                    document.body.classList.remove('modal-open');
                    document.querySelectorAll('.modal-backdrop').forEach(el => el.remove());
                    document.body.style.overflow = '';
                    document.body.style.paddingRight = '';
                }, { once: true });
            } else {
                // Log using the original pixel coordinates received by this function
                this.app.logAction('error', `No element found near pixel coordinates (${x}, ${y}). Error: ${result.error}`);
            }
        } catch (error) {
            console.error("Error showing element inspector modal:", error);
            this.app.logAction('error', `Failed to show element inspector: ${error.message}`);
        }
    }

    populateElementInspector(data) {
        console.log("Populating element inspector with data:", data);

        const element = data.element;
        const coordinates = data.coordinates;

        if (!element) {
            console.error("Element data is missing");
            return;
        }

        // Store the current inspected element for later reference
        this.currentInspectedElement = element;

        // Update element type and bounds display
        this.updateElementTypeAndBounds(element);

        // Update selected element tab
        this.updateSelectedElementTab(element);

        // Update locators tab
        this.updateLocatorsTab(element);

        // Update attributes tab
        this.updateAttributesTab(element);
    }

    updateElementTypeAndBounds(element) {
        const typeDisplay = document.getElementById('elementTypeDisplay');
        const boundsDisplay = document.getElementById('elementBounds');

        if (typeDisplay && element.attributes) {
            typeDisplay.textContent = element.attributes.type || element.attributes.class || 'Unknown';
        }

        if (boundsDisplay && element.attributes) {
            boundsDisplay.textContent = element.attributes.bounds || 'Unknown';
        }
    }

    updateSelectedElementTab(element) {
        console.log("Updating Selected Element tab with data:", element);
        // Update the selectors in the Selected Element tab
        const accessibilityIdSelector = document.getElementById('accessibilityIdSelector');
        const iOSClassChainSelector = document.getElementById('iOSClassChainSelector');
        const iOSPredicateSelector = document.getElementById('iOSPredicateSelector');
        const xpathSelector = document.getElementById('xpathSelector');

        // Get the locators and attributes from the element
        const locators = element.locators || {};
        const attributes = element.attributes || {};

        // Accessibility ID
        if (accessibilityIdSelector) {
            if (locators['accessibility id'] && locators['accessibility id'].length > 0) {
                accessibilityIdSelector.textContent = locators['accessibility id'][0];
            } else if (attributes.name) {
                accessibilityIdSelector.textContent = attributes.name;
            } else if (attributes.label) {
                accessibilityIdSelector.textContent = attributes.label;
            } else if (attributes['content-desc']) {
                accessibilityIdSelector.textContent = attributes['content-desc'];
            } else {
                accessibilityIdSelector.textContent = '-';
            }
        }

        // iOS Class Chain
        if (iOSClassChainSelector) {
            if (locators['class chain'] && locators['class chain'].length > 0) {
                iOSClassChainSelector.textContent = locators['class chain'][0];
            } else if (attributes.type && (attributes.name || attributes.label)) {
                const elementType = attributes.type;
                const name = attributes.name || attributes.label || '';
                iOSClassChainSelector.textContent = `**//${elementType}[\`name == "${name}"\`]`;
            } else {
                iOSClassChainSelector.textContent = '-';
            }
        }

        // iOS Predicate String
        if (iOSPredicateSelector) {
            if (locators['predicate string'] && locators['predicate string'].length > 0) {
                iOSPredicateSelector.textContent = locators['predicate string'][0];
            } else if (attributes.name) {
                iOSPredicateSelector.textContent = `name == "${attributes.name}"`;
            } else if (attributes.label) {
                iOSPredicateSelector.textContent = `label == "${attributes.label}"`;
            } else {
                iOSPredicateSelector.textContent = '-';
            }
        }

        // XPath
        if (xpathSelector) {
            if (locators.xpath && locators.xpath.length > 0) {
                xpathSelector.textContent = locators.xpath[0];
            } else if (attributes.type) {
                let xpathStr = `//${attributes.type}`;

                if (attributes.name) {
                    xpathStr += `[@name="${attributes.name}"]`;
                } else if (attributes.label) {
                    xpathStr += `[@label="${attributes.label}"]`;
                } else if (attributes.value) {
                    xpathStr += `[@value="${attributes.value}"]`;
                }

                xpathSelector.textContent = xpathStr;
            } else {
                xpathSelector.textContent = '-';
            }
        }

        // Populate the attributes table in the Selected Element tab
        const attributesTable = document.getElementById('elementAttributesTable');
        if (attributesTable && attributes) {
            attributesTable.innerHTML = '';

            // Add key attributes first in this priority order
            const keyAttributes = ['elementId', 'type', 'name', 'label', 'value', 'text', 'enabled', 'visible', 'accessible', 'x', 'y', 'width', 'height'];

            // First add key attributes in the specified order
            keyAttributes.forEach(key => {
                if (attributes[key] !== undefined) {
                    const row = document.createElement('tr');

                    const nameCell = document.createElement('td');
                    nameCell.textContent = key;
                    row.appendChild(nameCell);

                    const valueCell = document.createElement('td');
                    valueCell.textContent = attributes[key];
                    row.appendChild(valueCell);

                    attributesTable.appendChild(row);
                }
            });

            // Then add any remaining attributes
            Object.keys(attributes).forEach(key => {
                if (!keyAttributes.includes(key)) {
                    const row = document.createElement('tr');

                    const nameCell = document.createElement('td');
                    nameCell.textContent = key;
                    row.appendChild(nameCell);

                    const valueCell = document.createElement('td');
                    valueCell.textContent = attributes[key];
                    row.appendChild(valueCell);

                    attributesTable.appendChild(row);
                }
            });
        }
    }

    updateLocatorsTab(element) {
        // Update the locators in the Locators tab
        const locators = element.locators || {};
        const attributes = element.attributes || {};

        // ID Locators
        this.populateLocatorList('idLocators', locators.id || [], 'id');

        // XPath Locators
        const xpathLocators = locators.xpath || [];
        // Generate a basic XPath if none exists but we have element type
        if (xpathLocators.length === 0 && attributes.type) {
            let xpathStr = `//${attributes.type}`;
            if (attributes.name) {
                xpathStr += `[@name="${attributes.name}"]`;
            } else if (attributes.label) {
                xpathStr += `[@label="${attributes.label}"]`;
            } else if (attributes.text) {
                xpathStr += `[text()="${attributes.text}"]`;
            }
            xpathLocators.push(xpathStr);
        }
        this.populateLocatorList('xpathLocators', xpathLocators, 'xpath');

        // Accessibility ID Locators
        const accessibilityLocators = locators['accessibility id'] ||
                                     locators['content-desc'] || [];
        // Generate an accessibility ID if none exists but we have name or label
        if (accessibilityLocators.length === 0) {
            if (attributes.name) {
                accessibilityLocators.push(attributes.name);
            } else if (attributes.label) {
                accessibilityLocators.push(attributes.label);
            }
        }
        this.populateLocatorList('accessibilityLocators', accessibilityLocators, 'accessibility id');

        // Text Locators
        const textLocators = locators.text || [];
        if (textLocators.length === 0 && (attributes.text || attributes.name || attributes.label)) {
            textLocators.push(attributes.text || attributes.name || attributes.label);
        }
        this.populateLocatorList('textLocators', textLocators, 'text');

        // Position Locators - always available based on element coordinates
        const posLocators = locators.airtest ||
            [`pos=(${attributes.center_x || attributes.x || 0}, ${attributes.center_y || attributes.y || 0})`];
        this.populateLocatorList('positionLocators', posLocators, 'position');

        // iOS Class Chain Locators
        let iosClassChainLocators = locators['class chain'] || [];
        if (iosClassChainLocators.length === 0 &&
            attributes.type && (attributes.name || attributes.label)) {
            const elementType = attributes.type;
            const name = attributes.name || attributes.label || '';
            iosClassChainLocators.push(`**//${elementType}[\`name == "${name}"\`]`);
        }
        this.populateLocatorList('iosClassChainLocators', iosClassChainLocators, 'class chain');

        // iOS Predicate String Locators
        let iosPredicateLocators = locators['predicate string'] || [];
        if (iosPredicateLocators.length === 0) {
            if (attributes.name) {
                iosPredicateLocators.push(`name == "${attributes.name}"`);
            } else if (attributes.label) {
                iosPredicateLocators.push(`label == "${attributes.label}"`);
            } else if (attributes.type) {
                iosPredicateLocators.push(`type == "${attributes.type}"`);
            }
        }
        this.populateLocatorList('iosPredicateLocators', iosPredicateLocators, 'predicate string');

        // Android UiAutomator Locators
        let androidUiAutomatorLocators = locators['uiautomator'] || [];
        if (androidUiAutomatorLocators.length === 0) {
            if (attributes.resource_id) {
                androidUiAutomatorLocators.push(`new UiSelector().resourceId("${attributes.resource_id}")`);
            } else if (attributes.text) {
                androidUiAutomatorLocators.push(`new UiSelector().text("${attributes.text}")`);
            } else if (attributes['content-desc']) {
                androidUiAutomatorLocators.push(`new UiSelector().description("${attributes['content-desc']}")`);
            } else if (attributes.class) {
                androidUiAutomatorLocators.push(`new UiSelector().className("${attributes.class}")`);
            }
        }
        this.populateLocatorList('androidUiAutomatorLocators', androidUiAutomatorLocators, 'uiautomator');
    }

    populateLocatorList(containerId, locators, locatorType) {
        const container = document.getElementById(containerId);
        if (!container) return;

        container.innerHTML = '';

        if (!locators || locators.length === 0) {
            const emptyMessage = document.createElement('div');
            emptyMessage.className = 'p-3 text-muted';
            emptyMessage.textContent = 'No locators available';
            container.appendChild(emptyMessage);
            return;
        }

        locators.forEach(locator => {
            if (!locator) return; // Skip null or undefined locators

            const locatorItem = document.createElement('div');
            locatorItem.className = 'locator-item p-2 border-bottom d-flex justify-content-between align-items-center';

            const locatorContent = document.createElement('div');
            locatorContent.className = 'locator-content';

            const locatorCode = document.createElement('code');
            locatorCode.textContent = locator;
            locatorCode.className = 'locator-value';
            locatorContent.appendChild(locatorCode);
            locatorItem.appendChild(locatorContent);

            // Add action buttons container
            const actionButtons = document.createElement('div');
            actionButtons.className = 'action-buttons';

            // Add copy button
            const copyButton = document.createElement('button');
            copyButton.className = 'btn btn-sm btn-outline-secondary me-1';
            copyButton.innerHTML = '<i class="bi bi-clipboard"></i>';
            copyButton.title = 'Copy to clipboard';
            copyButton.onclick = (e) => {
                e.stopPropagation();
                navigator.clipboard.writeText(locator)
                    .then(() => {
                        // Show success feedback
                        copyButton.innerHTML = '<i class="bi bi-check"></i>';
                        copyButton.classList.remove('btn-outline-secondary');
                        copyButton.classList.add('btn-success');

                        // Reset after a short delay
                        setTimeout(() => {
                            copyButton.innerHTML = '<i class="bi bi-clipboard"></i>';
                            copyButton.classList.remove('btn-success');
                            copyButton.classList.add('btn-outline-secondary');
                        }, 1500);
                    })
                    .catch(err => console.error('Failed to copy:', err));
            };
            actionButtons.appendChild(copyButton);

            // Add use button
            const useButton = document.createElement('button');
            useButton.className = 'btn btn-sm btn-outline-primary me-1';
            useButton.innerHTML = 'Use';
            useButton.title = 'Use this locator';
            useButton.onclick = (e) => {
                e.stopPropagation();
                // Store this locator for action buttons
                this.app.selectedLocator = {
                    type: locatorType,
                    value: locator
                };

                // Add visual feedback
                document.querySelectorAll('.locator-item').forEach(item => {
                    item.classList.remove('selected');
                });
                locatorItem.classList.add('selected');

                // Update UI to indicate selection
                this.app.logAction('info', `Selected locator: ${locatorType} = "${locator}"`);
            };
            actionButtons.appendChild(useButton);

            // Add fallback button
            const fallbackButton = document.createElement('button');
            fallbackButton.className = 'btn btn-sm btn-outline-success';
            fallbackButton.innerHTML = '<i class="bi bi-shield-plus"></i>';
            fallbackButton.title = 'Add as fallback locator';
            fallbackButton.onclick = (e) => {
                e.stopPropagation();
                this.addAsFallbackLocator(locatorType, locator);
            };
            actionButtons.appendChild(fallbackButton);

            locatorItem.appendChild(actionButtons);
            container.appendChild(locatorItem);
        });
    }

    /**
     * Add a locator as a fallback locator for the current action
     * @param {string} locatorType - The type of locator (id, xpath, etc.)
     * @param {string} locatorValue - The value of the locator
     */
    addAsFallbackLocator(locatorType, locatorValue) {
        // Check if we have a fallback locators container in the current action form
        const fallbackContainer = document.getElementById('fallbackLocatorsContainer');
        if (!fallbackContainer) {
            this.app.logAction('error', 'Fallback locators container not found in the current action form');
            return;
        }

        // Create a new fallback locator entry
        const fallbackEntry = document.createElement('div');
        fallbackEntry.className = 'fallback-locator-entry mb-2 p-2 border rounded';

        // Create the fallback locator content
        const fallbackContent = document.createElement('div');
        fallbackContent.className = 'd-flex justify-content-between align-items-center';

        // Create the locator info
        const locatorInfo = document.createElement('div');
        locatorInfo.innerHTML = `<strong>${locatorType}</strong>: <code>${locatorValue}</code>`;
        fallbackContent.appendChild(locatorInfo);

        // Create the remove button
        const removeButton = document.createElement('button');
        removeButton.className = 'btn btn-sm btn-outline-danger';
        removeButton.innerHTML = '<i class="bi bi-trash"></i>';
        removeButton.title = 'Remove fallback locator';
        removeButton.onclick = () => {
            fallbackEntry.remove();
            this.app.logAction('info', `Removed fallback locator: ${locatorType}=${locatorValue}`);

            // Check if we need to hide the container
            if (fallbackContainer.querySelectorAll('.fallback-locator-entry').length === 0) {
                document.getElementById('fallbackLocatorsSection')?.classList.add('d-none');
            }
        };
        fallbackContent.appendChild(removeButton);

        // Add hidden inputs for the form submission
        const typeInput = document.createElement('input');
        typeInput.type = 'hidden';
        typeInput.name = 'fallback_locator_type[]';
        typeInput.value = locatorType;
        fallbackEntry.appendChild(typeInput);

        const valueInput = document.createElement('input');
        valueInput.type = 'hidden';
        valueInput.name = 'fallback_locator_value[]';
        valueInput.value = locatorValue;
        fallbackEntry.appendChild(valueInput);

        // Add the content to the entry
        fallbackEntry.appendChild(fallbackContent);

        // Add the entry to the container
        fallbackContainer.appendChild(fallbackEntry);

        // Show the fallback locators section if it was hidden
        document.getElementById('fallbackLocatorsSection')?.classList.remove('d-none');

        // Log the action
        this.app.logAction('success', `Added fallback locator: ${locatorType}=${locatorValue}`);

        // Close the modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('elementInspectorModal'));
        if (modal) {
            modal.hide();
        }
    }

    updateAttributesTab(element) {
        // Update the attributes in the Attributes tab
        const attributesList = document.getElementById('allAttributesTable');
        if (!attributesList || !element.attributes) return;

        attributesList.innerHTML = '';

        // Sort attributes by name
        const attributeNames = Object.keys(element.attributes).sort();

        attributeNames.forEach(name => {
            const value = element.attributes[name];

            const row = document.createElement('tr');

            const nameCell = document.createElement('td');
            nameCell.textContent = name;
            row.appendChild(nameCell);

            const valueCell = document.createElement('td');
            valueCell.textContent = value;
            row.appendChild(valueCell);
            attributesList.appendChild(row);
        });
    }

    setupElementActionButtons() {
        // Setup action dropdown buttons
        const addTapAction = document.getElementById('addTapAction');
        const addClearAction = document.getElementById('addClearAction');
        const addInputTextAction = document.getElementById('addInputTextAction');
        const addLongTapAction = document.getElementById('addLongTapAction');

        if (addTapAction) {
            addTapAction.onclick = () => this.addTapActionFromElement();
        }

        if (addClearAction) {
            addClearAction.onclick = () => this.addClearActionFromElement();
        }

        if (addInputTextAction) {
            addInputTextAction.onclick = () => this.addInputTextActionFromElement();
        }

        if (addLongTapAction) {
            addLongTapAction.onclick = () => this.addLongTapActionFromElement();
        }
    }

    addTapActionFromElement() {
        if (!this.currentInspectedElement) return;

        // Get center coordinates or use coordinates from bounds
        let x, y;
        if (this.currentInspectedElement.attributes && this.currentInspectedElement.attributes.bounds) {
            try {
                const bounds = this.currentInspectedElement.attributes.bounds;
                const boundsMatch = bounds.match(/\[(\d+),(\d+)\]\[(\d+),(\d+)\]/);
                if (boundsMatch) {
                    const x1 = parseInt(boundsMatch[1]);
                    const y1 = parseInt(boundsMatch[2]);
                    const x2 = parseInt(boundsMatch[3]);
                    const y2 = parseInt(boundsMatch[4]);
                    x = Math.round((x1 + x2) / 2);
                    y = Math.round((y1 + y2) / 2);
                }
            } catch (e) {
                console.error("Error parsing bounds:", e);
            }
        }

        if (!x || !y) {
            // Fallback to using center coordinates if provided
            x = this.currentInspectedElement.attributes && this.currentInspectedElement.attributes.center_x;
            y = this.currentInspectedElement.attributes && this.currentInspectedElement.attributes.center_y;
        }

        if (!x || !y) {
            this.app.logAction('error', 'Could not determine tap coordinates from element');
            return;
        }

        // Create a tap action
        const tapAction = {
            type: 'tap',
            x: x,
            y: y,
            description: `Tap on ${this.getElementDescription(this.currentInspectedElement)}`
        };

        // Add the action
        this.app.addActionToList(tapAction);

        // Close the modal
        bootstrap.Modal.getInstance(document.getElementById('elementInspectorModal')).hide();
    }

    addClearActionFromElement() {
        if (!this.currentInspectedElement) return;

        // Choose the best locator available
        let locatorType = 'xpath';
        let locatorValue = '';

        // Prefer ID locator if available
        if (this.currentInspectedElement.locators && this.currentInspectedElement.locators.id && this.currentInspectedElement.locators.id.length > 0) {
            locatorType = 'id';
            locatorValue = this.currentInspectedElement.locators.id[0];
        }
        // Next prefer accessibility ID
        else if (this.currentInspectedElement.locators && this.currentInspectedElement.locators.accessibility && this.currentInspectedElement.locators.accessibility.length > 0) {
            locatorType = 'accessibility';
            locatorValue = this.currentInspectedElement.locators.accessibility[0];
        }
        // Next prefer text
        else if (this.currentInspectedElement.locators && this.currentInspectedElement.locators.text && this.currentInspectedElement.locators.text.length > 0) {
            locatorType = 'text';
            locatorValue = this.currentInspectedElement.locators.text[0];
        }
        // Finally use xpath
        else if (this.currentInspectedElement.locators && this.currentInspectedElement.locators.xpath && this.currentInspectedElement.locators.xpath.length > 0) {
            locatorType = 'xpath';
            locatorValue = this.currentInspectedElement.locators.xpath[0];
        }

        if (!locatorValue) {
            this.app.logAction('error', 'No suitable locator found for clear action');
            return;
        }

        // Create a clear element action
        const clearAction = {
            type: 'clearElement',
            locator_type: locatorType,
            locator_value: locatorValue,
            description: `Clear ${this.getElementDescription(this.currentInspectedElement)}`
        };

        // Add the action
        this.app.addActionToList(clearAction);

        // Close the modal
        bootstrap.Modal.getInstance(document.getElementById('elementInspectorModal')).hide();
    }

    addInputTextActionFromElement() {
        if (!this.currentInspectedElement) return;

        // Choose the best locator available
        let locatorType = 'xpath';
        let locatorValue = '';

        // Prefer ID locator if available
        if (this.currentInspectedElement.locators && this.currentInspectedElement.locators.id && this.currentInspectedElement.locators.id.length > 0) {
            locatorType = 'id';
            locatorValue = this.currentInspectedElement.locators.id[0];
        }
        // Next prefer accessibility ID
        else if (this.currentInspectedElement.locators && this.currentInspectedElement.locators.accessibility && this.currentInspectedElement.locators.accessibility.length > 0) {
            locatorType = 'accessibility';
            locatorValue = this.currentInspectedElement.locators.accessibility[0];
        }
        // Next prefer text
        else if (this.currentInspectedElement.locators && this.currentInspectedElement.locators.text && this.currentInspectedElement.locators.text.length > 0) {
            locatorType = 'text';
            locatorValue = this.currentInspectedElement.locators.text[0];
        }
        // Finally use xpath
        else if (this.currentInspectedElement.locators && this.currentInspectedElement.locators.xpath && this.currentInspectedElement.locators.xpath.length > 0) {
            locatorType = 'xpath';
            locatorValue = this.currentInspectedElement.locators.xpath[0];
        }

        if (!locatorValue) {
            this.app.logAction('error', 'No suitable locator found for input text action');
            return;
        }

        // Prompt for text input
        const textToInput = prompt('Enter text to input:', '');
        if (textToInput === null) return; // User cancelled

        // Create an input text action
        const inputTextAction = {
            type: 'sendKeysToElement',
            locator_type: locatorType,
            locator_value: locatorValue,
            text: textToInput,
            description: `Input "${textToInput}" into ${this.getElementDescription(this.currentInspectedElement)}`
        };

        // Add the action
        this.app.addActionToList(inputTextAction);

        // Close the modal
        bootstrap.Modal.getInstance(document.getElementById('elementInspectorModal')).hide();
    }

    addLongTapActionFromElement() {
        if (!this.currentInspectedElement) return;

        // Get center coordinates or use coordinates from bounds
        let x, y;
        if (this.currentInspectedElement.attributes && this.currentInspectedElement.attributes.bounds) {
            try {
                const bounds = this.currentInspectedElement.attributes.bounds;
                const boundsMatch = bounds.match(/\[(\d+),(\d+)\]\[(\d+),(\d+)\]/);
                if (boundsMatch) {
                    const x1 = parseInt(boundsMatch[1]);
                    const y1 = parseInt(boundsMatch[2]);
                    const x2 = parseInt(boundsMatch[3]);
                    const y2 = parseInt(boundsMatch[4]);
                    x = Math.round((x1 + x2) / 2);
                    y = Math.round((y1 + y2) / 2);
                }
            } catch (e) {
                console.error("Error parsing bounds:", e);
            }
        }

        if (!x || !y) {
            // Fallback to using center coordinates if provided
            x = this.currentInspectedElement.attributes && this.currentInspectedElement.attributes.center_x;
            y = this.currentInspectedElement.attributes && this.currentInspectedElement.attributes.center_y;
        }

        if (!x || !y) {
            this.app.logAction('error', 'Could not determine long press coordinates from element');
            return;
        }

        // Create a long press action
        const longPressAction = {
            type: 'longPress',
            x: x,
            y: y,
            duration: 1000, // 1 second
            description: `Long press on ${this.getElementDescription(this.currentInspectedElement)}`
        };

        // Add the action
        this.app.addActionToList(longPressAction);

        // Close the modal
        bootstrap.Modal.getInstance(document.getElementById('elementInspectorModal')).hide();
    }

    getElementDescription(element) {
        // Create a descriptive text for the element
        let description = element.class || 'element';

        if (element.attributes) {
            if (element.attributes['resource-id']) {
                const idParts = element.attributes['resource-id'].split('/');
                description += ` with ID ${idParts[idParts.length - 1]}`;
            } else if (element.attributes['text']) {
                description += ` with text "${element.attributes['text']}"`;
            } else if (element.attributes['content-desc']) {
                description += ` with content-desc "${element.attributes['content-desc']}"`;
            }
        }

        return description;
    }

    async executeTap(x, y) {
        try {
            if (!this.app.isConnected) {
                this.app.logAction('error', 'Cannot tap - no device connected');
                return;
            }

            this.app.logAction('info', `Executing tap at (${x}, ${y})`);

            const response = await this.app.fetchApi('action/execute', 'POST', {
                action: {
                    type: 'tap',
                    x: x,
                    y: y
                }
            });

            if (response.success) {
                this.app.logAction('success', `Tap executed at (${x}, ${y})`);
                return true;
            } else {
                this.app.logAction('error', `Tap failed: ${response.error || 'Unknown error'}`);
                return false;
            }
        } catch (error) {
            this.app.logAction('error', `Tap error: ${error.message}`);
            return false;
        } finally {
            this.app.deviceScreen.style.cursor = 'default';
        }
    }

    // Handler for screen clicks
    handleScreenClick(event) {
        // Only process if inspection mode is active
        if (this.app.isInspectMode) {
            const rect = event.target.getBoundingClientRect();
            const x = event.clientX - rect.left;
            const y = event.clientY - rect.top;

            this.inspectElement(x, y);
        }
    }

    addTextActionFromElement() {
        if (!this.currentInspectedElement) return;

        // Choose the best locator available
        let locatorType = 'xpath';
        let locatorValue = '';

        // Prefer ID locator if available
        if (this.currentInspectedElement.locators && this.currentInspectedElement.locators.id && this.currentInspectedElement.locators.id.length > 0) {
            locatorType = 'id';
            locatorValue = this.currentInspectedElement.locators.id[0];
        }
        // Next prefer accessibility ID
        else if (this.currentInspectedElement.locators && this.currentInspectedElement.locators.accessibility && this.currentInspectedElement.locators.accessibility.length > 0) {
            locatorType = 'accessibility';
            locatorValue = this.currentInspectedElement.locators.accessibility[0];
        }
        // Finally use xpath
        else if (this.currentInspectedElement.locators && this.currentInspectedElement.locators.xpath && this.currentInspectedElement.locators.xpath.length > 0) {
            locatorType = 'xpath';
            locatorValue = this.currentInspectedElement.locators.xpath[0];
        }

        if (!locatorValue) {
            this.app.logAction('error', 'No suitable locator found for text extraction');
            return;
        }

        // Create a getText action
        const getTextAction = {
            type: 'getText',
            locator_type: locatorType,
            locator_value: locatorValue,
            description: `Get text from ${this.getElementDescription(this.currentInspectedElement)}`
        };

        // Add the action
        this.app.addActionToList(getTextAction);

        // Close the modal
        bootstrap.Modal.getInstance(document.getElementById('elementInspectorModal')).hide();
    }
}

// Make the class available globally instead of using export
window.ElementInteractions = ElementInteractions;