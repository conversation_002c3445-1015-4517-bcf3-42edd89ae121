/**
 * Random Data Generator
 *
 * This module provides functions to enhance input fields with random data generation capabilities
 * and supports automatic data generation during test execution.
 */

class RandomDataGenerator {
    constructor(app) {
        this.app = app;
        this.generators = [];
        this.initialized = false;
    }

    /**
     * Initialize the random data generator
     */
    async init() {
        if (this.initialized) return;

        try {
            // Fetch available generators from the API
            const response = await fetch('/api/random_data/generators');
            const data = await response.json();

            if (data.status === 'success' && Array.isArray(data.generators)) {
                this.generators = data.generators;
                console.log(`Loaded ${this.generators.length} random data generators`);
                this.initialized = true;

                // Populate all generator dropdowns in the UI
                this.populateGeneratorDropdowns();
            } else {
                console.error('Failed to load random data generators:', data);
            }
        } catch (error) {
            console.error('Error initializing random data generator:', error);
        }
    }

    /**
     * Populate all generator dropdowns in the UI
     */
    populateGeneratorDropdowns() {
        // Find all generator dropdowns
        const dropdowns = [
            document.getElementById('inputTextGeneratorDropdown'),
            document.getElementById('textGeneratorDropdown'),
            document.getElementById('tapAndTypeGeneratorDropdown'),
            document.getElementById('sendKeysGeneratorDropdown'),
            document.getElementById('textClearGeneratorDropdown')
        ];

        // Populate each dropdown
        dropdowns.forEach(dropdown => {
            if (dropdown) {
                // Clear existing items except the header and "No Generator" option
                const header = dropdown.querySelector('.dropdown-header');
                const noGenerator = dropdown.querySelector('[data-generator-id="none"]');
                dropdown.innerHTML = '';

                if (header) {
                    dropdown.appendChild(header);
                } else {
                    const newHeader = document.createElement('li');
                    newHeader.innerHTML = '<h6 class="dropdown-header">Generate Random Data</h6>';
                    dropdown.appendChild(newHeader);
                }

                if (noGenerator) {
                    dropdown.appendChild(noGenerator.parentElement);
                } else {
                    const noGenOption = document.createElement('li');
                    const noGenLink = document.createElement('a');
                    noGenLink.className = 'dropdown-item';
                    noGenLink.href = '#';
                    noGenLink.textContent = 'No Generator';
                    noGenLink.dataset.generatorId = 'none';
                    noGenOption.appendChild(noGenLink);
                    dropdown.appendChild(noGenOption);
                }

                // Add all generators
                this.generators.forEach(generator => {
                    const item = document.createElement('li');
                    const link = document.createElement('a');
                    link.className = 'dropdown-item';
                    link.href = '#';
                    link.textContent = generator.name;
                    link.title = generator.description;
                    link.dataset.generatorId = generator.id;

                    // Add click handler
                    link.addEventListener('click', (e) => {
                        e.preventDefault();

                        // Find the associated input field
                        const dropdownButton = dropdown.previousElementSibling;
                        const inputGroup = dropdownButton.parentElement;
                        const input = inputGroup.querySelector('input[type="text"]');

                        // Find the info element
                        const formGroup = inputGroup.closest('.form-group');
                        const infoElement = formGroup.querySelector('.form-text');

                        if (input) {
                            // Update the input field with a placeholder
                            input.value = `${generator.name}`;
                            input.dataset.generatorId = generator.id;

                            // Update the info element
                            if (infoElement) {
                                infoElement.dataset.generatorId = generator.id;
                                infoElement.dataset.generatorName = generator.name;

                                // Update the info text
                                const small = infoElement.querySelector('small');
                                if (small) {
                                    small.textContent = `Using generator: ${generator.name}`;
                                    small.className = 'text-primary';
                                }
                            }

                            // Log the action
                            if (this.app && typeof this.app.logAction === 'function') {
                                this.app.logAction('info', `Selected random data generator: ${generator.name}`);
                            } else {
                                console.log(`Selected random data generator: ${generator.name}`);
                            }
                        }
                    });

                    item.appendChild(link);
                    dropdown.appendChild(item);
                });

                // Add click handler for "No Generator" option
                const noGenLink = dropdown.querySelector('[data-generator-id="none"]');
                if (noGenLink) {
                    noGenLink.addEventListener('click', (e) => {
                        e.preventDefault();

                        // Find the associated input field
                        const dropdownButton = dropdown.previousElementSibling;
                        const inputGroup = dropdownButton.parentElement;
                        const input = inputGroup.querySelector('input[type="text"]');

                        // Find the info element
                        const formGroup = inputGroup.closest('.form-group');
                        const infoElement = formGroup.querySelector('.form-text');

                        if (input) {
                            // Clear the generator ID
                            delete input.dataset.generatorId;

                            // Update the info element
                            if (infoElement) {
                                delete infoElement.dataset.generatorId;
                                delete infoElement.dataset.generatorName;

                                // Update the info text
                                const small = infoElement.querySelector('small');
                                if (small) {
                                    small.textContent = 'Select a data generator to automatically generate data during test execution';
                                    small.className = 'text-muted';
                                }
                            }

                            // Log the action
                            if (this.app && typeof this.app.logAction === 'function') {
                                this.app.logAction('info', 'Cleared random data generator');
                            } else {
                                console.log('Cleared random data generator');
                            }
                        }
                    });
                }
            }
        });
    }

    /**
     * Enhance an input field with random data generation capabilities
     * @param {HTMLInputElement} inputElement - The input element to enhance
     */
    enhanceInputField(inputElement) {
        if (!inputElement || !(inputElement instanceof HTMLElement)) {
            console.error('Invalid input element provided to enhanceInputField');
            return;
        }

        // Create a container for the input and button
        const container = document.createElement('div');
        container.className = 'input-group';

        // Get the parent of the input element
        const parent = inputElement.parentNode;

        // Replace the input with the container
        parent.replaceChild(container, inputElement);

        // Add the input to the container
        container.appendChild(inputElement);

        // Create the dropdown button
        const dropdownButton = document.createElement('button');
        dropdownButton.className = 'btn btn-outline-secondary dropdown-toggle';
        dropdownButton.type = 'button';
        dropdownButton.setAttribute('data-bs-toggle', 'dropdown');
        dropdownButton.setAttribute('aria-expanded', 'false');
        dropdownButton.innerHTML = '<i class="bi bi-magic"></i>';
        dropdownButton.title = 'Generate random data';

        // Create the dropdown menu
        const dropdownMenu = document.createElement('div');
        dropdownMenu.className = 'dropdown-menu dropdown-menu-end';

        // Add a header to the dropdown
        const dropdownHeader = document.createElement('h6');
        dropdownHeader.className = 'dropdown-header';
        dropdownHeader.textContent = 'Generate Random Data';
        dropdownMenu.appendChild(dropdownHeader);

        // Add generators to the dropdown
        this.generators.forEach(generator => {
            const item = document.createElement('a');
            item.className = 'dropdown-item';
            item.href = '#';
            item.textContent = generator.name;
            item.title = generator.description;
            item.setAttribute('data-generator-id', generator.id);

            // Add click event to generate data
            item.addEventListener('click', async (e) => {
                e.preventDefault();

                try {
                    // Show loading state
                    inputElement.disabled = true;
                    dropdownButton.disabled = true;
                    dropdownButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>';

                    // Generate data
                    const response = await fetch('/api/random_data/generate', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            generator_id: generator.id
                        })
                    });

                    const data = await response.json();

                    if (data.status === 'success') {
                        // Set the generated data in the input field
                        inputElement.value = data.data;

                        // Log the action
                        if (this.app && typeof this.app.logAction === 'function') {
                            this.app.logAction('info', `Generated random ${generator.name}: ${data.data}`);
                        } else {
                            console.log(`Generated random ${generator.name}: ${data.data}`);
                        }

                        // Trigger change event
                        const event = new Event('change', { bubbles: true });
                        inputElement.dispatchEvent(event);
                    } else {
                        console.error('Error generating random data:', data.message);
                        if (this.app && typeof this.app.logAction === 'function') {
                            this.app.logAction('error', `Failed to generate ${generator.name}: ${data.message}`);
                        }
                    }
                } catch (error) {
                    console.error('Error generating random data:', error);
                    if (this.app && typeof this.app.logAction === 'function') {
                        this.app.logAction('error', `Error generating ${generator.name}: ${error.message}`);
                    }
                } finally {
                    // Reset loading state
                    inputElement.disabled = false;
                    dropdownButton.disabled = false;
                    dropdownButton.innerHTML = '<i class="bi bi-magic"></i>';
                }
            });

            dropdownMenu.appendChild(item);
        });

        // Add the dropdown button and menu to the container
        container.appendChild(dropdownButton);
        container.appendChild(dropdownMenu);
    }

    /**
     * Enhance all input fields with the specified selector
     * @param {string} selector - CSS selector for input fields to enhance
     */
    enhanceAllInputFields(selector = 'input[type="text"]') {
        if (!this.initialized) {
            console.warn('Random data generator not initialized. Call init() first.');
            return;
        }

        // Find all matching input fields
        const inputFields = document.querySelectorAll(selector);

        // Enhance each input field
        inputFields.forEach(input => {
            // Skip inputs that are already enhanced or should be excluded
            if (input.closest('.input-group') || input.hasAttribute('data-no-random-data')) {
                return;
            }

            this.enhanceInputField(input);
        });
    }

    /**
     * Generate data during test execution
     * @param {string} generatorId - The ID of the generator to use
     * @returns {Promise<string>} - The generated data
     */
    async generateDataForExecution(generatorId) {
        if (!this.initialized) {
            await this.init();
        }

        try {
            // Generate data
            const response = await fetch('/api/random_data/generate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    generator_id: generatorId
                })
            });

            const data = await response.json();

            if (data.status === 'success') {
                // Log the action
                if (this.app && typeof this.app.logAction === 'function') {
                    const generator = this.generators.find(g => g.id === generatorId);
                    const generatorName = generator ? generator.name : generatorId;
                    this.app.logAction('info', `Generated random ${generatorName} for test execution: ${data.data}`);
                } else {
                    console.log(`Generated random data for test execution: ${data.data}`);
                }

                return data.data;
            } else {
                console.error('Error generating random data for test execution:', data.message);
                if (this.app && typeof this.app.logAction === 'function') {
                    this.app.logAction('error', `Failed to generate data for test execution: ${data.message}`);
                }
                return null;
            }
        } catch (error) {
            console.error('Error generating random data for test execution:', error);
            if (this.app && typeof this.app.logAction === 'function') {
                this.app.logAction('error', `Error generating data for test execution: ${error.message}`);
            }
            return null;
        }
    }
}

// Create a global instance
window.randomDataGenerator = new RandomDataGenerator(window.app);

// Initialize when the DOM is loaded
document.addEventListener('DOMContentLoaded', async () => {
    await window.randomDataGenerator.init();

    // Enhance all text input fields
    window.randomDataGenerator.enhanceAllInputFields();
});

// Export the function to enhance specific input fields
window.enhanceInputWithRandomData = (inputElement) => {
    if (window.randomDataGenerator && window.randomDataGenerator.initialized) {
        window.randomDataGenerator.enhanceInputField(inputElement);
    } else {
        console.warn('Random data generator not initialized. Cannot enhance input field.');
    }
};
