/**
 * Reports Manager
 *
 * Handles all report-related functionality including listing, viewing, and downloading reports.
 */

class ReportsManager {
    constructor() {
        // Initialize elements
        this.reportsTable = document.getElementById('reportsTable');
        this.reportsTableBody = document.getElementById('reportsTableBody');
        this.noReportsMessage = document.getElementById('noReportsMessage');
        this.refreshReportsBtn = document.getElementById('refreshReportsBtn');

        // Bind methods
        this.loadReports = this.loadReports.bind(this);
        this.downloadReport = this.downloadReport.bind(this);
        this.regenerateReport = this.regenerateReport.bind(this);

        // Set up event listeners
        this.setupEventListeners();
    }

    setupEventListeners() {
        // Setup refresh button
        if (this.refreshReportsBtn) {
            this.refreshReportsBtn.addEventListener('click', this.loadReports);
        }

        // Listen for tab activation to load reports
        const reportsTabBtn = document.getElementById('reports-tab-btn');
        if (reportsTabBtn) {
            reportsTabBtn.addEventListener('click', this.loadReports);
        }
    }

    // The methods for clearAllureResults, resetDatabase, and clearScreenshots have been removed
    // as they are no longer needed since the buttons were removed from the UI

    async loadReports() {
        try {
            // Show loading state
            if (this.reportsTableBody) {
                this.reportsTableBody.innerHTML = `
                    <tr>
                        <td colspan="5" class="text-center">
                            <div class="spinner-border spinner-border-sm text-primary" role="status">
                                <span class="visually-hidden">Loading reports...</span>
                            </div>
                            <span class="ms-2">Loading reports...</span>
                        </td>
                    </tr>
                `;
            }

            if (this.noReportsMessage) {
                this.noReportsMessage.classList.add('d-none');
            }

            // Fetch reports
            console.log("Fetching reports from API...");
            const response = await fetch('/api/reports/list');
            const data = await response.json();
            console.log("Reports API response:", data);

            if (!response.ok) {
                throw new Error(data.message || 'Failed to load reports');
            }

            // Populate the table with reports
            this.renderReportsTable(data.reports);

        } catch (error) {
            console.error('Error loading reports:', error);
            this.showError('Failed to load reports: ' + error.message);
        }
    }

    renderReportsTable(reports) {
        if (!this.reportsTableBody) return;

        // Clear the table
        this.reportsTableBody.innerHTML = '';

        // If no reports, show message
        if (!reports || reports.length === 0) {
            if (this.reportsTable) {
                this.reportsTable.classList.add('d-none');
            }
            if (this.noReportsMessage) {
                this.noReportsMessage.classList.remove('d-none');
            }
            return;
        }

        // Show table, hide no reports message
        if (this.reportsTable) {
            this.reportsTable.classList.remove('d-none');
        }
        if (this.noReportsMessage) {
            this.noReportsMessage.classList.add('d-none');
        }

        // Log reports for debugging
        console.log("Rendering reports:", reports);

        // Add each report to the table
        reports.forEach(report => {
            const row = document.createElement('tr');

            // Format date
            let formattedDate = 'Unknown';
            try {
                // Handle both creation_time (from database) and timestamp (from directory scan)
                const dateStr = report.creation_time || report.timestamp;
                const date = new Date(dateStr);
                formattedDate = date.toLocaleString();
            } catch (e) {
                console.error('Error formatting date:', e);
                // Use the raw timestamp if available
                formattedDate = report.timestamp || report.creation_time || 'Unknown';
            }

            // Get status badge class
            let statusBadgeClass = 'bg-secondary';
            if (report.status === 'Passed') {
                statusBadgeClass = 'bg-success';
            } else if (report.status === 'Failed') {
                statusBadgeClass = 'bg-danger';
            } else if (report.status === 'Skipped') {
                statusBadgeClass = 'bg-warning';
            }

            // Ensure report_id is set - use directory name from URL if not provided
            if (!report.report_id && report.url) {
                const urlParts = report.url.split('/');
                if (urlParts.length >= 2 && urlParts[1].startsWith('testsuite_execution_')) {
                    report.report_id = urlParts[1];
                    console.log(`Extracted report_id from URL: ${report.report_id}`);
                }
            }

            // Check if Allure report is available
            const hasAllureReport = report.allure_report_url || false;

            // Get filename and suite name
            const filename = report.filename || report.report_dir || report.id || 'Unknown';
            const suiteName = report.suite_name || 'Unknown Suite';

            // Create row content
            row.innerHTML = `
                <td>${filename}</td>
                <td>${suiteName}</td>
                <td>${formattedDate}</td>
                <td><span class="badge ${statusBadgeClass}">${report.status || 'Unknown'}</span></td>
                <td>
                    <div class="btn-group btn-group-sm" role="group">
                        ${report.report_url && report.has_report !== false ? `
                        <a href="${report.report_url}" target="_blank" class="btn btn-outline-primary" title="View HTML Report">
                            <i class="bi bi-eye"></i>
                        </a>` : `
                        <button class="btn btn-outline-secondary" disabled title="Report not available">
                            <i class="bi bi-eye-slash"></i>
                        </button>`}

                        ${report.zip_url && report.has_zip !== false ? `
                        <a href="${report.zip_url}" class="btn btn-outline-primary download-zip" data-filename="${report.filename || report.report_id || ''}" title="Download ZIP Report">
                            <i class="bi bi-file-earmark-zip"></i>
                        </a>` : `
                        <button type="button" class="btn btn-outline-primary download-report" data-url="${report.url}" data-filename="${report.filename || report.report_id || ''}" title="Download Report">
                            <i class="bi bi-download"></i>
                        </button>`}
                        ${report.report_id ? `
                        <button type="button" class="btn btn-outline-success regenerate-report"
                            data-report-id="${report.report_id}"
                            title="Regenerate Report">
                            <i class="bi bi-arrow-clockwise"></i>
                        </button>` : ''}
                        <button type="button" class="btn btn-outline-danger delete-report"
                            data-filename="${report.filename}"
                            data-report-id="${report.report_id || ''}"
                            title="Delete Report">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </td>
            `;

            // Add click handler for download button
            const downloadBtn = row.querySelector('.download-report');
            if (downloadBtn) {
                downloadBtn.addEventListener('click', () => {
                    const filename = downloadBtn.getAttribute('data-filename');
                    this.downloadReport(report.url, filename);
                });
            }

            // Add click handler for download zip button
            const downloadZipBtn = row.querySelector('.download-zip');
            if (downloadZipBtn) {
                downloadZipBtn.addEventListener('click', (e) => {
                    e.preventDefault();
                    const filename = downloadZipBtn.getAttribute('data-filename');
                    this.downloadZipReport(filename);
                });
            }

            // Add click handler for regenerate button
            const regenerateBtn = row.querySelector('.regenerate-report');
            if (regenerateBtn) {
                regenerateBtn.addEventListener('click', () => {
                    const reportId = regenerateBtn.getAttribute('data-report-id');
                    this.regenerateReport(reportId);
                });
            }

            // Add click handler for delete button
            const deleteBtn = row.querySelector('.delete-report');
            if (deleteBtn) {
                deleteBtn.addEventListener('click', () => {
                    const filename = deleteBtn.getAttribute('data-filename');
                    const reportId = deleteBtn.getAttribute('data-report-id');
                    this.deleteReport(filename, reportId);
                });
            }

            this.reportsTableBody.appendChild(row);
        });
    }

    async downloadReport(url, filename) {
        try {
            // Show loading toast
            this.showToast('Downloading report...', 'info');
            console.log(`Attempting to download report: ${url}, filename: ${filename}`);

            // Create a direct download link using the /api/reports/download endpoint
            const downloadUrl = `/api/reports/download/${encodeURIComponent(filename)}`;
            console.log(`Using download URL: ${downloadUrl}`);

            // Create a temporary link and trigger download
            const downloadLink = document.createElement('a');
            downloadLink.href = downloadUrl;
            downloadLink.download = filename;
            downloadLink.target = '_blank'; // Open in new tab as fallback
            document.body.appendChild(downloadLink);

            // Click the link to start download
            downloadLink.click();

            // Clean up
            setTimeout(() => {
                document.body.removeChild(downloadLink);
            }, 1000);

            // Show success toast
            this.showToast('Report download initiated', 'success');

        } catch (error) {
            console.error('Error downloading report:', error);
            this.showToast('Failed to download report: ' + error.message, 'error');
        }
    }

    async downloadZipReport(filename) {
        try {
            // Show loading toast
            this.showToast('Downloading ZIP report...', 'info');
            console.log(`Attempting to download ZIP report for: ${filename}`);

            // Create a direct download link using the /api/reports/download_zip endpoint
            const downloadUrl = `/api/reports/download_zip/${encodeURIComponent(filename)}.zip`;
            console.log(`Using download URL: ${downloadUrl}`);

            // Create a temporary link and trigger download
            const downloadLink = document.createElement('a');
            downloadLink.href = downloadUrl;
            downloadLink.download = `${filename}.zip`;
            downloadLink.target = '_blank'; // Open in new tab as fallback
            document.body.appendChild(downloadLink);

            // Click the link to start download
            downloadLink.click();

            // Clean up
            setTimeout(() => {
                document.body.removeChild(downloadLink);
            }, 1000);

            // Show success toast
            this.showToast('ZIP report download initiated', 'success');

        } catch (error) {
            console.error('Error downloading ZIP report:', error);
            this.showToast('Failed to download ZIP report: ' + error.message, 'error');
        }
    }

    async regenerateReport(reportId) {
        try {
            console.log("Regenerate report called with reportId:", reportId);

            // Check if we have a valid reportId
            if (!reportId) {
                console.error("Cannot regenerate report: reportId is missing");
                this.showToast('Cannot regenerate report: missing report ID', 'error');
                return;
            }

            // Show loading toast
            this.showToast('Regenerating report...', 'info');

            // Send regenerate request
            const response = await fetch(`/api/reports/regenerate/${reportId}`);
            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.message || 'Failed to regenerate report');
            }

            // Show success toast
            this.showToast('Report regenerated successfully', 'success');

            // If we have a report URL, open it in a new tab
            if (data.report_url) {
                window.open(data.report_url, '_blank');
            }

            // Reload reports list to update any changes
            this.loadReports();

        } catch (error) {
            console.error('Error regenerating report:', error);
            this.showToast('Failed to regenerate report: ' + error.message, 'error');
        }
    }

    async deleteReport(filename, reportId) {
        try {
            console.log("Delete report called with:", { filename, reportId });

            // Check if we have valid parameters
            if (!filename && !reportId) {
                console.error("Cannot delete report: both filename and reportId are missing");
                this.showToast('Cannot delete report: missing identifier', 'error');
                return;
            }

            // Special handling for undefined reports
            if (filename === "undefined" || reportId === "undefined") {
                console.log("Detected undefined report - special handling");

                // Use a more descriptive name for the confirmation
                const displayName = "undefined report";

                // Confirm deletion
                if (!confirm(`Are you sure you want to delete the ${displayName}? This action cannot be undone.`)) {
                    return; // User cancelled
                }

                // Show loading toast
                this.showToast('Deleting undefined report...', 'info');

                // Use special URL for undefined reports
                const deleteUrl = `/api/reports/delete/undefined`;
                console.log(`Using special URL for undefined report deletion: ${deleteUrl}`);

                // Send delete request
                const response = await fetch(deleteUrl, {
                    method: 'DELETE'
                });

                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.message || 'Failed to delete undefined report');
                }

                // Show success toast
                this.showToast('Undefined report deleted successfully', 'success');

                // Reload reports list
                this.loadReports();

                return;
            }

            // Use a more descriptive name for the confirmation
            const displayName = reportId ? `report suite ${reportId}` : `report ${filename}`;

            // Confirm deletion
            if (!confirm(`Are you sure you want to delete ${displayName}? This action cannot be undone.`)) {
                return; // User cancelled
            }

            // Show loading toast
            this.showToast('Deleting report...', 'info');

            // Determine the correct URL to use for deletion
            let deleteUrl;

            // If we have a reportId and it looks like a timestamp (testsuite_execution_YYYYMMDD_HHMMSS format)
            if (reportId && (reportId.startsWith('testsuite_execution_') || /^\d{8}_\d{6}$/.test(reportId))) {
                // Use the report_id directly which is the directory name
                deleteUrl = `/api/reports/delete/${reportId}/mainreport.html`;
                console.log(`Using report_id for deletion: ${reportId}`);
            } else if (filename && filename.includes('/')) {
                // This is already a path with directory/file format
                deleteUrl = `/api/reports/delete/${encodeURIComponent(filename)}`;
                console.log(`Using full path for deletion: ${filename}`);
            } else if (filename) {
                // Fallback to just the filename
                deleteUrl = `/api/reports/delete/${encodeURIComponent(filename)}`;
                console.log(`Using filename for deletion: ${filename}`);
            } else {
                // Last resort - try to use reportId as filename
                deleteUrl = `/api/reports/delete/${encodeURIComponent(reportId)}`;
                console.log(`Using reportId as filename for deletion: ${reportId}`);
            }

            console.log(`Sending DELETE request to: ${deleteUrl}`);

            // Send delete request
            const response = await fetch(deleteUrl, {
                method: 'DELETE'
            });

            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.message || 'Failed to delete report');
            }

            // Show success toast
            this.showToast('Report deleted successfully', 'success');

            // Reload reports list
            this.loadReports();

        } catch (error) {
            console.error('Error deleting report:', error);
            this.showToast('Failed to delete report: ' + error.message, 'error');
        }
    }

    showError(message) {
        if (this.reportsTableBody) {
            this.reportsTableBody.innerHTML = `
                <tr>
                    <td colspan="5" class="text-center text-danger">
                        <i class="bi bi-exclamation-triangle me-2"></i>${message}
                    </td>
                </tr>
            `;
        }

        // Also show toast
        this.showToast(message, 'error');
    }

    showToast(message, type = 'info') {
        const toastContainer = document.getElementById('toastContainer');
        if (!toastContainer) return;

        // Create toast element
        const toast = document.createElement('div');
        toast.className = `toast align-items-center text-white bg-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'primary'} border-0`;
        toast.setAttribute('role', 'alert');
        toast.setAttribute('aria-live', 'assertive');
        toast.setAttribute('aria-atomic', 'true');

        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        `;

        // Add to container
        toastContainer.appendChild(toast);

        // Initialize and show
        const bsToast = new bootstrap.Toast(toast, { autohide: true, delay: 5000 });
        bsToast.show();

        // Remove from DOM after hiding
        toast.addEventListener('hidden.bs.toast', () => {
            toast.remove();
        });
    }
}

// Initialize reports manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Create global instance
    window.reportsManager = new ReportsManager();

    // Check if TestSuitesManager also has a reference
    if (window.testSuitesManager) {
        // Make test suites manager aware of reports manager
        window.testSuitesManager.reportsManager = window.reportsManager;
    }
});