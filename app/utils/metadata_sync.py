#!/usr/bin/env python3
"""
Metadata synchronization utilities for test cases and test suites
"""
import os
import json
import logging
from datetime import datetime

logger = logging.getLogger(__name__)

def sync_test_case_to_database(file_path):
    """
    Sync a test case file to the database metadata table
    
    Args:
        file_path (str): Path to the test case JSON file
        
    Returns:
        bool: Success status
    """
    try:
        from utils.database import upsert_test_case_metadata
        
        if not os.path.exists(file_path):
            logger.warning(f"Test case file not found: {file_path}")
            return False
        
        with open(file_path, 'r') as f:
            test_case_data = json.load(f)
        
        # Extract metadata
        test_case_id = test_case_data.get('test_case_id')
        if not test_case_id:
            logger.warning(f"No test_case_id found in {file_path}")
            return False
        
        name = test_case_data.get('name', os.path.basename(file_path).replace('.json', ''))
        description = test_case_data.get('description', '')
        device_id = test_case_data.get('device_id', '')
        actions = test_case_data.get('actions', [])
        action_count = len(actions)
        labels = test_case_data.get('labels', [])
        
        # Sync to database
        success = upsert_test_case_metadata(
            test_case_id=test_case_id,
            name=name,
            description=description,
            file_path=file_path,
            device_id=device_id,
            action_count=action_count,
            labels=labels
        )
        
        if success:
            logger.info(f"✅ Synced test case to database: {test_case_id}")
        else:
            logger.error(f"❌ Failed to sync test case to database: {test_case_id}")
        
        return success
        
    except Exception as e:
        logger.error(f"Error syncing test case to database: {str(e)}")
        return False

def sync_test_suite_to_database(file_path):
    """
    Sync a test suite file to the database metadata table
    
    Args:
        file_path (str): Path to the test suite JSON file
        
    Returns:
        bool: Success status
    """
    try:
        from utils.database import upsert_test_suite_metadata
        
        if not os.path.exists(file_path):
            logger.warning(f"Test suite file not found: {file_path}")
            return False
        
        with open(file_path, 'r') as f:
            test_suite_data = json.load(f)
        
        # Extract metadata
        suite_id = test_suite_data.get('id')
        if not suite_id:
            logger.warning(f"No id found in {file_path}")
            return False
        
        name = test_suite_data.get('name', os.path.basename(file_path).replace('.json', ''))
        description = test_suite_data.get('description', '')
        test_cases = test_suite_data.get('test_cases', [])
        
        # Sync to database
        success = upsert_test_suite_metadata(
            suite_id=suite_id,
            name=name,
            description=description,
            file_path=file_path,
            test_case_files=test_cases
        )
        
        if success:
            logger.info(f"✅ Synced test suite to database: {suite_id}")
        else:
            logger.error(f"❌ Failed to sync test suite to database: {suite_id}")
        
        return success
        
    except Exception as e:
        logger.error(f"Error syncing test suite to database: {str(e)}")
        return False

def remove_test_case_from_database(test_case_id):
    """
    Remove a test case from the database metadata table
    
    Args:
        test_case_id (str): Test case ID
        
    Returns:
        bool: Success status
    """
    try:
        from utils.database import delete_test_case_metadata
        
        success = delete_test_case_metadata(test_case_id)
        
        if success:
            logger.info(f"✅ Removed test case from database: {test_case_id}")
        else:
            logger.error(f"❌ Failed to remove test case from database: {test_case_id}")
        
        return success
        
    except Exception as e:
        logger.error(f"Error removing test case from database: {str(e)}")
        return False

def remove_test_suite_from_database(suite_id):
    """
    Remove a test suite from the database metadata table
    
    Args:
        suite_id (str): Test suite ID
        
    Returns:
        bool: Success status
    """
    try:
        from utils.database import delete_test_suite_metadata
        
        success = delete_test_suite_metadata(suite_id)
        
        if success:
            logger.info(f"✅ Removed test suite from database: {suite_id}")
        else:
            logger.error(f"❌ Failed to remove test suite from database: {suite_id}")
        
        return success
        
    except Exception as e:
        logger.error(f"Error removing test suite from database: {str(e)}")
        return False

def sync_all_test_cases():
    """
    Sync all test case files to the database
    
    Returns:
        dict: Summary of sync results
    """
    test_cases_dir = "/Users/<USER>/Documents/automation-tool/test_cases"
    
    if not os.path.exists(test_cases_dir):
        logger.warning(f"Test cases directory not found: {test_cases_dir}")
        return {'success': 0, 'failed': 0, 'total': 0}
    
    json_files = [f for f in os.listdir(test_cases_dir) if f.endswith('.json')]
    
    success_count = 0
    failed_count = 0
    
    for json_file in json_files:
        file_path = os.path.join(test_cases_dir, json_file)
        if sync_test_case_to_database(file_path):
            success_count += 1
        else:
            failed_count += 1
    
    return {
        'success': success_count,
        'failed': failed_count,
        'total': len(json_files)
    }

def sync_all_test_suites():
    """
    Sync all test suite files to the database
    
    Returns:
        dict: Summary of sync results
    """
    test_suites_dir = "/Users/<USER>/Documents/automation-tool/test_suites"
    
    if not os.path.exists(test_suites_dir):
        logger.warning(f"Test suites directory not found: {test_suites_dir}")
        return {'success': 0, 'failed': 0, 'total': 0}
    
    json_files = [f for f in os.listdir(test_suites_dir) if f.endswith('.json')]
    
    success_count = 0
    failed_count = 0
    
    for json_file in json_files:
        file_path = os.path.join(test_suites_dir, json_file)
        if sync_test_suite_to_database(file_path):
            success_count += 1
        else:
            failed_count += 1
    
    return {
        'success': success_count,
        'failed': failed_count,
        'total': len(json_files)
    }

def auto_sync_on_file_change(file_path, operation='create'):
    """
    Automatically sync metadata when files are created, modified, or deleted
    
    Args:
        file_path (str): Path to the file that changed
        operation (str): Type of operation ('create', 'modify', 'delete')
        
    Returns:
        bool: Success status
    """
    try:
        if not file_path.endswith('.json'):
            return True  # Ignore non-JSON files
        
        # Determine if it's a test case or test suite
        if '/test_cases/' in file_path:
            if operation == 'delete':
                # Extract test_case_id from filename or read from file before deletion
                # For now, we'll skip delete operations for test cases
                logger.info(f"Test case file deleted: {file_path}")
                return True
            else:
                return sync_test_case_to_database(file_path)
        
        elif '/test_suites/' in file_path:
            if operation == 'delete':
                # Extract suite_id from filename or read from file before deletion
                # For now, we'll skip delete operations for test suites
                logger.info(f"Test suite file deleted: {file_path}")
                return True
            else:
                return sync_test_suite_to_database(file_path)
        
        return True
        
    except Exception as e:
        logger.error(f"Error in auto-sync: {str(e)}")
        return False
