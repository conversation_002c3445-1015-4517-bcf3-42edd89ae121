import re
import logging
from .global_values_db import GlobalValuesDB

# Configure logger
logger = logging.getLogger(__name__)

def substitute_parameters(text, log_substitution=True):
    """
    Substitute parameters in the format ${paramname} with values from the global parameters database.
    
    Args:
        text (str): The text containing parameters to substitute
        log_substitution (bool): Whether to log the substitution for debugging
        
    Returns:
        str: The text with parameters substituted
    """
    if not text:
        return text
        
    if not isinstance(text, str):
        return text
        
    # If no parameter pattern is found, return the original text
    if '${' not in text:
        return text
        
    # Initialize the global values database
    global_values_db = GlobalValuesDB()
    
    # Define the pattern for parameters: ${paramname}
    pattern = r'\${([^}]+)}'
    
    # Find all parameter names in the text
    param_names = re.findall(pattern, text)
    
    # If no parameters found, return the original text
    if not param_names:
        return text
        
    # Create a copy of the original text for substitution
    result = text
    
    # Track substitutions for logging
    substitutions = []
    
    # Replace each parameter with its value
    for param_name in param_names:
        # Get the parameter value from the database
        param_value = global_values_db.get_value(param_name)
        
        if param_value is not None:
            # Convert the value to string if it's not already
            param_value_str = str(param_value)
            
            # Replace the parameter in the text
            result = result.replace(f'${{{param_name}}}', param_value_str)
            
            # Track the substitution
            substitutions.append((param_name, param_value_str))
        else:
            # Parameter not found, log a warning but keep the placeholder
            logger.warning(f"Parameter '{param_name}' not found in global values, keeping placeholder")
    
    # Log the substitutions if requested
    if log_substitution and substitutions:
        logger.info(f"Parameter substitution: '{text}' -> '{result}'")
        for param_name, param_value in substitutions:
            logger.info(f"  - ${{{param_name}}} -> {param_value}")
    
    return result
