"""
Random Data Generator using Mimesis

This module provides functions to generate random test data for different input fields
using the Mimesis library.
"""

from mimesis import Person, Address, Finance, Text, Generic
from mimesis.locales import Locale
import random

# Create instances of Mimesis providers for different locales
person_au = Person(locale=Locale.EN_AU)
person_nz = Person(locale=Locale.EN)  # Using EN as NZ is not directly available
address_au = Address(locale=Locale.EN_AU)
address_nz = Address(locale=Locale.EN)  # Using EN as NZ is not directly available
finance = Finance(locale=Locale.EN)
text = Text(locale=Locale.EN)
generic = Generic(locale=Locale.EN)

# Dictionary of available data generation functions
DATA_GENERATORS = {
    "first_name": {
        "name": "First Name",
        "description": "Generate a random first name",
        "function": lambda: person_au.first_name()
    },
    "last_name": {
        "name": "Last Name",
        "description": "Generate a random last name",
        "function": lambda: person_au.last_name()
    },
    "email": {
        "name": "<PERSON><PERSON>",
        "description": "Generate a random email address",
        "function": lambda: person_au.email()
    },
    "au_phone": {
        "name": "AU Phone",
        "description": "Generate a random Australian phone number (10 digits starting with 04)",
        "function": lambda: "04" + ''.join([str(random.randint(0, 9)) for _ in range(8)])
    },
    "nz_phone": {
        "name": "NZ Phone",
        "description": "Generate a random New Zealand phone number (10 digits starting with 02)",
        "function": lambda: "02" + ''.join([str(random.randint(0, 9)) for _ in range(8)])
    },
    "flybuy_number": {
        "name": "FlyBuy Number",
        "description": "Generate a random FlyBuy number (13 digits starting with 279)",
        "function": lambda: "279" + ''.join([str(random.randint(0, 9)) for _ in range(10)])
    },
    "au_address": {
        "name": "AU Address",
        "description": "Generate a random Australian address",
        "function": lambda: f"{address_au.street_number()} {address_au.street_name()}, {address_au.city()}, {address_au.state()}"
    },
    "nz_address": {
        "name": "NZ Address",
        "description": "Generate a random New Zealand address",
        "function": lambda: f"{address_nz.street_number()} {address_nz.street_name()}, {address_nz.city()}, New Zealand"
    },
    "au_postcode": {
        "name": "AU Postcode",
        "description": "Generate a valid Australian postcode",
        "function": lambda: address_au.postal_code()
    },
    "nz_postcode": {
        "name": "NZ Postcode",
        "description": "Generate a valid New Zealand postcode",
        "function": lambda: ''.join([str(random.randint(0, 9)) for _ in range(4)])  # NZ postcodes are 4 digits
    },
    "company": {
        "name": "Company",
        "description": "Generate a random company name",
        "function": lambda: generic.business.company()
    },
    "notes": {
        "name": "Notes",
        "description": "Generate random notes",
        "function": lambda: text.text(quantity=2)
    },
    "username": {
        "name": "Username",
        "description": "Generate a random username",
        "function": lambda: person_au.username()
    },
    "password": {
        "name": "Password",
        "description": "Generate a random password",
        "function": lambda: person_au.password(length=10)
    },
    "credit_card": {
        "name": "Credit Card",
        "description": "Generate a random credit card number",
        "function": lambda: finance.credit_card_number()
    },
    "iban": {
        "name": "IBAN",
        "description": "Generate a random IBAN",
        "function": lambda: finance.iban()
    },
    "job_title": {
        "name": "Job Title",
        "description": "Generate a random job title",
        "function": lambda: person_au.occupation()
    },
    "full_name": {
        "name": "Full Name",
        "description": "Generate a random full name",
        "function": lambda: person_au.full_name()
    },
    "sentence": {
        "name": "Sentence",
        "description": "Generate a random sentence",
        "function": lambda: text.sentence()
    },
    "paragraph": {
        "name": "Paragraph",
        "description": "Generate a random paragraph",
        "function": lambda: text.paragraph()
    },
    "url": {
        "name": "URL",
        "description": "Generate a random URL",
        "function": lambda: f"https://www.{text.word().lower()}.com"
    },
    "date": {
        "name": "Date",
        "description": "Generate a random date (YYYY-MM-DD)",
        "function": lambda: person_au.date().strftime("%Y-%m-%d")
    }
}

def get_generator_options():
    """
    Get a list of available data generator options for the UI dropdown

    Returns:
        list: List of dictionaries with id, name, and description for each generator
    """
    return [
        {"id": key, "name": value["name"], "description": value["description"]}
        for key, value in DATA_GENERATORS.items()
    ]

def generate_data(generator_id):
    """
    Generate random data using the specified generator

    Args:
        generator_id (str): ID of the generator to use

    Returns:
        str: Generated data or error message if generator not found
    """
    if generator_id in DATA_GENERATORS:
        try:
            return DATA_GENERATORS[generator_id]["function"]()
        except Exception as e:
            return f"Error generating data: {str(e)}"
    else:
        return f"Unknown generator: {generator_id}"

# For testing
if __name__ == "__main__":
    # Test each generator
    for generator_id, generator_info in DATA_GENERATORS.items():
        print(f"{generator_info['name']}: {generate_data(generator_id)}")
