/**
 * Test Report Generator
 * 
 * This module provides functions to generate HTML test reports from test suite execution results.
 */

const fs = require('fs');
const path = require('path');

/**
 * Generate an HTML report for a test suite execution
 *
 * @param {Object} testSuite - The test suite data
 * @param {string} testSuite.name - The name of the test suite
 * @param {Array} testSuite.testCases - Array of test cases in the suite
 * @param {string} reportsDirectory - Optional reports directory path from settings
 * @returns {string} - Path to the generated report file
 */
function generateReport(testSuite, reportsDirectory = null) {
    // Create timestamp for filename
    const timestamp = getTimestamp();
    const reportName = `TestSuite_Execution_${timestamp}.html`;

    // Use provided reports directory or fallback to default
    const reportsDir = reportsDirectory || path.join(__dirname, '../../reports');
    const reportPath = path.join(reportsDir, reportName);
    
    // Ensure reports directory exists
    if (!fs.existsSync(reportsDir)) {
        fs.mkdirSync(reportsDir, { recursive: true });
    }
    
    // Create screenshots directory if it doesn't exist
    const screenshotsDir = path.join(reportsDir, 'screenshots');
    if (!fs.existsSync(screenshotsDir)) {
        fs.mkdirSync(screenshotsDir, { recursive: true });
    }
    
    // Generate the HTML content
    const htmlContent = generateHtmlContent(testSuite, timestamp);
    
    // Write to file
    fs.writeFileSync(reportPath, htmlContent, 'utf8');
    
    console.log(`Test report generated: ${reportPath}`);
    return reportPath;
}

/**
 * Generate a timestamp in the format YYYYMMDD_HHMMSS
 * 
 * @returns {string} - Formatted timestamp
 */
function getTimestamp() {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    const seconds = String(now.getSeconds()).padStart(2, '0');
    
    return `${year}${month}${day}_${hours}${minutes}${seconds}`;
}

/**
 * Generate HTML content for the report
 * 
 * @param {Object} testSuite - The test suite data
 * @param {string} timestamp - Report timestamp
 * @returns {string} - HTML content
 */
function generateHtmlContent(testSuite, timestamp) {
    // Calculate statistics
    const stats = calculateStatistics(testSuite);
    
    // Format timestamp for display
    const formattedTimestamp = formatTimestamp(timestamp);
    
    // Start HTML content
    let html = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Suite Report - ${formattedTimestamp}</title>
    <link rel="stylesheet" href="assets/report.css">
</head>
<body>
    <header class="header">
        <h1>Suites</h1>
        <div class="status-summary">
            Status: <span class="status-badge status-badge-${stats.status}">${stats.status}</span>
            <span class="stats-summary">
                <span class="passed-count">${stats.passed}</span> passed,
                <span class="failed-count">${stats.failed}</span> failed,
                <span class="skipped-count">${stats.skipped}</span> skipped
            </span>
        </div>
    </header>
    
    <div class="content">
        <div class="suites-panel">
            <div class="suite-heading">
                <span class="expand-icon"></span>
                ${testSuite.name}
            </div>
            
            <ul class="test-list">`;
    
    // Add test cases
    let stepId = 1; // For tracking step IDs
    testSuite.testCases.forEach((testCase, index) => {
        const testCaseStatus = determineStatus(testCase.steps);
        
        html += `
                <li class="test-item">
                    <div class="test-header">
                        <div class="test-case">
                            <span class="expand-icon"></span>
                            <span class="status-icon status-icon-${testCaseStatus}"></span>
                            #${index + 1} ${testCase.name}
                        </div>
                        <span class="test-duration">${testCase.duration || '0ms'}</span>
                    </div>
                    <ul class="test-steps">`;
        
        // Add test steps
        testCase.steps.forEach(step => {
            html += `
                        <li class="test-step" data-step-id="${stepId}" data-status="${step.status}">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-${step.status}"></span>
                                ${step.name}
                            </div>
                            <span class="test-step-duration">${step.duration || '0ms'}</span>
                        </li>`;
            stepId++;
        });
        
        html += `
                    </ul>
                </li>`;
    });
            
    html += `
            </ul>
        </div>
        
        <div class="details-panel">
            <!-- This will be populated by JavaScript when a test step is clicked -->
        </div>
    </div>
    
    <script src="assets/report.js"></script>
</body>
</html>`;

    return html;
}

/**
 * Calculate statistics from test suite results
 * 
 * @param {Object} testSuite - The test suite data
 * @returns {Object} - Statistics object
 */
function calculateStatistics(testSuite) {
    let passed = 0;
    let failed = 0;
    let skipped = 0;
    
    // Count pass/fail/skip for test cases
    testSuite.testCases.forEach(testCase => {
        const testCaseStatus = determineStatus(testCase.steps);
        if (testCaseStatus === 'passed') passed++;
        else if (testCaseStatus === 'failed') failed++;
        else if (testCaseStatus === 'skipped') skipped++;
    });
    
    // Determine overall status
    let status = 'passed';
    if (failed > 0) status = 'failed';
    else if (passed === 0 && skipped > 0) status = 'skipped';
    
    return {
        passed,
        failed,
        skipped,
        status
    };
}

/**
 * Determine status based on test steps using unique ID retry logic
 *
 * @param {Array} steps - Array of test steps
 * @returns {string} - Status (passed, failed, or skipped)
 */
function determineStatus(steps) {
    // Group steps by action_id to handle retries properly
    const actionGroups = {};

    steps.forEach(step => {
        const actionId = step.action_id || step.clean_action_id || 'unknown';
        if (!actionGroups[actionId]) {
            actionGroups[actionId] = [];
        }
        actionGroups[actionId].push(step);
    });

    // Determine final status for each action (most recent execution)
    const finalStatuses = [];

    for (const actionId in actionGroups) {
        const actionSteps = actionGroups[actionId];

        // Sort by timestamp or use last entry as most recent
        // For now, use the last entry in the array as most recent
        const mostRecentStep = actionSteps[actionSteps.length - 1];
        finalStatuses.push(mostRecentStep.status);
    }

    // Determine overall status based on final statuses
    if (finalStatuses.some(status => status === 'failed')) {
        return 'failed';
    }
    if (finalStatuses.some(status => status === 'passed')) {
        return 'passed';
    }
    return 'skipped';
}

/**
 * Format timestamp for display in the report
 * 
 * @param {string} timestamp - Timestamp in format YYYYMMDD_HHMMSS
 * @returns {string} - Formatted timestamp for display
 */
function formatTimestamp(timestamp) {
    // Format: YYYYMMDD_HHMMSS -> YYYY-MM-DD HH:MM:SS
    const year = timestamp.substring(0, 4);
    const month = timestamp.substring(4, 6);
    const day = timestamp.substring(6, 8);
    const hour = timestamp.substring(9, 11);
    const minute = timestamp.substring(11, 13);
    const second = timestamp.substring(13, 15);
    
    return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
}

/**
 * Save screenshot to the screenshots directory
 *
 * @param {string} stepId - ID of the step
 * @param {string} screenshotData - Base64 encoded screenshot data
 * @param {string} reportsDirectory - Optional reports directory path from settings
 * @returns {string} - Path to the saved screenshot
 */
function saveScreenshot(stepId, screenshotData, reportsDirectory = null) {
    if (!screenshotData) return null;

    // Remove data URL prefix if present
    let base64Data = screenshotData;
    if (screenshotData.includes('base64,')) {
        base64Data = screenshotData.split('base64,')[1];
    }

    // Create filename
    const filename = `step_${stepId}.png`;
    const reportsDir = reportsDirectory || path.join(__dirname, '../../reports');
    const screenshotsDir = path.join(reportsDir, 'screenshots');
    const screenshotPath = path.join(screenshotsDir, filename);
    
    // Ensure directory exists
    if (!fs.existsSync(screenshotsDir)) {
        fs.mkdirSync(screenshotsDir, { recursive: true });
    }
    
    // Write file
    fs.writeFileSync(screenshotPath, base64Data, 'base64');
    
    return `screenshots/${filename}`;
}

/**
 * Get the URL for the latest generated report
 *
 * @param {string} reportsDirectory - Optional reports directory path from settings
 * @returns {string} - URL to the latest report
 */
function getLatestReportUrl(reportsDirectory = null) {
    const reportsDir = reportsDirectory || path.join(__dirname, '../../reports');
    if (!fs.existsSync(reportsDir)) return null;
    
    // Get all HTML files
    const htmlFiles = fs.readdirSync(reportsDir)
        .filter(file => file.endsWith('.html'))
        .map(file => {
            const filePath = path.join(reportsDir, file);
            return {
                name: file,
                path: filePath,
                time: fs.statSync(filePath).mtime.getTime()
            };
        });
    
    if (htmlFiles.length === 0) return null;
    
    // Sort by modification time (newest first)
    htmlFiles.sort((a, b) => b.time - a.time);
    
    return `/reports/${htmlFiles[0].name}`;
}

// Export functions
module.exports = {
    generateReport,
    getLatestReportUrl,
};

// Command-line execution
if (require.main === module) {
    try {
        // Check if a file path was provided as a command-line argument
        const args = process.argv.slice(2);
        
        if (args.length > 0) {
            const jsonFilePath = args[0];
            
            // Check if the file exists
            if (!fs.existsSync(jsonFilePath)) {
                console.error(`Error: File not found: ${jsonFilePath}`);
                process.exit(1);
            }
            
            // Read the JSON file
            const testSuiteData = JSON.parse(fs.readFileSync(jsonFilePath, 'utf8'));
            
            // Generate the report
            const reportPath = generateReport(testSuiteData);
            
            // Output just the report path for the Python script to capture
            console.log(reportPath);
        } else {
            console.error('Error: No input file specified');
            console.error('Usage: node reportGenerator.js <path_to_test_suite_data.json>');
            process.exit(1);
        }
    } catch (error) {
        console.error(`Error: ${error.message}`);
        process.exit(1);
    }
} 