import os
import shutil
import logging
import glob
from datetime import datetime

logger = logging.getLogger(__name__)

def ensure_directory_exists(directory_path):
    """Ensure a directory exists, create it if it doesn't"""
    if not os.path.exists(directory_path):
        os.makedirs(directory_path, exist_ok=True)
        logger.info(f"Created directory: {directory_path}")
    return directory_path

def copy_screenshots_to_report(report_dir):
    """
    DISABLED: This function is now disabled as screenshots are saved directly to the report folder.

    Args:
        report_dir (str): Path to the report directory

    Returns:
        int: Always returns 0 as no screenshots are copied
    """
    logger.info("copy_screenshots_to_report is disabled - screenshots are now saved directly to the report folder")
    return 0

def copy_latest_screenshot_to_report(report_dir, action_id=None):
    """
    Copy the latest.png screenshot to the report's screenshots directory with an action_id.
    This function is used as a fallback when a screenshot fails to be taken directly.

    Args:
        report_dir (str): Path to the report directory
        action_id (str, optional): Action ID for the filename. If None, a random one will be generated.

    Returns:
        str or None: Path to the copied screenshot or None if failed
    """
    try:
        # If no action_id is provided, generate one
        if not action_id:
            import random
            import string
            chars = string.ascii_letters + string.digits
            action_id = ''.join(random.choice(chars) for _ in range(10))
            logger.info(f"Generated action_id for screenshot: {action_id}")

        # Check if a screenshot with this action_id already exists in the report folder
        report_screenshots_dir = os.path.join(report_dir, 'screenshots')
        ensure_directory_exists(report_screenshots_dir)
        target_filename = f"{action_id}.png"
        target_path = os.path.join(report_screenshots_dir, target_filename)

        if os.path.exists(target_path):
            logger.info(f"Screenshot with action_id {action_id} already exists at {target_path}, skipping copy")
            return target_path

        # First check for latest.png in the report folder
        report_latest_path = os.path.join(report_screenshots_dir, 'latest.png')
        if os.path.exists(report_latest_path):
            # Copy from report's latest.png
            shutil.copy2(report_latest_path, target_path)
            logger.info(f"Copied report's latest screenshot to: {target_path}")
            return target_path

        # Fallback to app's static directory latest.png
        app_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        latest_screenshot = os.path.join(app_dir, 'app', 'static', 'screenshots', 'latest.png')

        # Check if latest.png exists
        if not os.path.exists(latest_screenshot):
            logger.warning(f"Latest screenshot not found: {latest_screenshot}")
            return None

        # Copy the file
        shutil.copy2(latest_screenshot, target_path)
        logger.info(f"Copied latest screenshot to report: {latest_screenshot} -> {target_path}")

        return target_path
    except Exception as e:
        logger.error(f"Error copying latest screenshot to report: {str(e)}")
        return None
