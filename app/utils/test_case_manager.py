import os
import json
import shutil
from datetime import datetime
import logging
from utils.id_generator import add_action_ids_to_test_case

# Setup logger for this module (optional, but good practice)
logger = logging.getLogger(__name__)

class TestCaseManager:
    """Manager for test case operations to prevent duplicates and maintain consistency"""

    def __init__(self, test_cases_dir):
        """Initialize the test case manager with the test cases directory"""
        self.test_cases_dir = test_cases_dir

        # Create directory if it doesn't exist
        if not os.path.exists(self.test_cases_dir):
            os.makedirs(self.test_cases_dir)

    def get_test_cases(self):
        """Get a list of all test cases with metadata"""
        test_cases = []

        # Ensure directory exists
        if not os.path.exists(self.test_cases_dir):
            return test_cases

        # Iterate through files in the directory
        for filename in os.listdir(self.test_cases_dir):
            if filename.endswith('.json') and not filename.endswith('.bak'):
                # Get the full path to the test case file
                file_path = os.path.join(self.test_cases_dir, filename)

                # Load the test case data to get metadata
                try:
                    with open(file_path, 'r') as f:
                        test_case = json.load(f)

                    # Extract test case metadata
                    test_cases.append({
                        'filename': filename,
                        'name': test_case.get('name', 'Unnamed Test Case'),
                        'description': test_case.get('description', ''),
                        'created': test_case.get('created', ''),
                        'updated': test_case.get('updated', ''),
                        'action_count': len(test_case.get('actions', []))
                    })
                except Exception as e:
                    print(f"Error loading test case {filename}: {str(e)}")

        # Sort test cases by creation date (newest first)
        test_cases.sort(key=lambda x: x.get('created', ''), reverse=True)
        return test_cases

    def load_test_case(self, filename):
        """Load a test case by filename"""
        file_path = os.path.join(self.test_cases_dir, filename)

        # Check if the file exists
        if not os.path.exists(file_path):
            return None

        # Load the test case data
        try:
            with open(file_path, 'r') as f:
                test_case = json.load(f)

            # Validate and process actions, especially hook actions
            if 'actions' in test_case and test_case['actions']:
                for action in test_case['actions']:
                    # Ensure action type is valid
                    if 'type' in action:
                        # Special handling for hookAction
                        if action['type'] == 'hookAction':
                            logger.info(f"Processing hookAction in test case {filename}")
                            # Ensure hook_type is present
                            if 'hook_type' not in action:
                                logger.warning(f"hookAction missing hook_type in {filename}")
                                action['hook_type'] = 'tap'  # Default to tap as fallback

                            # Ensure hook_data is present
                            if 'hook_data' not in action:
                                logger.warning(f"hookAction missing hook_data in {filename}")
                                action['hook_data'] = {}  # Add empty hook_data as fallback
                    else:
                        logger.warning(f"Action missing type in {filename}")
                        action['type'] = 'unknown'  # Add a default type

            return test_case
        except Exception as e:
            logger.error(f"Error loading test case {filename}: {str(e)}")
            return None

    def save_test_case(self, test_case_data, filename=None, is_save_as=False):
        """Save a test case with automatic versioning

        Args:
            test_case_data: The test case data to save
            filename: Optional filename to save as. If not provided, a new filename will be generated.
            is_save_as: Whether this is a "Save As" operation. If True, always generate a new filename.

        Returns:
            str: The filename where the test case was saved
        """
        # Set created timestamp if not present
        if 'created' not in test_case_data:
            test_case_data['created'] = datetime.now().isoformat()

        # Always update the 'updated' timestamp
        test_case_data['updated'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        # If this is a Save As operation, ignore any existing filename and generate a new one
        if is_save_as:
            filename = None

        # Generate a filename if not provided
        if not filename:
            # Use the test case name if available, otherwise 'test_case'
            base_name = test_case_data.get('name', 'test_case')
            # Remove spaces and special characters
            base_name = ''.join(c for c in base_name if c.isalnum() or c in '_ ').strip()
            base_name = base_name.replace(' ', '_')
            # Add timestamp
            timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
            filename = f"{base_name}_{timestamp}.json"

        # Get the full path to the file
        file_path = os.path.join(self.test_cases_dir, filename)

        # Create a backup if the file already exists and this is not a Save As operation
        if os.path.exists(file_path) and not is_save_as:
            backup_path = f"{file_path}.bak"
            try:
                shutil.copy2(file_path, backup_path)
            except Exception as e:
                print(f"Error creating backup for {filename}: {str(e)}")

        # Save the test case
        try:
            # Log the test case data for debugging
            print(f"Saving test case with {len(test_case_data.get('actions', []))} actions")

            # Check for fallback locators in actions
            for i, action in enumerate(test_case_data.get('actions', [])):
                if 'fallback_locators' in action:
                    print(f"Action {i+1} has {len(action['fallback_locators'])} fallback locators")
                    for j, fallback in enumerate(action['fallback_locators']):
                        print(f"  Fallback {j+1}: {fallback.get('locator_type')}={fallback.get('locator_value')}")

            # Add unique action IDs to all actions if they don't already have them
            test_case_data = add_action_ids_to_test_case(test_case_data)
            logger.info(f"Added unique action IDs to test case actions")

            with open(file_path, 'w') as f:
                json.dump(test_case_data, f, indent=2)
            return filename
        except Exception as e:
            print(f"Error saving test case {filename}: {str(e)}")
            return None

    def delete_test_case(self, filename):
        """Delete a test case by filename"""
        file_path = os.path.join(self.test_cases_dir, filename)

        # Check if the file exists
        if not os.path.exists(file_path):
            logger.warning(f"Attempted to delete non-existent file: {file_path}")
            return False

        # Delete the file without creating a backup
        try:
            logger.info(f"Attempting to delete file: {file_path}")
            os.remove(file_path)
            logger.info(f"Successfully deleted file: {file_path}")
            # Also delete any backup files if they exist
            backup_path = f"{file_path}.bak"
            if os.path.exists(backup_path):
                try:
                    logger.info(f"Attempting to delete backup file: {backup_path}")
                    os.remove(backup_path)
                    logger.info(f"Successfully deleted backup file: {backup_path}")
                except OSError as backup_e: # Catch potential errors deleting backup
                    logger.error(f"Failed to delete backup file {backup_path}: {backup_e}")
                    # Decide if this constitutes overall failure - currently, we still return True
            return True
        except FileNotFoundError:
             # This case should ideally be caught by the os.path.exists check above,
             # but include for robustness (e.g., race conditions)
            logger.error(f"File not found during delete operation (unexpected): {file_path}")
            return False
        except PermissionError as pe:
            logger.error(f"Permission denied deleting file {file_path}: {pe}")
            return False
        except OSError as oe: # Catch other OS-related errors (like file locked)
            logger.error(f"OS error deleting file {file_path}: {oe}")
            return False
        except Exception as e:
            # Catch any other unexpected exceptions
            logger.exception(f"Unexpected error deleting test case {filename}: {e}") # Use logger.exception to include traceback
            return False

    def rename_test_case(self, filename, new_name):
        """Rename a test case by filename and update the filename to match the new name.

        Args:
            filename: The filename of the test case to rename
            new_name: The new name for the test case

        Returns:
            dict: A dictionary with the updated test case data and new filename, or None if failed
        """
        # Load the original test case
        test_case_data = self.load_test_case(filename)
        if not test_case_data:
            logger.error(f"Cannot rename non-existent test case: {filename}")
            return None

        # Update the name in the test case data
        test_case_data['name'] = new_name

        # Update the 'updated' timestamp
        test_case_data['updated'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        # Generate a new filename based on the new name
        # Remove spaces and special characters
        base_name = ''.join(c for c in new_name if c.isalnum() or c in '_ ').strip()
        base_name = base_name.replace(' ', '_')

        # Extract timestamp from original filename if it exists
        import re
        timestamp_match = re.search(r'_(\d{14})\.json$', filename)
        if timestamp_match:
            timestamp = timestamp_match.group(1)
        else:
            # Use current timestamp if no timestamp found in original filename
            timestamp = datetime.now().strftime('%Y%m%d%H%M%S')

        new_filename = f"{base_name}_{timestamp}.json"

        # Save the test case with the new filename
        saved_filename = self.save_test_case(test_case_data, filename=new_filename, is_save_as=False)

        if saved_filename:
            # Delete the old file
            old_file_path = os.path.join(self.test_cases_dir, filename)
            try:
                if os.path.exists(old_file_path):
                    os.remove(old_file_path)
                    logger.info(f"Deleted old test case file: {filename}")
            except Exception as e:
                logger.warning(f"Failed to delete old test case file {filename}: {e}")

            # Update references in test suites
            self._update_test_suite_references(filename, saved_filename)

            logger.info(f"Successfully renamed test case {filename} to '{new_name}' with new filename {saved_filename}")
            return {
                'filename': saved_filename,
                'test_case': test_case_data,
                'old_filename': filename
            }
        else:
            logger.error(f"Failed to save renamed test case data for {filename}")
            return None

    def duplicate_test_case(self, filename):
        """Duplicate a test case by filename, automatically naming the copy.

        Args:
            filename: The filename of the test case to duplicate

        Returns:
            str: The filename of the duplicated test case, or None if failed
        """
        # Load the original test case
        original_data = self.load_test_case(filename)
        if not original_data:
            logger.error(f"Cannot duplicate non-existent test case: {filename}")
            return None

        # Create the data for the new test case (deep copy might be safer if complex objects)
        new_data = json.loads(json.dumps(original_data)) # Simple deep copy via JSON

        # Generate a new name (e.g., "Original Name_Copy_Timestamp")
        original_name = new_data.get('name', 'Unnamed')
        timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
        new_data['name'] = f"{original_name}_Copy_{timestamp}"

        # Clear existing timestamps so save_test_case generates new ones
        new_data.pop('created', None)
        new_data.pop('updated', None)
        # Let save_test_case generate a new filename based on the new name and timestamp
        # Setting is_save_as=True ensures a new file is always created
        new_filename = self.save_test_case(new_data, filename=None, is_save_as=True)

        if new_filename:
            logger.info(f"Successfully duplicated {filename} to {new_filename} with name {new_data['name']}")
        else:
            logger.error(f"Failed to save duplicated test case data for {filename}")

        return new_filename

    def clean_duplicates(self):
        """Clean up duplicate test case files based on content comparison

        Returns:
            list: List of duplicates that were moved to backup
        """
        test_cases = {}
        duplicates = []

        # Get all test case files
        files = [f for f in os.listdir(self.test_cases_dir)
                if f.endswith('.json') and not f.endswith('.bak')]

        # First, sort the files by modification time (newest first)
        files.sort(key=lambda f: os.path.getmtime(os.path.join(self.test_cases_dir, f)),
                   reverse=True)

        # Find duplicates based on content
        for filename in files:
            file_path = os.path.join(self.test_cases_dir, filename)

            try:
                with open(file_path, 'r') as f:
                    content = f.read()

                # Calculate content hash for comparison
                content_hash = hash(content)

                if content_hash in test_cases:
                    # This is a duplicate
                    duplicates.append(filename)

                    # Backup and remove the duplicate
                    backup_path = f"{file_path}.duplicate"
                    shutil.copy2(file_path, backup_path)
                    os.remove(file_path)
                else:
                    # This is a new unique test case
                    test_cases[content_hash] = filename
            except Exception as e:
                print(f"Error processing file {filename}: {str(e)}")

        return duplicates

    def _update_test_suite_references(self, old_filename, new_filename):
        """Update references to a test case in all test suites.

        Args:
            old_filename: The old filename to replace
            new_filename: The new filename to use
        """
        try:
            # Get test suites directory from settings or use default
            test_suites_dir = os.path.join(os.getcwd(), 'test_suites')

            if not os.path.exists(test_suites_dir):
                logger.info(f"Test suites directory does not exist: {test_suites_dir}")
                return

            # Get all test suite files
            suite_files = [f for f in os.listdir(test_suites_dir) if f.endswith('.json')]

            updated_suites = 0
            for suite_file in suite_files:
                suite_path = os.path.join(test_suites_dir, suite_file)
                try:
                    with open(suite_path, 'r') as f:
                        suite_data = json.load(f)

                    # Check if this suite references the old filename
                    test_cases = suite_data.get('test_cases', [])
                    updated = False

                    for i, test_case in enumerate(test_cases):
                        if isinstance(test_case, dict) and test_case.get('filename') == old_filename:
                            test_cases[i]['filename'] = new_filename
                            updated = True
                        elif isinstance(test_case, str) and test_case == old_filename:
                            test_cases[i] = new_filename
                            updated = True

                    # Save the updated suite if changes were made
                    if updated:
                        suite_data['updated'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                        with open(suite_path, 'w') as f:
                            json.dump(suite_data, f, indent=2)
                        updated_suites += 1
                        logger.info(f"Updated test suite {suite_file} to reference new filename {new_filename}")

                except Exception as e:
                    logger.error(f"Error updating test suite {suite_file}: {e}")

            if updated_suites > 0:
                logger.info(f"Updated {updated_suites} test suites with new filename reference")
            else:
                logger.info("No test suites needed updating")

        except Exception as e:
            logger.error(f"Error updating test suite references: {e}")