from .base_action import BaseAction
import logging
import traceback

class AndroidFunctionsAction(BaseAction):
    """Handler for Android-specific functions using UiAutomator2 and ADB"""

    def execute(self, params):
        """
        Execute Android-specific functions using UiAutomator2 and ADB

        Args:
            params: Dictionary containing:
                - function_name: Name of the Android function to execute (e.g., 'home', 'back', 'recent_apps')
                - function_params: Optional parameters for the function

        Returns:
            dict: Result with status and message
        """
        if not self.controller:
            return {"status": "error", "message": "No device controller available"}

        # Get the function name from params
        function_name = params.get('function_name')
        if not function_name:
            return {"status": "error", "message": "No function name provided"}

        self.logger.info(f"Executing Android function: {function_name}")

        try:
            # Execute the function based on the function name
            if function_name == 'home':
                # Press the home button
                if hasattr(self.controller, 'driver') and self.controller.driver:
                    self.controller.driver.press_keycode(3)  # KEYCODE_HOME
                    return {"status": "success", "message": "Pressed home button"}
                else:
                    # Use ADB fallback
                    result = self.controller._run_adb_command(['shell', 'input', 'keyevent', '3'])
                    return {"status": "success", "message": "Pressed home button via ADB"}

            elif function_name == 'back':
                # Press the back button
                if hasattr(self.controller, 'driver') and self.controller.driver:
                    self.controller.driver.press_keycode(4)  # KEYCODE_BACK
                    return {"status": "success", "message": "Pressed back button"}
                else:
                    # Use ADB fallback
                    result = self.controller._run_adb_command(['shell', 'input', 'keyevent', '4'])
                    return {"status": "success", "message": "Pressed back button via ADB"}

            elif function_name == 'recent_apps':
                # Open recent apps
                if hasattr(self.controller, 'driver') and self.controller.driver:
                    self.controller.driver.press_keycode(187)  # KEYCODE_APP_SWITCH
                    return {"status": "success", "message": "Opened recent apps"}
                else:
                    # Use ADB fallback
                    result = self.controller._run_adb_command(['shell', 'input', 'keyevent', '187'])
                    return {"status": "success", "message": "Opened recent apps via ADB"}

            elif function_name == 'menu':
                # Press menu button
                if hasattr(self.controller, 'driver') and self.controller.driver:
                    self.controller.driver.press_keycode(82)  # KEYCODE_MENU
                    return {"status": "success", "message": "Pressed menu button"}
                else:
                    # Use ADB fallback
                    result = self.controller._run_adb_command(['shell', 'input', 'keyevent', '82'])
                    return {"status": "success", "message": "Pressed menu button via ADB"}

            elif function_name == 'power':
                # Press power button
                if hasattr(self.controller, 'driver') and self.controller.driver:
                    self.controller.driver.press_keycode(26)  # KEYCODE_POWER
                    return {"status": "success", "message": "Pressed power button"}
                else:
                    # Use ADB fallback
                    result = self.controller._run_adb_command(['shell', 'input', 'keyevent', '26'])
                    return {"status": "success", "message": "Pressed power button via ADB"}

            elif function_name == 'volume_up':
                # Press volume up
                if hasattr(self.controller, 'driver') and self.controller.driver:
                    self.controller.driver.press_keycode(24)  # KEYCODE_VOLUME_UP
                    return {"status": "success", "message": "Pressed volume up"}
                else:
                    # Use ADB fallback
                    result = self.controller._run_adb_command(['shell', 'input', 'keyevent', '24'])
                    return {"status": "success", "message": "Pressed volume up via ADB"}

            elif function_name == 'volume_down':
                # Press volume down
                if hasattr(self.controller, 'driver') and self.controller.driver:
                    self.controller.driver.press_keycode(25)  # KEYCODE_VOLUME_DOWN
                    return {"status": "success", "message": "Pressed volume down"}
                else:
                    # Use ADB fallback
                    result = self.controller._run_adb_command(['shell', 'input', 'keyevent', '25'])
                    return {"status": "success", "message": "Pressed volume down via ADB"}

            elif function_name == 'get_clipboard':
                # Get clipboard content
                if hasattr(self.controller, 'driver') and self.controller.driver:
                    content = self.controller.driver.get_clipboard()
                    return {"status": "success", "message": f"Clipboard content: {content}", "content": content}
                else:
                    # Use ADB fallback
                    result = self.controller._run_adb_command(['shell', 'am', 'broadcast', '-a', 'clipper.get'])
                    return {"status": "success", "message": "Retrieved clipboard via ADB", "content": result}

            elif function_name == 'set_clipboard':
                # Set clipboard content
                content = params.get('content', '')
                if hasattr(self.controller, 'driver') and self.controller.driver:
                    self.controller.driver.set_clipboard(content)
                    return {"status": "success", "message": f"Set clipboard content to: {content}"}
                else:
                    # Use ADB fallback
                    result = self.controller._run_adb_command(['shell', 'am', 'broadcast', '-a', 'clipper.set', '-e', 'text', content])
                    return {"status": "success", "message": f"Set clipboard content via ADB: {content}"}

            elif function_name == 'open_notifications':
                # Open notification panel
                if hasattr(self.controller, 'driver') and self.controller.driver:
                    self.controller.driver.open_notifications()
                    return {"status": "success", "message": "Opened notification panel"}
                else:
                    # Use ADB fallback
                    result = self.controller._run_adb_command(['shell', 'cmd', 'statusbar', 'expand-notifications'])
                    return {"status": "success", "message": "Opened notification panel via ADB"}

            elif function_name == 'open_quick_settings':
                # Open quick settings
                if hasattr(self.controller, 'driver') and self.controller.driver:
                    # UiAutomator2 doesn't have direct method, use ADB
                    result = self.controller._run_adb_command(['shell', 'cmd', 'statusbar', 'expand-settings'])
                    return {"status": "success", "message": "Opened quick settings"}
                else:
                    result = self.controller._run_adb_command(['shell', 'cmd', 'statusbar', 'expand-settings'])
                    return {"status": "success", "message": "Opened quick settings via ADB"}

            elif function_name == 'hide_keyboard':
                # Hide keyboard
                if hasattr(self.controller, 'driver') and self.controller.driver:
                    self.controller.driver.hide_keyboard()
                    return {"status": "success", "message": "Hidden keyboard"}
                else:
                    # Use ADB fallback
                    result = self.controller._run_adb_command(['shell', 'input', 'keyevent', '111'])  # KEYCODE_ESCAPE
                    return {"status": "success", "message": "Hidden keyboard via ADB"}

            elif function_name == 'get_device_info':
                # Get device information
                info = {}
                if hasattr(self.controller, 'driver') and self.controller.driver:
                    info = self.controller.driver.capabilities
                
                # Add ADB info
                if hasattr(self.controller, 'adb_available') and self.controller.adb_available:
                    info['manufacturer'] = self.controller._run_adb_command(['shell', 'getprop', 'ro.product.manufacturer'])
                    info['model'] = self.controller._run_adb_command(['shell', 'getprop', 'ro.product.model'])
                    info['android_version'] = self.controller._run_adb_command(['shell', 'getprop', 'ro.build.version.release'])
                    info['api_level'] = self.controller._run_adb_command(['shell', 'getprop', 'ro.build.version.sdk'])
                
                return {"status": "success", "message": "Retrieved device information", "info": info}

            elif function_name == 'list_packages':
                # List installed packages
                if hasattr(self.controller, 'adb_available') and self.controller.adb_available:
                    result = self.controller._run_adb_command(['shell', 'pm', 'list', 'packages'])
                    packages = [line.replace('package:', '') for line in result.split('\n') if line.startswith('package:')]
                    return {"status": "success", "message": f"Found {len(packages)} packages", "packages": packages}
                else:
                    return {"status": "error", "message": "ADB not available for package listing"}

            elif function_name == 'clear_app_data':
                # Clear app data
                package_name = params.get('package_name', '')
                if not package_name:
                    return {"status": "error", "message": "No package name provided"}
                
                if hasattr(self.controller, 'adb_available') and self.controller.adb_available:
                    result = self.controller._run_adb_command(['shell', 'pm', 'clear', package_name])
                    return {"status": "success", "message": f"Cleared data for {package_name}"}
                else:
                    return {"status": "error", "message": "ADB not available for clearing app data"}

            elif function_name == 'enable_wifi':
                # Enable WiFi
                result = self.controller._run_adb_command(['shell', 'svc', 'wifi', 'enable'])
                return {"status": "success", "message": "Enabled WiFi"}

            elif function_name == 'disable_wifi':
                # Disable WiFi
                result = self.controller._run_adb_command(['shell', 'svc', 'wifi', 'disable'])
                return {"status": "success", "message": "Disabled WiFi"}

            elif function_name == 'enable_data':
                # Enable mobile data
                result = self.controller._run_adb_command(['shell', 'svc', 'data', 'enable'])
                return {"status": "success", "message": "Enabled mobile data"}

            elif function_name == 'disable_data':
                # Disable mobile data
                result = self.controller._run_adb_command(['shell', 'svc', 'data', 'disable'])
                return {"status": "success", "message": "Disabled mobile data"}

            elif function_name == 'take_screenshot':
                # Take screenshot using ADB
                screenshot_path = params.get('path', '/sdcard/screenshot.png')
                result = self.controller._run_adb_command(['shell', 'screencap', '-p', screenshot_path])
                return {"status": "success", "message": f"Screenshot saved to {screenshot_path}"}

            elif function_name == 'input_text':
                # Input text using ADB
                text = params.get('text', '')
                if not text:
                    return {"status": "error", "message": "No text provided"}
                
                # Escape special characters for shell
                escaped_text = text.replace(' ', '%s').replace('&', '\\&')
                result = self.controller._run_adb_command(['shell', 'input', 'text', escaped_text])
                return {"status": "success", "message": f"Input text: {text}"}

            elif function_name == 'swipe_gesture':
                # Perform swipe gesture
                start_x = params.get('start_x', 500)
                start_y = params.get('start_y', 1000)
                end_x = params.get('end_x', 500)
                end_y = params.get('end_y', 500)
                duration = params.get('duration', 1000)
                
                result = self.controller._run_adb_command(['shell', 'input', 'swipe', str(start_x), str(start_y), str(end_x), str(end_y), str(duration)])
                return {"status": "success", "message": f"Performed swipe from ({start_x},{start_y}) to ({end_x},{end_y})"}

            else:
                return {"status": "error", "message": f"Unsupported Android function: {function_name}"}

        except Exception as e:
            self.logger.error(f"Error executing Android function {function_name}: {e}")
            self.logger.error(traceback.format_exc())
            return {"status": "error", "message": f"Android function execution failed: {str(e)}"}
