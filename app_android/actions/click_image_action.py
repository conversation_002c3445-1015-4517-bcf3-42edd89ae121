from .base_action import BaseAction
import os
import logging

class ClickImageAction(BaseAction):
    """Handler for click image actions (compatible with AirTest)"""

    def execute(self, params):
        """
        Execute click image action

        Args:
            params: Dictionary containing:
                - image_path: Path to the target image
                - timeout: (Optional) Timeout in seconds for locating the image
                - threshold: (Optional) Similarity threshold (0.0-1.0)
                - target_pos: (Optional) Target position to click relative to the found image
                              (e.g., [0.5, 0.5] for center)

        Returns:
            dict: Result with status and message
        """
        if not self.controller:
            return {"status": "error", "message": "No device controller available"}

        image_path = params.get('image_path')
        timeout = params.get('timeout', 20)  # Default timeout of 20 seconds
        threshold = params.get('threshold', 0.8)  # Default threshold
        target_pos = params.get('target_pos', [0.5, 0.5])  # Default to center

        if not image_path:
            return {"status": "error", "message": "Missing image path parameter"}

        # Check if Airtest device is connected
        if hasattr(self.controller, 'airtest_device') and not self.controller.airtest_device:
            # Try to ensure Airtest connection
            if hasattr(self.controller, '_ensure_airtest_connected'):
                try:
                    airtest_connected = self.controller._ensure_airtest_connected()
                    if not airtest_connected:
                        return {"status": "error", "message": "No devices added. Please connect a device first."}
                except Exception as e:
                    self.logger.error(f"Error connecting Airtest device: {e}")
                    return {"status": "error", "message": f"Failed to connect Airtest device: {str(e)}"}

        try:
            # Check if the image file exists and is accessible
            if not os.path.exists(image_path):
                # Try to resolve from reference_images directory if it's a relative path
                try:
                    from config import DIRECTORIES
                    reference_dir = DIRECTORIES.get('REFERENCE_IMAGES', '')
                    if reference_dir:
                        full_path = os.path.join(reference_dir, os.path.basename(image_path))
                        if os.path.exists(full_path):
                            image_path = full_path
                            self.logger.info(f"Resolved image path to: {image_path}")
                except (ImportError, Exception) as e:
                    self.logger.warning(f"Could not resolve reference image directory: {e}")

                # If still doesn't exist, return error
                if not os.path.exists(image_path):
                    return {"status": "error", "message": f"Image file not found: {image_path}"}

            # Check if controller has an AirTest-compatible click_image method
            if hasattr(self.controller, 'click_image'):
                try:
                    result = self.controller.click_image(
                        image_path,
                        timeout=timeout,
                        threshold=threshold,
                        target_pos=target_pos
                    )

                    # Handle different return types
                    if isinstance(result, dict):
                        return result
                    elif isinstance(result, bool) or isinstance(result, tuple):
                        if result:
                            return {
                                "status": "success",
                                "message": f"Clicked image: {image_path}"
                            }
                        else:
                            return {
                                "status": "error",
                                "message": f"Failed to click image: {image_path}"
                            }
                    else:
                        # Assume success if no specific return value
                        return {
                            "status": "success",
                            "message": f"Clicked image: {image_path}"
                        }
                except Exception as e:
                    self.logger.error(f"Error in click_image method: {e}")
                    if "No devices added" in str(e):
                        return {"status": "error", "message": "No devices added. Please connect a device first."}
                    return {"status": "error", "message": f"Error clicking image: {str(e)}"}

            # Check for alternative implementation patterns
            elif hasattr(self.controller, 'find_image') and hasattr(self.controller, 'click'):
                try:
                    # First find the image
                    position = self.controller.find_image(
                        image_path,
                        timeout=timeout,
                        threshold=threshold
                    )

                    if position:
                        # Then click at the position
                        self.controller.click(position)
                        return {
                            "status": "success",
                            "message": f"Clicked image: {image_path}"
                        }
                    else:
                        return {
                            "status": "error",
                            "message": f"Image not found: {image_path}"
                        }
                except Exception as e:
                    self.logger.error(f"Error in find_image/click approach: {e}")
                    if "No devices added" in str(e):
                        return {"status": "error", "message": "No devices added. Please connect a device first."}
                    return {"status": "error", "message": f"Error finding/clicking image: {str(e)}"}

            # Try direct AirTest imports if available (fallback)
            elif self._has_airtest_support():
                try:
                    from airtest.core.api import touch, Template, exists, device as current_device

                    # Ensure Airtest device is initialized
                    if hasattr(self.controller, '_ensure_airtest_connected'):
                        self.logger.info("Ensuring Airtest device is connected...")
                        airtest_connected = self.controller._ensure_airtest_connected()
                        if not airtest_connected:
                            self.logger.warning("Failed to connect Airtest device, but continuing anyway")

                    # Try to initialize Airtest device if not already initialized
                    if hasattr(self.controller, '_init_airtest'):
                        self.logger.info("Initializing Airtest device...")
                        airtest_initialized = self.controller._init_airtest()
                        if not airtest_initialized:
                            self.logger.warning("Failed to initialize Airtest device, but continuing anyway")

                    # Check if a current device is set in Airtest
                    if not current_device():
                        self.logger.error("No Airtest device available after initialization attempts")
                        return {"status": "error", "message": "No devices added. Airtest device not available."}

                    # Try to find the image on screen
                    template = Template(image_path, threshold=threshold, target_pos=target_pos)
                    match_pos = exists(template)

                    if match_pos:
                        # Click the found position
                        touch(match_pos)
                        return {
                            "status": "success",
                            "message": f"Clicked image: {image_path} using AirTest API"
                        }
                    else:
                        return {
                            "status": "error",
                            "message": f"Image not found on screen: {image_path}"
                        }
                except ImportError:
                    return {
                        "status": "error",
                        "message": "AirTest API not available"
                    }
                except Exception as e:
                    self.logger.error(f"Error using Airtest API directly: {e}")
                    if "No devices added" in str(e):
                        return {"status": "error", "message": "No devices added. Please connect a device first."}
                    return {"status": "error", "message": f"Error with Airtest API: {str(e)}"}

            # Try direct OpenCV approach as a last resort
            else:
                try:
                    self.logger.info(f"Trying direct OpenCV approach for image recognition: {image_path}")
                    import cv2
                    import numpy as np
                    from PIL import Image
                    import io
                    import base64

                    # Take a screenshot using Appium
                    if hasattr(self.controller, 'driver') and self.controller.driver:
                        # Get device dimensions first
                        device_width = None
                        device_height = None
                        if hasattr(self.controller, 'device_dimensions') and self.controller.device_dimensions:
                            device_width = self.controller.device_dimensions.get('width')
                            device_height = self.controller.device_dimensions.get('height')
                            self.logger.info(f"Device dimensions: {device_width}x{device_height}")

                        # Get screenshot as base64
                        screenshot_base64 = self.controller.driver.get_screenshot_as_base64()
                        screenshot_data = base64.b64decode(screenshot_base64)

                        # Convert to PIL Image first
                        screenshot_pil = Image.open(io.BytesIO(screenshot_data))
                        original_size = screenshot_pil.size
                        self.logger.info(f"Original screenshot size: {original_size[0]}x{original_size[1]}")

                        # DO NOT resize the screenshot - use original dimensions
                        self.logger.info(f"Using original screenshot dimensions: {original_size[0]}x{original_size[1]}")
                        # Update device dimensions to match the actual screenshot
                        device_width = original_size[0]
                        device_height = original_size[1]

                        # Convert to OpenCV format
                        screenshot_cv = cv2.cvtColor(np.array(screenshot_pil), cv2.COLOR_RGB2BGR)

                        # Save the processed screenshot for debugging
                        debug_screenshot_path = os.path.join(os.path.dirname(image_path), 'debug_screenshot.png')
                        cv2.imwrite(debug_screenshot_path, screenshot_cv)
                        self.logger.info(f"Saved debug screenshot to {debug_screenshot_path}")

                        # Load the template image
                        template = cv2.imread(image_path)
                        if template is None:
                            self.logger.error(f"Failed to load template image: {image_path}")
                            return {"status": "error", "message": f"Failed to load template image: {image_path}"}

                        # Get template dimensions
                        h, w = template.shape[:2]
                        self.logger.info(f"Template dimensions: {w}x{h}")

                        # Try multiple template matching methods
                        methods = [
                            (cv2.TM_CCOEFF_NORMED, "TM_CCOEFF_NORMED"),
                            (cv2.TM_CCORR_NORMED, "TM_CCORR_NORMED"),
                            (cv2.TM_SQDIFF_NORMED, "TM_SQDIFF_NORMED")
                        ]

                        best_val = 0
                        best_loc = None
                        best_method = None

                        # Use a lower threshold for OpenCV matching
                        opencv_threshold = max(0.5, threshold - 0.2)  # Lower threshold by 0.2 but not below 0.5
                        self.logger.info(f"Using OpenCV threshold: {opencv_threshold} (original: {threshold})")

                        for method, method_name in methods:
                            # Perform template matching
                            result = cv2.matchTemplate(screenshot_cv, template, method)

                            # Different handling for SQDIFF (lower is better) vs others (higher is better)
                            if method == cv2.TM_SQDIFF_NORMED:
                                min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
                                curr_val = 1.0 - min_val  # Convert to same scale as other methods
                                curr_loc = min_loc
                            else:
                                min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
                                curr_val = max_val
                                curr_loc = max_loc

                            self.logger.info(f"Template matching with {method_name}: {curr_val}")

                            if curr_val > best_val:
                                best_val = curr_val
                                best_loc = curr_loc
                                best_method = method_name

                        self.logger.info(f"Best template matching result: {best_val} with method {best_method} (threshold: {opencv_threshold})")

                        if best_val >= opencv_threshold:
                            # Match found, calculate center coordinates
                            x = best_loc[0] + w // 2
                            y = best_loc[1] + h // 2

                            self.logger.info(f"Found image at ({x}, {y}) in original screenshot")

                            # Validate coordinates against device dimensions
                            if device_width and device_height:
                                if x >= original_size[0] or y >= original_size[1]:
                                    self.logger.warning(f"Coordinates ({x}, {y}) are outside screenshot bounds {original_size[0]}x{original_size[1]}")

                                    # Clamp coordinates to screenshot bounds
                                    x = min(x, original_size[0] - 1)
                                    y = min(y, original_size[1] - 1)
                                    self.logger.info(f"Clamped coordinates to ({x}, {y})")

                                # Only scale if dimensions are different and scaling is needed
                                if device_width != original_size[0] or device_height != original_size[1]:
                                    # Calculate scaling factors
                                    scale_x = device_width / original_size[0]
                                    scale_y = device_height / original_size[1]

                                    # Apply scaling
                                    original_x, original_y = x, y
                                    x = int(x * scale_x)
                                    y = int(y * scale_y)

                                    # Ensure coordinates are within device bounds after scaling
                                    x = min(x, device_width - 1)
                                    y = min(y, device_height - 1)

                                    self.logger.info(f"Scaled coordinates from ({original_x}, {original_y}) to ({x}, {y}) for device dimensions {device_width}x{device_height}")

                            # Create a debug image showing the match
                            debug_match_path = os.path.join(os.path.dirname(image_path), 'debug_match.png')
                            debug_img = screenshot_cv.copy()
                            cv2.rectangle(debug_img, best_loc, (best_loc[0] + w, best_loc[1] + h), (0, 255, 0), 2)
                            cv2.circle(debug_img, (x, y), 5, (0, 0, 255), -1)
                            cv2.imwrite(debug_match_path, debug_img)
                            self.logger.info(f"Saved debug match image to {debug_match_path}")

                            self.logger.info(f"Tapping at ({x}, {y}) using OpenCV image recognition")

                            # Tap at the center using Appium
                            self.controller.driver.tap([(int(x), int(y))])

                            return {
                                "status": "success",
                                "message": f"Clicked image at ({x}, {y}) using OpenCV image recognition"
                            }
                        else:
                            return {
                                "status": "error",
                                "message": f"Image not found with OpenCV (best match: {best_val}, threshold: {opencv_threshold})"
                            }
                    else:
                        return {
                            "status": "error",
                            "message": "No Appium driver available for screenshot"
                        }
                except ImportError as e:
                    self.logger.error(f"OpenCV not available: {e}")
                    return {
                        "status": "error",
                        "message": f"OpenCV not available: {str(e)}"
                    }
                except Exception as e:
                    self.logger.error(f"Error using OpenCV for image recognition: {e}")
                    return {
                        "status": "error",
                        "message": f"Error using OpenCV for image recognition: {str(e)}"
                    }

        except Exception as e:
            self.logger.error(f"Error executing click image action: {e}")
            error_msg = str(e)
            if "No devices added" in error_msg:
                return {"status": "error", "message": "No devices added. Please connect a device first."}
            return {"status": "error", "message": f"Click image action failed: {error_msg}"}

    def _has_airtest_support(self):
        """Check if AirTest is available in the environment"""
        try:
            import airtest
            return True
        except ImportError:
            return False