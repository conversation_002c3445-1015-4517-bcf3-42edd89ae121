from .base_action import BaseAction
from selenium.webdriver.support.ui import <PERSON><PERSON><PERSON><PERSON><PERSON>
from selenium.webdriver.support import expected_conditions as EC
from appium.webdriver.common.appiumby import AppiumBy
from selenium.common.exceptions import TimeoutException, NoSuchElementException, StaleElementReferenceException

class CompareValueAction(BaseAction):
    """Handler for comparing element value with expected value action"""
    
    def execute(self, params):
        """
        Execute compare value action to compare element attribute with expected value
        
        Args:
            params: Dictionary containing:
                - locator_type: The type of locator to find the element ('id', 'xpath', 'accessibility_id', etc.)
                - locator_value: The value of the locator to find the element
                - attribute: The attribute to get from the element ('text', 'content-desc', etc.)
                - expected_value: The expected value to compare against
                - timeout: Optional timeout for finding the element
                
        Returns:
            dict: Result with status, message, actual value, and comparison result
        """
        if not self.controller:
            return {"status": "error", "message": "No device controller available"}
        
        locator_type = params.get('locator_type')
        locator_value = params.get('locator_value')
        attribute = params.get('attribute', 'text')
        expected_value = params.get('expected_value')
        timeout = params.get('timeout', 10)
        
        if not locator_type or not locator_value:
            return {"status": "error", "message": "Locator type and value are required"}
            
        if expected_value is None:
            return {"status": "error", "message": "Expected value is required"}
        
        try:
            # Check if we have an Appium driver
            if hasattr(self.controller, 'driver') and self.controller.driver:
                # Map locator_type to Appium's AppiumBy constants
                by_mapping = {
                    'id': AppiumBy.ID,
                    'xpath': AppiumBy.XPATH,
                    'accessibility_id': AppiumBy.ACCESSIBILITY_ID,
                    'class': AppiumBy.CLASS_NAME,
                    'name': AppiumBy.NAME,
                    'css': AppiumBy.CSS_SELECTOR,
                    'link_text': AppiumBy.LINK_TEXT,
                    'partial_link_text': AppiumBy.PARTIAL_LINK_TEXT,
                    'tag_name': AppiumBy.TAG_NAME,
                    'android': AppiumBy.ANDROID_UIAUTOMATOR,
                    'ios': AppiumBy.IOS_PREDICATE,
                    'ios_class_chain': AppiumBy.IOS_CLASS_CHAIN
                }
                
                locator_by = by_mapping.get(locator_type.lower(), AppiumBy.XPATH)
                
                # Wait for the element to be present
                try:
                    element = WebDriverWait(self.controller.driver, timeout).until(
                        EC.presence_of_element_located((locator_by, locator_value))
                    )
                    
                    # Get the specified attribute value
                    try:
                        # Try getting the attribute
                        if attribute.lower() == 'text':
                            # For text, try different methods based on platform
                            platform = getattr(self.controller, 'platform_name', '').lower()
                            
                            if platform == 'android':
                                # For Android, try both text attribute and getText() method
                                actual_value = element.text
                                if not actual_value:
                                    actual_value = element.get_attribute('text')
                            elif platform == 'ios':
                                # For iOS, use "value" or "label" attribute
                                actual_value = element.text
                                if not actual_value:
                                    actual_value = element.get_attribute('value')
                                if not actual_value:
                                    actual_value = element.get_attribute('label')
                            else:
                                # Generic fallback
                                actual_value = element.text
                        else:
                            # For other attributes, use get_attribute
                            actual_value = element.get_attribute(attribute)
                            
                        # Convert both values to string for comparison
                        str_expected = str(expected_value)
                        str_actual = str(actual_value) if actual_value is not None else ""
                        
                        # Compare values
                        matches = str_actual == str_expected
                        
                        if matches:
                            result_msg = f"Value matches: '{actual_value}'"
                            self.logger.info(f"Value match success: expected='{expected_value}', actual='{actual_value}'")
                            return {
                                "status": "success", 
                                "message": result_msg, 
                                "value": actual_value,
                                "matches": True
                            }
                        else:
                            result_msg = f"Value mismatch! Expected: '{expected_value}', Actual: '{actual_value}'"
                            self.logger.warning(f"Value comparison failed: {result_msg}")
                            return {
                                "status": "error", 
                                "message": result_msg, 
                                "value": actual_value,
                                "expected": expected_value,
                                "matches": False
                            }
                    
                    except Exception as attr_err:
                        self.logger.error(f"Error getting attribute '{attribute}' from element: {attr_err}")
                        return {"status": "error", "message": f"Error getting attribute '{attribute}': {str(attr_err)}"}
                
                except TimeoutException:
                    self.logger.error(f"Element with {locator_type}='{locator_value}' not found within {timeout} seconds")
                    return {"status": "error", "message": f"Element not found within {timeout} seconds"}
                
                except Exception as wait_err:
                    self.logger.error(f"Error waiting for element: {wait_err}")
                    return {"status": "error", "message": f"Error waiting for element: {str(wait_err)}"}
            else:
                return {"status": "error", "message": "Controller does not have an active driver"}
                
        except Exception as e:
            self.logger.error(f"Error executing compare value action: {e}")
            return {"status": "error", "message": f"Compare value action failed: {str(e)}"} 