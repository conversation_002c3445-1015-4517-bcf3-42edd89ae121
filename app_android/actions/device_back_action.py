from .base_action import BaseAction

class DeviceBackAction(BaseAction):
    """Handler for device back button action (Android only)"""
    
    def execute(self, params):
        """
        Execute device back button action
        
        Args:
            params: Dictionary containing no specific parameters
                
        Returns:
            dict: Result with status and message
        """
        if not self.controller:
            return {"status": "error", "message": "No device controller available"}
        
        try:
            # Get the platform if available
            platform = getattr(self.controller, 'platform_name', None)
            
            # Verify this is Android
            if not platform or platform.lower() != 'android':
                return {"status": "error", "message": "Device back action is only supported on Android devices"}
            
            # Check if we have an Appium driver
            if hasattr(self.controller, 'driver') and self.controller.driver:
                # Method 1: Using the back() method
                if hasattr(self.controller.driver, 'back'):
                    self.controller.driver.back()
                    self.logger.info("Pressed back button using driver.back()")
                    return {"status": "success", "message": "Pressed device back button"}
                
                # Method 2: Using press_keycode for Android
                elif hasattr(self.controller.driver, 'press_keycode'):
                    # Android keycode for BACK is 4
                    self.controller.driver.press_keycode(4)
                    self.logger.info("Pressed back button using keycode 4")
                    return {"status": "success", "message": "Pressed device back button"}
                
                # Method 3: Last resort - execute ADB shell command
                else:
                    # Execute ADB shell command to press back
                    if hasattr(self.controller.driver, 'execute_script'):
                        self.controller.driver.execute_script('mobile: shell', {
                            'command': 'input',
                            'args': ['keyevent', '4']
                        })
                        self.logger.info("Pressed back button using ADB shell command")
                        return {"status": "success", "message": "Pressed device back button using shell command"}
                    else:
                        return {"status": "error", "message": "No suitable method found to press back button"}
            else:
                return {"status": "error", "message": "Controller does not have an active driver"}
                
        except Exception as e:
            self.logger.error(f"Error executing device back action: {e}")
            return {"status": "error", "message": f"Device back action failed: {str(e)}"} 