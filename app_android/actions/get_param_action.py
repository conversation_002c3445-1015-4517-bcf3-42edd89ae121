from .base_action import BaseAction
import sys
from pathlib import Path

# Add parent directory to path to import config
parent_dir = Path(__file__).resolve().parent.parent.parent
if str(parent_dir) not in sys.path:
    sys.path.insert(0, str(parent_dir))

class GetParamAction(BaseAction):
    """Action to retrieve a parameter value from Global Values"""
    
    def execute(self, params):
        """
        Execute get parameter action
        
        Args:
            params: Dictionary containing:
                - param_name: Name of the parameter to retrieve
                
        Returns:
            dict: Result with status, message, and the retrieved value
        """
        if not self.controller:
            return {"status": "error", "message": "No device controller available"}
        
        param_name = params.get('param_name')
        
        if not param_name:
            return {"status": "error", "message": "Missing parameter name"}
        
        try:
            # Import config to access GLOBAL_VALUES
            import config
            
            # Check if GLOBAL_VALUES exists and has the parameter
            if hasattr(config, 'GLOBAL_VALUES') and isinstance(config.GLOBAL_VALUES, dict):
                if param_name in config.GLOBAL_VALUES:
                    value = config.GLOBAL_VALUES[param_name]
                    return {
                        "status": "success", 
                        "message": f"Parameter '{param_name}' value: {value}",
                        "value": value
                    }
                else:
                    return {
                        "status": "error", 
                        "message": f"Parameter '{param_name}' not found in Global Values"
                    }
            else:
                return {
                    "status": "error", 
                    "message": "Global Values not defined in configuration"
                }
                
        except Exception as e:
            self.logger.error(f"Error executing get parameter action: {e}")
            return {"status": "error", "message": f"Get parameter action failed: {str(e)}"} 