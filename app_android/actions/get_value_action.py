from .base_action import BaseAction
from selenium.webdriver.support.ui import <PERSON><PERSON><PERSON><PERSON><PERSON>
from selenium.webdriver.support import expected_conditions as EC
from appium.webdriver.common.appiumby import AppiumBy
from selenium.common.exceptions import TimeoutException, NoSuchElementException, StaleElementReferenceException

class GetValueAction(BaseAction):
    """Handler for getting element value action"""
    
    def execute(self, params):
        """
        Execute get value action to extract attribute values from elements
        
        Args:
            params: Dictionary containing:
                - locator_type: The type of locator to find the element ('id', 'xpath', 'accessibility_id', etc.)
                - locator_value: The value of the locator to find the element
                - attribute: The attribute to get from the element ('text', 'content-desc', etc.)
                - timeout: Optional timeout for finding the element
                
        Returns:
            dict: Result with status, message, and the extracted value
        """
        if not self.controller:
            return {"status": "error", "message": "No device controller available"}
        
        locator_type = params.get('locator_type')
        locator_value = params.get('locator_value')
        attribute = params.get('attribute', 'text')
        timeout = params.get('timeout', 10)
        
        if not locator_type or not locator_value:
            return {"status": "error", "message": "Locator type and value are required"}
        
        try:
            # Check if we have an Appium driver
            if hasattr(self.controller, 'driver') and self.controller.driver:
                # Map locator_type to Appium's AppiumBy constants
                by_mapping = {
                    'id': AppiumBy.ID,
                    'xpath': AppiumBy.XPATH,
                    'accessibility_id': AppiumBy.ACCESSIBILITY_ID,
                    'class': AppiumBy.CLASS_NAME,
                    'name': AppiumBy.NAME,
                    'css': AppiumBy.CSS_SELECTOR,
                    'link_text': AppiumBy.LINK_TEXT,
                    'partial_link_text': AppiumBy.PARTIAL_LINK_TEXT,
                    'tag_name': AppiumBy.TAG_NAME,
                    'android': AppiumBy.ANDROID_UIAUTOMATOR,
                    'ios': AppiumBy.IOS_PREDICATE,
                    'ios_class_chain': AppiumBy.IOS_CLASS_CHAIN
                }
                
                locator_by = by_mapping.get(locator_type.lower(), AppiumBy.XPATH)
                
                # Wait for the element to be present
                try:
                    element = WebDriverWait(self.controller.driver, timeout).until(
                        EC.presence_of_element_located((locator_by, locator_value))
                    )
                    
                    # Get the specified attribute
                    # In mobile testing, common attributes are 'text', 'content-desc', 'name', 'value'
                    try:
                        # Try getting the attribute
                        if attribute.lower() == 'text':
                            # For text, try different methods based on platform
                            platform = getattr(self.controller, 'platform_name', '').lower()
                            
                            if platform == 'android':
                                # For Android, try both text attribute and getText() method
                                value = element.text
                                if not value:
                                    value = element.get_attribute('text')
                            elif platform == 'ios':
                                # For iOS, use "value" or "label" attribute
                                value = element.text
                                if not value:
                                    value = element.get_attribute('value')
                                if not value:
                                    value = element.get_attribute('label')
                            else:
                                # Generic fallback
                                value = element.text
                        else:
                            # For other attributes, use get_attribute
                            value = element.get_attribute(attribute)
                        
                        # Log and return the value
                        self.logger.info(f"Retrieved value '{value}' from element with {locator_type}='{locator_value}', attribute='{attribute}'")
                        return {"status": "success", "message": f"Retrieved value: '{value}'", "value": value}
                    
                    except Exception as attr_err:
                        self.logger.error(f"Error getting attribute '{attribute}' from element: {attr_err}")
                        return {"status": "error", "message": f"Error getting attribute '{attribute}': {str(attr_err)}"}
                
                except TimeoutException:
                    self.logger.error(f"Element with {locator_type}='{locator_value}' not found within {timeout} seconds")
                    return {"status": "error", "message": f"Element not found within {timeout} seconds"}
                
                except Exception as wait_err:
                    self.logger.error(f"Error waiting for element: {wait_err}")
                    return {"status": "error", "message": f"Error waiting for element: {str(wait_err)}"}
            else:
                return {"status": "error", "message": "Controller does not have an active driver"}
                
        except Exception as e:
            self.logger.error(f"Error executing get value action: {e}")
            return {"status": "error", "message": f"Get value action failed: {str(e)}"} 