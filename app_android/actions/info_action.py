from .base_action import BaseAction
import logging
import re
import os

class InfoAction(BaseAction):
    """Handler for logging information with environment and global variable parsing"""

    def execute(self, params):
        """
        Execute info action - logs information with variable parsing

        Args:
            params: Dictionary containing:
                - text: The information text to log (with variable placeholders)

        Returns:
            dict: Result with status and message
        """
        if not params.get('text'):
            return {"status": "error", "message": "No information text provided"}

        original_text = params.get('text', '')
        
        # Parse environment and global variables
        parsed_text = self.parse_variables(original_text)
        
        # Log the information
        self.logger.info(f"INFO: {parsed_text}")
        
        return {
            "status": "success", 
            "message": f"Information logged: {parsed_text}",
            "info_text": parsed_text,
            "original_text": original_text
        }

    def parse_variables(self, text):
        """
        Parse environment and global variables in the text
        
        Args:
            text: Text containing variable placeholders like env[variable_name] or global[variable_name]
            
        Returns:
            str: Text with variables replaced by their values
        """
        if not text:
            return text
            
        parsed_text = text
        
        # Parse environment variables: env[variable_name]
        env_pattern = r'env\[([^\]]+)\]'
        env_matches = re.findall(env_pattern, text)
        
        for var_name in env_matches:
            # Try to get the environment variable value
            env_value = self.get_environment_variable(var_name)
            if env_value is not None:
                parsed_text = parsed_text.replace(f'env[{var_name}]', str(env_value))
                self.logger.info(f"Replaced env[{var_name}] with: {env_value}")
            else:
                # Keep the placeholder if variable not found
                self.logger.warning(f"Environment variable '{var_name}' not found")
                parsed_text = parsed_text.replace(f'env[{var_name}]', f'<env[{var_name}] not found>')
        
        # Parse global variables: global[variable_name]
        global_pattern = r'global\[([^\]]+)\]'
        global_matches = re.findall(global_pattern, text)
        
        for var_name in global_matches:
            # Try to get the global variable value
            global_value = self.get_global_variable(var_name)
            if global_value is not None:
                parsed_text = parsed_text.replace(f'global[{var_name}]', str(global_value))
                self.logger.info(f"Replaced global[{var_name}] with: {global_value}")
            else:
                # Keep the placeholder if variable not found
                self.logger.warning(f"Global variable '{var_name}' not found")
                parsed_text = parsed_text.replace(f'global[{var_name}]', f'<global[{var_name}] not found>')
        
        return parsed_text

    def get_environment_variable(self, var_name):
        """
        Get environment variable value
        
        Args:
            var_name: Name of the environment variable
            
        Returns:
            str or None: Variable value or None if not found
        """
        try:
            # First try to get from OS environment variables
            env_value = os.environ.get(var_name)
            if env_value is not None:
                return env_value
            
            # Try to get from app's environment variables if available
            if hasattr(self, 'app') and hasattr(self.app, 'environment_variables'):
                return self.app.environment_variables.get(var_name)
            
            # Try to get from controller's environment variables if available
            if hasattr(self.controller, 'environment_variables'):
                return self.controller.environment_variables.get(var_name)
                
            return None
        except Exception as e:
            self.logger.error(f"Error getting environment variable '{var_name}': {e}")
            return None

    def get_global_variable(self, var_name):
        """
        Get global variable value
        
        Args:
            var_name: Name of the global variable
            
        Returns:
            str or None: Variable value or None if not found
        """
        try:
            # Try to get from app's global variables if available
            if hasattr(self, 'app') and hasattr(self.app, 'global_variables'):
                return self.app.global_variables.get(var_name)
            
            # Try to get from controller's global variables if available
            if hasattr(self.controller, 'global_variables'):
                return self.controller.global_variables.get(var_name)
                
            return None
        except Exception as e:
            self.logger.error(f"Error getting global variable '{var_name}': {e}")
            return None
