from .base_action import BaseAction
import logging

class LaunchAppAction(BaseAction):
    """Handler for launching an app"""

    def execute(self, params):
        """
        Launch an app

        Args:
            params: Dictionary containing:
                - package_id: ID of the app to launch

        Returns:
            dict: Result with status and message
        """
        self.logger.info(f"[HOOK_DEBUG] LaunchAppAction.execute called with params: {params}")
        
        if not self.controller:
            self.logger.error("[HOOK_DEBUG] No device controller available")
            return {"status": "error", "message": "No device controller available"}

        # Get the package ID from params
        package_id = params.get('package_id')
        
        if not package_id:
            self.logger.error("[HOOK_DEBUG] No package ID provided")
            return {"status": "error", "message": "No package ID provided"}

        self.logger.info(f"Launching app: {package_id}")
        
        try:
            # Use the device controller to launch the app
            result = self.controller.launch_app(package_id)
            
            if result:
                return {"status": "success", "message": f"App {package_id} launched successfully"}
            else:
                return {"status": "error", "message": f"Failed to launch app {package_id}"}
        except Exception as e:
            self.logger.error(f"Error launching app {package_id}: {str(e)}")
            return {"status": "error", "message": f"Error launching app: {str(e)}"}
