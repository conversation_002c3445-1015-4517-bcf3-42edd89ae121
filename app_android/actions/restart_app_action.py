from .base_action import BaseAction
import logging

class RestartAppAction(BaseAction):
    """Handler for restarting an app"""

    def execute(self, params):
        """
        Restart an app

        Args:
            params: Dictionary containing:
                - package_id: ID of the app to restart

        Returns:
            dict: Result with status and message
        """
        self.logger.info(f"[HOOK_DEBUG] RestartAppAction.execute called with params: {params}")
        
        if not self.controller:
            self.logger.error("[HOOK_DEBUG] No device controller available")
            return {"status": "error", "message": "No device controller available"}

        # Get the package ID from params
        package_id = params.get('package_id')
        
        if not package_id:
            self.logger.error("[HOOK_DEBUG] No package ID provided")
            return {"status": "error", "message": "No package ID provided"}

        self.logger.info(f"Restarting app: {package_id}")
        
        try:
            # First terminate the app
            terminate_result = self.controller.terminate_app(package_id)
            if not terminate_result:
                self.logger.warning(f"Failed to terminate app {package_id} before restarting")
            
            # Then launch the app
            launch_result = self.controller.launch_app(package_id)
            
            if launch_result:
                return {"status": "success", "message": f"App {package_id} restarted successfully"}
            else:
                return {"status": "error", "message": f"Failed to restart app {package_id}"}
        except Exception as e:
            self.logger.error(f"Error restarting app {package_id}: {str(e)}")
            return {"status": "error", "message": f"Error restarting app: {str(e)}"}
