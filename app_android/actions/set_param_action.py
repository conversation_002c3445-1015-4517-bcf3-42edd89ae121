from .base_action import BaseAction
import sys
from pathlib import Path
import sys
import os
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
utils_dir = os.path.join(parent_dir, 'utils')
if utils_dir not in sys.path:
    sys.path.insert(0, utils_dir)
from global_values_db import global_values_db

# Add parent directory to path to import config
parent_dir = Path(__file__).resolve().parent.parent.parent
if str(parent_dir) not in sys.path:
    sys.path.insert(0, str(parent_dir))

class SetParamAction(BaseAction):
    """Action to set a parameter value in Global Values"""

    def execute(self, params):
        """
        Execute set parameter action

        Args:
            params: Dictionary containing:
                - param_name: Name of the parameter to set
                - param_value: Value to set for the parameter

        Returns:
            dict: Result with status and message
        """
        if not self.controller:
            return {"status": "error", "message": "No device controller available"}

        param_name = params.get('param_name')
        param_value = params.get('param_value')

        if not param_name:
            return {"status": "error", "message": "Missing parameter name"}

        if param_value is None:
            return {"status": "error", "message": "Missing parameter value"}

        try:
            # Convert param_value to the appropriate type if possible
            try:
                # Try to convert to number if it looks like one
                if param_value.lower() == 'true':
                    typed_value = True
                elif param_value.lower() == 'false':
                    typed_value = False
                elif param_value.isdigit():
                    typed_value = int(param_value)
                elif '.' in param_value and all(part.isdigit() for part in param_value.split('.', 1)):
                    typed_value = float(param_value)
                else:
                    typed_value = param_value
            except (AttributeError, ValueError):
                # Not a string or conversion failed, use as is
                typed_value = param_value

            # Save the value to the database
            if global_values_db.set_value(param_name, typed_value):
                self.logger.info(f"Parameter '{param_name}' set to '{typed_value}' in database")
            else:
                self.logger.error(f"Failed to set parameter '{param_name}' in database")
                return {"status": "error", "message": f"Failed to save parameter to database"}

            # For backward compatibility, also update config.py
            try:
                # Import config to access GLOBAL_VALUES
                import config

                # Initialize GLOBAL_VALUES if not exists
                if not hasattr(config, 'GLOBAL_VALUES'):
                    config.GLOBAL_VALUES = {}

                # Update the value in memory
                config.GLOBAL_VALUES[param_name] = typed_value

                # Format the value for insertion based on type
                if isinstance(typed_value, str):
                    value_str = f"'{typed_value}'"
                elif isinstance(typed_value, bool):
                    value_str = str(typed_value)
                else:
                    value_str = str(typed_value)

                # Get all global values from the database
                all_global_values = global_values_db.get_all_values()

                # Update config.py file with all global values
                config_path = Path(__file__).resolve().parent.parent.parent / 'config.py'
                if config_path.exists():
                    with open(config_path, 'r') as f:
                        content = f.read()

                    # Check if GLOBAL_VALUES already exists in the file
                    if 'GLOBAL_VALUES' in content:
                        # Update the entire GLOBAL_VALUES section
                        global_values_pattern = r"GLOBAL_VALUES\s*=\s*\{[^\}]*\}"
                        global_values_str = "GLOBAL_VALUES = {\n"

                        # Add all values from the database
                        for key, value in all_global_values.items():
                            if isinstance(value, str):
                                global_values_str += f"    '{key}': '{value}',\n"
                            else:
                                global_values_str += f"    '{key}': {value},\n"

                        global_values_str += "}"
                        content = re.sub(global_values_pattern, global_values_str, content)

                    # Write the updated content back to the file
                    with open(config_path, 'w') as f:
                        f.write(content)

                    self.logger.info(f"Updated config.py with global values for backward compatibility")
            except Exception as e:
                self.logger.warning(f"Error updating config.py (non-critical): {str(e)}")
                # Continue even if config.py update fails, as the database is the primary storage now

            return {
                "status": "success",
                "message": f"Parameter '{param_name}' set to '{typed_value}'"
            }

        except Exception as e:
            self.logger.error(f"Error executing set parameter action: {e}")
            return {"status": "error", "message": f"Set parameter action failed: {str(e)}"}