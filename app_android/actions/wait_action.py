from .base_action import BaseAction
import time
import logging

class WaitAction(BaseAction):
    """Handler for wait actions"""

    def execute(self, params):
        """
        Execute a wait action - wait for the specified duration

        Args:
            params: Dictionary containing:
                - duration: Duration to wait in seconds (default: 1)
                - time: Duration to wait in milliseconds (alternative to duration)

        Returns:
            dict: Result with status and message
        """
        if not self.controller:
            return {"status": "error", "message": "No device controller available"}

        # Get duration in seconds
        # First check for 'duration' parameter (in seconds)
        duration = params.get('duration')
        
        # If duration is not provided, check for 'time' parameter (in milliseconds)
        if duration is None and 'time' in params:
            # Convert milliseconds to seconds
            duration = float(params.get('time', 1000)) / 1000.0
        
        # Default to 1 second if no duration is provided
        if duration is None:
            duration = 1.0
            
        # Ensure duration is a float
        try:
            duration = float(duration)
        except (ValueError, TypeError):
            return {"status": "error", "message": f"Invalid duration value: {duration}"}
            
        # Log the wait action
        self.logger.info(f"Waiting for {duration} seconds")
        
        try:
            # Sleep for the specified duration
            time.sleep(duration)
            return {"status": "success", "message": f"Waited for {duration} seconds"}
        except Exception as e:
            self.logger.error(f"Error during wait: {e}")
            return {"status": "error", "message": f"Error during wait: {str(e)}"}
