{"environments": [{"id": "env_dev", "name": "Development", "variables": [{"key": "baseUrl", "type": "default", "value": "https://dev-api.example.com"}, {"key": "username", "type": "default", "value": "testuser"}, {"key": "password", "type": "default", "value": "testpass"}]}, {"id": "env_test", "name": "Testing", "variables": [{"key": "baseUrl", "type": "default", "value": "https://test-api.example.com"}, {"key": "username", "type": "default", "value": "qauser"}, {"key": "password", "type": "default", "value": "qapass"}]}], "exported_at": 1748780552.29235, "globalVariables": [{"key": "apiVersion", "type": "default", "value": "v1"}]}