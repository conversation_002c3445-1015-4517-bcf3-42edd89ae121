"""
API routes for random data generation
"""

from flask import Blueprint, jsonify, request
import sys
from pathlib import Path

# Add the app directory to the path
app_dir = str(Path(__file__).resolve().parent.parent)
if app_dir not in sys.path:
    sys.path.insert(0, app_dir)

from app_android.utils.random_data_generator import get_generator_options, generate_data
import logging

logger = logging.getLogger(__name__)

# Create a Blueprint for random data routes
random_data_bp = Blueprint('random_data', __name__)

@random_data_bp.route('/api/random_data/generators', methods=['GET'])
def get_generators():
    """
    Get a list of available random data generators
    
    Returns:
        JSON: List of available generators with id, name, and description
    """
    try:
        generators = get_generator_options()
        return jsonify({
            "status": "success",
            "generators": generators
        })
    except Exception as e:
        logger.error(f"Error getting generators: {str(e)}")
        return jsonify({
            "status": "error",
            "message": f"Error getting generators: {str(e)}"
        }), 500

@random_data_bp.route('/api/random_data/generate', methods=['POST'])
def generate_random_data():
    """
    Generate random data using the specified generator
    
    Request JSON:
        generator_id (str): ID of the generator to use
        
    Returns:
        JSON: Generated data or error message
    """
    try:
        data = request.get_json()
        
        if not data or 'generator_id' not in data:
            return jsonify({
                "status": "error",
                "message": "Missing generator_id parameter"
            }), 400
            
        generator_id = data['generator_id']
        generated_data = generate_data(generator_id)
        
        return jsonify({
            "status": "success",
            "data": generated_data
        })
    except Exception as e:
        logger.error(f"Error generating random data: {str(e)}")
        return jsonify({
            "status": "error",
            "message": f"Error generating random data: {str(e)}"
        }), 500
