/* Styles for action execution visual indicators */
.list-group-item.executing {
    background-color: #e9f5ff !important;
    border-left: 4px solid #0d6efd !important;
    animation: pulse-blue 2s infinite;
}
.list-group-item.success:not(.error) {
    background-color: #e8f7ee !important;
    border-left: 4px solid #198754 !important;
}
.list-group-item.error:not(.success) {
    background-color: #feeceb !important;
    border-left: 4px solid #dc3545 !important;
}
/* When both classes are present, success should take precedence */
.list-group-item.success.error {
    background-color: #e8f7ee !important;
    border-left: 4px solid #198754 !important;
}
@keyframes pulse-blue {
    0% { box-shadow: 0 0 0 0 rgba(13, 110, 253, 0.2); }
    70% { box-shadow: 0 0 0 6px rgba(13, 110, 253, 0); }
    100% { box-shadow: 0 0 0 0 rgba(13, 110, 253, 0); }
}

/* Disabled action styles */
.list-group-item.action-disabled {
    background-color: #f8f9fa !important;
    opacity: 0.6;
    border-left: 4px solid #6c757d !important;
}

.list-group-item.action-disabled .action-content {
    text-decoration: line-through;
    color: #6c757d;
}

.list-group-item.action-disabled .badge {
    opacity: 0.7;
}