/* Validation result styles */
.validation-result {
    min-height: 30px;
    transition: all 0.3s ease;
}

.validation-result .alert {
    font-size: 0.9rem;
    margin-bottom: 0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.validation-result .alert i {
    margin-right: 5px;
}

/* Validation states */
.validating {
    opacity: 0.8;
    transition: opacity 0.3s ease;
}

.validation-complete {
    opacity: 1;
}

/* Highlight animation for validation results */
.highlight-result {
    animation: pulse 1.5s ease;
}

@keyframes pulse {
    0% { transform: scale(1); }
    10% { transform: scale(1.03); }
    20% { transform: scale(1); }
    30% { transform: scale(1.02); }
    40% { transform: scale(1); }
    100% { transform: scale(1); }
}

/* Fade-in animation for alerts */
.fade-in {
    animation: fadeIn 0.4s ease-in;
}

@keyframes fadeIn {
    0% { opacity: 0; }
    100% { opacity: 1; }
}

/* Add a border to validation success to make it more noticeable */
.validation-result .alert-success {
    border-left: 4px solid #198754;
}

.validation-result .alert-danger {
    border-left: 4px solid #dc3545;
}

.validation-result .alert-warning {
    border-left: 4px solid #ffc107;
}

/* Cleanup Steps Styles */
.cleanup-steps-container {
    background-color: #fff8e1;
    border: 1px solid #ffc107;
    border-radius: 8px;
    padding: 12px;
    margin-top: 8px;
}

.cleanup-steps-header {
    border-bottom: 1px solid #ffc107;
    padding-bottom: 8px;
    margin-bottom: 12px;
}

.cleanup-step-item {
    background-color: #ffffff;
    border: 1px solid #e0e0e0;
    transition: all 0.3s ease;
}

.cleanup-step-item:hover {
    background-color: #f8f9fa;
}

.cleanup-step-executing {
    background-color: #e3f2fd !important;
    border-color: #2196f3 !important;
    box-shadow: 0 0 8px rgba(33, 150, 243, 0.3);
}

.cleanup-step-success {
    background-color: #e8f5e8 !important;
    border-color: #4caf50 !important;
}

.cleanup-step-error {
    background-color: #ffebee !important;
    border-color: #f44336 !important;
}

.cleanup-step-warning {
    background-color: #fff3e0 !important;
    border-color: #ff9800 !important;
}

.cleanup-step-content {
    flex: 1;
    display: flex;
    align-items: center;
}

.cleanup-step-description {
    font-size: 0.9rem;
    color: #333;
}

.cleanup-step-status {
    min-width: 24px;
    text-align: center;
}

/* Responsive adjustments for cleanup steps */
@media (max-width: 768px) {
    .cleanup-steps-container {
        padding: 8px;
    }

    .cleanup-step-item {
        padding: 8px !important;
    }

    .cleanup-step-description {
        font-size: 0.8rem;
    }
}