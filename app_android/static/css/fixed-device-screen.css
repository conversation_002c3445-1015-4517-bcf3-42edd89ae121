/* Fixed Device Screen Styles */

/* This class will be added to the device screen container during execution */
.fixed-device-screen-container {
    position: fixed;
    top: 10px; /* Reduced from 20px to 10px to give more vertical space */
    left: 20px;
    z-index: 9990;
    background-color: rgba(0, 0, 0, 0.7);
    border-radius: 10px;
    padding: 5px; /* Reduced from 10px to 5px to give more space to the device screen */
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    max-width: 400px; /* Increased from 350px to 400px for better visibility */
    transition: all 0.3s ease;
    max-height: calc(100vh - 10px); /* Increased from 100vh-20px to 100vh-10px to show more of the device screen */
    display: flex;
    flex-direction: column;
}

/* Status indicator for current action */
.fixed-device-screen-status {
    position: absolute;
    bottom: 5px; /* Reduced from 10px to 5px to give more space to the device screen */
    left: 10px;
    right: 10px;
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 6px 10px; /* Reduced padding to make it more compact */
    border-radius: 5px;
    font-size: 12px;
    z-index: 9991;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Style for the device screen image when fixed */
.fixed-device-screen-container img.device-screen {
    max-width: 100%;
    max-height: 95vh; /* Increased from 90vh to 95vh to show more of the device screen */
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
}

/* Header for the fixed device screen */
.fixed-device-screen-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 3px 10px; /* Reduced from 5px to 3px */
    background-color: rgba(0, 0, 0, 0.8);
    border-radius: 5px 5px 0 0;
    margin-bottom: 5px; /* Reduced from 10px to 5px */
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.fixed-device-screen-title {
    color: white;
    font-size: 12px; /* Reduced from 14px to 12px */
    font-weight: bold;
    margin: 0;
}

.fixed-device-screen-close {
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.7);
    cursor: pointer;
    font-size: 16px;
    padding: 0;
}

.fixed-device-screen-close:hover {
    color: white;
}

/* Make the fixed device screen draggable */
.fixed-device-screen-header {
    cursor: move;
}

/* Resize handle */
.fixed-device-screen-resize {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 15px;
    height: 15px;
    cursor: nwse-resize;
    background: linear-gradient(135deg, transparent 50%, rgba(255, 255, 255, 0.5) 50%);
    border-radius: 0 0 5px 0;
}

/* Overlay canvas positioning */
.fixed-device-screen-container #overlayCanvas {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10;
}

/* Loading overlay positioning */
.fixed-device-screen-container #loadingOverlay {
    border-radius: 8px;
}
