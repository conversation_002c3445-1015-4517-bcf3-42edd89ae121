/* Modern UI Styles for Mobile App Automation Tool */

:root {
  --primary: #4f46e5;
  --primary-dark: #4338ca;
  --secondary: #8b5cf6;
  --success: #10b981;
  --danger: #ef4444;
  --warning: #f59e0b;
  --info: #3b82f6;
  --light: #f9fafb;
  --dark: #1f2937;
  --gray: #6b7280;
  --gray-light: #e5e7eb;
  --border-radius: 0.5rem;
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --transition: all 0.2s ease-in-out;
}

/* Base Styles */
body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  background-color: #f8fafc;
  color: var(--dark);
  line-height: 1.5;
}

/* App Header - Improved */
.app-header {
  padding: 1.5rem 0;
  background-color: white;
  border-bottom: 1px solid var(--border-color);
  margin-bottom: 2rem;
  text-align: center;
}

.app-header::before {
  content: "";
  position: absolute;
  top: -50%;
  right: -50%;
  width: 100%;
  height: 200%;
  background: rgba(255, 255, 255, 0.1);
  transform: rotate(35deg);
  pointer-events: none;
}

.app-header h2 {
  font-weight: 800;
  font-size: 2.2rem;
  margin-bottom: 0.5rem;
  letter-spacing: -0.025em;
  position: relative;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.app-header p {
  opacity: 0.95;
  font-weight: 500;
  font-size: 1.1rem;
  margin: 0 auto;
  position: relative;
}

/* Updated Modern Tab Styles with Sliding Indicator */
.nav-tabs {
  border-bottom: 1px solid var(--gray-light);
  margin-bottom: 1.5rem;
  display: flex;
  gap: 0.5rem;
  padding: 0 0.25rem;
  position: relative;
}

/* Sliding indicator for active tab */
.nav-tabs::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  height: 3px;
  width: var(--slider-width, 0);
  background-color: var(--primary);
  transition: transform 0.3s ease, width 0.3s ease;
  transform: translateX(var(--slider-left, 0));
  pointer-events: none;
  border-radius: 3px 3px 0 0;
  z-index: 1;
}

.nav-tabs .nav-item {
  margin-bottom: -1px;
}

.nav-tabs .nav-link {
  border: none;
  border-bottom: 3px solid transparent;
  border-radius: 0.5rem 0.5rem 0 0;
  padding: 0.875rem 1.5rem;
  font-weight: 600;
  color: var(--gray);
  transition: all 0.25s ease;
  position: relative;
  background-color: transparent;
}

.nav-tabs .nav-link:hover,
.nav-tabs .nav-link:focus {
  color: var(--primary);
  border-color: transparent;
  background-color: rgba(79, 70, 229, 0.075);
}

.nav-tabs .nav-link.active {
  color: var(--primary);
  background-color: rgba(79, 70, 229, 0.1);
  border-bottom: 3px solid transparent;
  font-weight: 600;
}

/* Remove default indicator since we have the sliding one */
.nav-tabs .nav-link.active::after {
  display: none;
}

.nav-tabs .nav-link i {
  margin-right: 0.5rem;
  font-size: 1.1em;
  vertical-align: -0.125em;
}

/* Pill Tab Variant */
.nav-pills {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.nav-pills .nav-item {
  margin-right: 0;
}

.nav-pills .nav-link {
  border-radius: 50rem;
  padding: 0.625rem 1.5rem;
  font-weight: 600;
  color: var(--gray);
  transition: all 0.25s ease;
  background-color: transparent;
}

.nav-pills .nav-link:hover,
.nav-pills .nav-link:focus {
  color: var(--primary);
  background-color: rgba(79, 70, 229, 0.075);
}

.nav-pills .nav-link.active {
  color: white;
  background-color: var(--primary);
  box-shadow: 0 2px 8px rgba(79, 70, 229, 0.3);
}

/* Tab Content */
.tab-content {
  padding: 1rem 0;
}

.tab-content > .tab-pane {
  display: none;
}

.tab-content > .active {
  display: block;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(5px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .nav-tabs {
    overflow-x: auto;
    flex-wrap: nowrap;
    scrollbar-width: none; /* For Firefox */
    -ms-overflow-style: none; /* For Internet Explorer and Edge */
  }

  .nav-tabs::-webkit-scrollbar {
    display: none; /* For Chrome, Safari, and Opera */
  }

  .nav-tabs .nav-link {
    padding: 0.75rem 1rem;
    white-space: nowrap;
  }

  .app-header h2 {
    font-size: 1.8rem;
  }
}

/* Button-style Tabs */
.btn-tabs {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
  border: none;
  background: var(--light);
  padding: 0.375rem;
  border-radius: 0.75rem;
}

.btn-tabs .nav-link {
  border-radius: 0.5rem;
  padding: 0.625rem 1.25rem;
  font-weight: 500;
  color: var(--gray);
  border: none;
  transition: all 0.2s ease;
}

.btn-tabs .nav-link.active {
  color: var(--primary);
  background-color: white;
  box-shadow: var(--shadow-sm);
  font-weight: 600;
}

/* Vertical Tabs */
.nav-tabs-vertical {
  flex-direction: column;
  border-bottom: none;
  border-right: 1px solid var(--gray-light);
  padding-right: 0;
  width: 200px;
}

.nav-tabs-vertical .nav-item {
  margin-right: -1px;
  margin-bottom: 0.25rem;
}

.nav-tabs-vertical .nav-link {
  border: none;
  border-right: 3px solid transparent;
  border-radius: 0.5rem 0 0 0.5rem;
  text-align: left;
  padding: 0.75rem 1rem;
}

.nav-tabs-vertical .nav-link.active {
  border-bottom: none;
  border-right: 3px solid var(--primary);
}

.nav-tabs-vertical .nav-link.active::after {
  top: 0;
  right: -3px;
  bottom: 0;
  left: auto;
  width: 3px;
  height: 100%;
}

.vertical-tab-container {
  display: flex;
}

.vertical-tab-container .nav-tabs-vertical {
  flex: 0 0 200px;
}

.vertical-tab-container .tab-content {
  flex: 1;
  padding-left: 1.5rem;
}

/* Cards */
.card {
  background-color: white;
  border-radius: 0.75rem;
  border: 1px solid var(--border-color);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  transition: all 0.2s ease;
  overflow: hidden;
  margin-bottom: 1.5rem;
}

.card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.card-header {
  padding: 1.25rem;
  border-bottom: 1px solid var(--border-color);
  background-color: rgba(var(--light-rgb), 0.5);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-title {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-dark);
}

.card-body {
  padding: 1.25rem;
}

.card-footer {
  padding: 1rem 1.25rem;
  border-top: 1px solid var(--border-color);
  background-color: rgba(var(--light-rgb), 0.5);
}

/* Modern Button Styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1.25rem;
  font-weight: 500;
  border-radius: 0.375rem;
  border: 1px solid transparent;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
  line-height: 1.5;
  gap: 0.5rem;
}

.btn:focus {
  outline: none;
  box-shadow: 0 0 0 0.2rem rgba(var(--primary-rgb), 0.25);
}

.btn-primary {
  background-color: var(--primary);
  color: white;
  border-color: var(--primary);
}

.btn-primary:hover {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
}

.btn-secondary {
  background-color: var(--secondary);
  color: white;
  border-color: var(--secondary);
}

.btn-secondary:hover {
  background-color: var(--secondary-dark);
  border-color: var(--secondary-dark);
}

.btn-outline-primary {
  background-color: transparent;
  color: var(--primary);
  border-color: var(--primary);
}

.btn-outline-primary:hover {
  background-color: var(--primary);
  color: white;
}

.btn-outline-secondary {
  background-color: transparent;
  color: var(--secondary);
  border-color: var(--secondary);
}

.btn-outline-secondary:hover {
  background-color: var(--secondary);
  color: white;
}

.btn-light {
  background-color: var(--light);
  color: var(--text-dark);
  border-color: var(--border-color);
}

.btn-light:hover {
  background-color: var(--light-dark);
}

.btn-sm {
  padding: 0.25rem 0.75rem;
  font-size: 0.75rem;
}

.btn-lg {
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
}

.btn-icon {
  padding: 0.5rem;
  border-radius: 0.375rem;
}

/* Modern Form Controls */
.form-control {
  display: block;
  width: 100%;
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  line-height: 1.5;
  color: var(--text-dark);
  background-color: white;
  border: 1px solid var(--border-color);
  border-radius: 0.375rem;
  transition: all 0.2s ease;
}

.form-control:focus {
  border-color: var(--primary);
  outline: none;
  box-shadow: 0 0 0 0.2rem rgba(var(--primary-rgb), 0.25);
}

.form-control::placeholder {
  color: var(--text-muted);
  opacity: 0.7;
}

.form-group {
  margin-bottom: 1rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--text-dark);
}

.form-text {
  display: block;
  margin-top: 0.25rem;
  font-size: 0.75rem;
  color: var(--text-muted);
}

/* Select */
.form-select {
  display: block;
  width: 100%;
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  line-height: 1.5;
  color: var(--text-dark);
  background-color: white;
  border: 1px solid var(--border-color);
  border-radius: 0.375rem;
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  background-size: 16px 12px;
}

.form-select:focus {
  border-color: var(--primary);
  outline: none;
  box-shadow: 0 0 0 0.2rem rgba(var(--primary-rgb), 0.25);
}

/* Checkbox & Radio */
.form-check {
  display: block;
  min-height: 1.5rem;
  padding-left: 1.75rem;
  margin-bottom: 0.5rem;
}

.form-check-input {
  width: 1rem;
  height: 1rem;
  margin-top: 0.25rem;
  margin-left: -1.75rem;
  background-color: white;
  border: 1px solid var(--border-color);
  transition: all 0.15s ease;
}

.form-check-input[type="checkbox"] {
  border-radius: 0.25rem;
}

.form-check-input[type="radio"] {
  border-radius: 50%;
}

.form-check-input:checked {
  background-color: var(--primary);
  border-color: var(--primary);
}

.form-check-label {
  margin-bottom: 0;
  font-size: 0.875rem;
  color: var(--text-dark);
  cursor: pointer;
}

/* Input Group */
.input-group {
  display: flex;
  flex-wrap: wrap;
  align-items: stretch;
  width: 100%;
}

.input-group .form-control {
  position: relative;
  flex: 1 1 auto;
  width: 1%;
  min-width: 0;
}

.input-group-text {
  display: flex;
  align-items: center;
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  font-weight: 400;
  color: var(--text-dark);
  text-align: center;
  white-space: nowrap;
  background-color: var(--light);
  border: 1px solid var(--border-color);
}

.input-group > :first-child {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.input-group > :last-child {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.input-group > :not(:first-child):not(:last-child) {
  border-radius: 0;
}

/* Mobile Automation Tool Specific Styles */
.device-card {
  display: flex;
  flex-direction: column;
  height: 100%;
  transition: all 0.2s ease;
  border: 1px solid var(--border-color);
  border-radius: 0.75rem;
  overflow: hidden;
}

.device-card-header {
  padding: 1rem;
  background-color: rgba(var(--light-rgb), 0.5);
  border-bottom: 1px solid var(--border-color);
}

.device-card-body {
  padding: 1rem;
  flex: 1;
}

.device-card-footer {
  padding: 0.75rem 1rem;
  background-color: rgba(var(--light-rgb), 0.5);
  border-top: 1px solid var(--border-color);
}

.device-status {
  display: inline-flex;
  align-items: center;
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 1rem;
  font-weight: 500;
}

.device-status.connected {
  background-color: rgba(25, 135, 84, 0.1);
  color: #198754;
}

.device-status.disconnected {
  background-color: rgba(220, 53, 69, 0.1);
  color: #dc3545;
}

.device-status.connecting {
  background-color: rgba(255, 193, 7, 0.1);
  color: #ffc107;
}

.device-name {
  font-weight: 600;
  margin: 0 0 0.25rem;
}

.device-model {
  color: var(--text-muted);
  font-size: 0.875rem;
  margin: 0;
}

/* Status Badge */
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  font-weight: 600;
  line-height: 1;
  color: white;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 50rem;
}

.badge-success {
  background-color: #198754;
}

.badge-danger {
  background-color: #dc3545;
}

.badge-warning {
  background-color: #ffc107;
  color: #212529;
}

.badge-info {
  background-color: #0dcaf0;
}

.badge-primary {
  background-color: var(--primary);
}

.badge-secondary {
  background-color: var(--secondary);
}

/* Modern Header & Title Styles */
.app-title {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--text-dark);
  margin: 0;
  background: linear-gradient(90deg, var(--primary) 0%, var(--secondary) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  letter-spacing: -0.02em;
}

.app-subtitle {
  font-size: 0.95rem;
  color: var(--text-muted);
  margin: 0.35rem 0 0;
  font-weight: 400;
}

.section-header {
  margin-bottom: 1.5rem;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.section-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-dark);
  margin: 0;
  position: relative;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -0.5rem;
  left: 0;
  width: 2.5rem;
  height: 0.25rem;
  background: linear-gradient(90deg, var(--primary) 0%, var(--secondary) 100%);
  border-radius: 1rem;
}

.section-subtitle {
  font-size: 0.875rem;
  color: var(--text-muted);
  margin: 0.75rem 0 0;
}

h1, h2, h3, h4, h5, h6 {
  color: var(--text-dark);
  font-weight: 600;
  margin-bottom: 1rem;
  line-height: 1.3;
}

h1 {
  font-size: 2rem;
  letter-spacing: -0.02em;
}

h2 {
  font-size: 1.75rem;
  letter-spacing: -0.015em;
}

h3 {
  font-size: 1.5rem;
}

h4 {
  font-size: 1.25rem;
}

h5 {
  font-size: 1.125rem;
}

h6 {
  font-size: 1rem;
}

/* Header Action Buttons */
.header-actions {
  display: flex;
  gap: 0.75rem;
}

.breadcrumb {
  display: flex;
  flex-wrap: wrap;
  padding: 0;
  margin-bottom: 1rem;
  list-style: none;
  font-size: 0.875rem;
}

.breadcrumb-item + .breadcrumb-item {
  padding-left: 0.5rem;
}

.breadcrumb-item + .breadcrumb-item::before {
  display: inline-block;
  padding-right: 0.5rem;
  color: var(--text-muted);
  content: "/";
}

.breadcrumb-item a {
  color: var(--primary);
  text-decoration: none;
}

.breadcrumb-item a:hover {
  text-decoration: underline;
}

.breadcrumb-item.active {
  color: var(--text-muted);
}

@media (max-width: 768px) {
  .app-header {
    padding: 1.25rem 0;
  }

  .app-title {
    font-size: 1.5rem;
  }

  .section-title {
    font-size: 1.125rem;
  }

  h1 {
    font-size: 1.75rem;
  }

  h2 {
    font-size: 1.5rem;
  }

  h3 {
    font-size: 1.25rem;
  }
}

/* Compact Tabs for Inspector */
#inspectorTabs {
  background-color: var(--light);
  border-radius: 0.5rem;
  padding: 0.25rem;
  gap: 0.125rem;
  border-bottom: none;
  margin-bottom: 0;
}

#inspectorTabs .nav-link {
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  border-radius: 0.375rem;
  border-bottom: none;
}

#inspectorTabs .nav-link.active {
  background-color: white;
  color: var(--primary);
  box-shadow: var(--shadow-sm);
  border-bottom: none;
}

#inspectorTabs .nav-link.active::after {
  display: none;
}

#inspectorTabContent {
  border-top: 1px solid var(--gray-light);
  padding-top: 0;
}

#inspectorTabContent .tab-pane {
  padding: 0;
}

/* Element inspector improvements */
.element-inspector {
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: var(--shadow);
}

.source-container {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid var(--gray-light);
  border-radius: 0.375rem;
  background-color: #f8f9fa;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
}

.locator-list {
  max-height: 250px;
  overflow-y: auto;
}

.locator-item {
  padding: 0.5rem 0.75rem;
  border-bottom: 1px solid var(--gray-light);
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.locator-item:last-child {
  border-bottom: none;
}

.locator-item .copy-btn {
  opacity: 0.5;
  transition: opacity 0.2s ease;
}

.locator-item:hover .copy-btn {
  opacity: 1;
}

/* Test Suites List View */
#availableTestCases .list-group-item,
#testSuitesList .list-group-item {
  border-left: 4px solid var(--primary);
  transition: all 0.2s ease;
}

#availableTestCases .list-group-item:hover,
#testSuitesList .list-group-item:hover {
  background-color: rgba(var(--primary-rgb, 79, 70, 229), 0.05);
  transform: translateX(2px);
}

#availableTestCases .form-check-input:checked + .form-check-label {
  color: var(--primary);
  font-weight: 600;
}

#testSuitesList .list-group-item {
  padding: 0.75rem 1rem;
}

#testSuitesList .badge {
  padding: 0.3rem 0.6rem;
  font-size: 0.7rem;
  font-weight: 500;
}

/* Enhanced badges with icons */
.badge-with-icon {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
}

.badge-with-icon i {
  font-size: 0.8em;
}

/* Device Connection Styling */
#deviceSelect {
  border: 2px solid var(--primary);
  border-radius: 0.375rem 0 0 0.375rem;
  box-shadow: 0 0 0 0.1rem rgba(79, 70, 229, 0.1);
  transition: all 0.2s ease;
}

#deviceSelect:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 0.25rem rgba(79, 70, 229, 0.25);
}

#refreshDevices {
  background-color: var(--info);
  color: white;
  border-color: var(--info);
}

#refreshDevices:hover {
  background-color: #0b5ed7;
  border-color: #0b5ed7;
}

/* Device Screen Header Buttons */
.device-screen-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.device-screen-title {
  margin: 0;
  font-weight: 600;
}

.device-screen-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

#refreshScreenBtn, #webInspectorBtn {
  background-color: rgba(255, 255, 255, 0.9);
  color: var(--primary);
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

#refreshScreenBtn:hover, #webInspectorBtn:hover {
  background-color: white;
}



/* Action Form Styling */
.action-form input[type="number"],
.action-form input[type="text"],
.action-form select,
.action-form textarea {
  border: 2px solid var(--primary);
  border-radius: 6px;
  padding: 8px 12px;
  transition: all 0.2s ease;
  box-shadow: 0 0 0 0.1rem rgba(79, 70, 229, 0.05);
}

.action-form input[type="number"]:focus,
.action-form input[type="text"]:focus,
.action-form select:focus,
.action-form textarea:focus {
  border-color: var(--primary-dark);
  box-shadow: 0 0 0 0.2rem rgba(79, 70, 229, 0.2);
  outline: none;
}

.action-form label {
  font-weight: 600;
  color: var(--dark);
  margin-bottom: 6px;
}

.action-form .row {
  margin-bottom: 12px;
}

/* Specific styles for coordinate inputs */
#tapX, #tapY,
#swipeStartX, #swipeStartY,
#swipeEndX, #swipeEndY,
#doubleClickX, #doubleClickY {
  text-align: center;
  font-weight: 500;
  letter-spacing: 0.5px;
  color: var(--primary-dark);
}

/* Action form "Pick from Screen" button */
#pickTapCoordinates,
#pickSwipeCoordinates,
#pickDoubleClickCoordinates {
  background-color: white;
  border: 2px solid var(--primary);
  color: var(--primary);
  margin-top: 8px;
  font-weight: 500;
  transition: all 0.2s ease;
}

#pickTapCoordinates:hover,
#pickSwipeCoordinates:hover,
#pickDoubleClickCoordinates:hover {
  background-color: var(--primary);
  color: white;
}

/* Action Type Dropdown */
#actionType {
  border: 2px solid var(--primary);
  border-radius: 6px;
  padding: 8px 12px;
  font-weight: 500;
  background-color: white;
  color: var(--dark);
  box-shadow: 0 0 0 0.1rem rgba(79, 70, 229, 0.05);
}

#actionType:focus {
  border-color: var(--primary-dark);
  box-shadow: 0 0 0 0.2rem rgba(79, 70, 229, 0.2);
  outline: none;
}

/* Add Action Button */
#addAction {
  background-color: var(--success);
  border-color: var(--success);
  font-weight: 500;
  padding: 8px 16px;
  transition: all 0.2s ease;
}

#addAction:hover {
  background-color: #0d9068;
  border-color: #0d9068;
  transform: translateY(-1px);
}

/* Test Suite Selection Modal */
.test-suite-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.25rem;
  background-color: #f9fafb;
  border-radius: 0.75rem;
  margin-bottom: 1rem;
  border: 1px solid var(--gray-light);
  box-shadow: var(--shadow-sm);
  transition: all 0.2s ease;
}

.test-suite-item:hover {
  box-shadow: var(--shadow);
  transform: translateY(-2px);
}

.test-suite-info {
  flex: 1;
}

.test-suite-name {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--dark);
  margin: 0 0 0.5rem 0;
}

.test-suite-meta {
  color: var(--gray);
  font-size: 0.875rem;
}

.test-suite-action {
  margin-left: 2rem;
}

.test-suite-action .btn {
  padding: 0.5rem 1.5rem;
  font-weight: 500;
}

/* Test Case List in Dropdown */
.test-case-dropdown {
  max-height: 250px;
  overflow-y: auto;
  border: 1px solid var(--gray-light);
  border-radius: 0.5rem;
  background-color: white;
  padding: 0.5rem 0;
}

.test-case-dropdown .list-item {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid var(--gray-light);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.test-case-dropdown .list-item:last-child {
  border-bottom: none;
}

.test-case-dropdown .test-case-name {
  font-weight: 500;
  color: var(--dark);
}

.test-case-dropdown .undefined {
  display: none;
}

/* Fix for "undefined" text in test case names */
.test-case-name::after,
#testCasesList .undefined,
.test-suite-item .undefined,
.test-case-dropdown .undefined {
  display: none !important;
}

/* Also fix for load test case modal */
#loadTestCaseModal .undefined,
#suite-dropdown .undefined {
  display: none !important;
}

/* Fix for action items alignment - ensure left alignment */
#actionsList .list-group-item,
.action-item {
  display: flex !important;
  flex-direction: row !important;
  align-items: center !important;
  justify-content: space-between !important;
  width: 100% !important;
  text-align: left !important;
}

.action-content {
  display: flex !important;
  align-items: center !important;
  justify-content: flex-start !important;
  flex: 1 !important;
  text-align: left !important;
}

.action-buttons {
  display: flex !important;
  justify-content: flex-end !important;
  align-items: center !important;
  margin-left: 10px !important;
}

/* Fix for the step number alignment */
.step-number {
  margin-right: 10px !important;
}

/* Make sure badge labels are properly aligned */
.badge {
  display: inline-flex !important;
  align-items: center !important;
  margin-right: 8px !important;
}

/* Ensure drag indicator is visible and properly positioned */
.drag-indicator {
  margin-right: 8px !important;
  display: inline-flex !important;
  align-items: center !important;
}

/* Ensure the action text is visible and left-aligned */
.action-content span:not(.badge):not(.step-number) {
  text-align: left !important;
  white-space: normal !important;
}

/* For nested test cases in the Actions List */
.test-case-container .action-item,
.test-case-actions .list-group-item {
  flex-direction: row !important;
  text-align: left !important;
}

/* Report Notification Styles */
#report-notification-container {
    z-index: 1100;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
    animation: slideInUp 0.5s ease-out;
}

#report-notification-container .toast {
    min-width: 300px;
    border-radius: 8px;
    overflow: hidden;
    border: none;
    opacity: 1;
}

#report-notification-container .toast-header {
    padding: 0.75rem 1rem;
    font-size: 1rem;
    font-weight: 600;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

#report-notification-container .toast-body {
    padding: 1rem;
    background-color: #ffffff;
}

@keyframes slideInUp {
    from {
        transform: translateY(100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Retry progress indicator */
.retry-progress {
    background-color: #fff3cd;
    padding: 10px;
    margin-bottom: 15px;
    border-radius: 5px;
    border-left: 5px solid #ffc107;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.retry-badge {
    background-color: #ffc107;
    color: #212529;
    padding: 5px 10px;
    border-radius: 20px;
    font-weight: bold;
    font-size: 0.9rem;
    display: inline-block;
}

/* Fix for white space in Test Suites, Reports, and Settings tabs */
#test-suites-tab, #reports-tab, #settings-tab {
    position: relative;
    top: 0;
    left: 0;
    right: 0;
}

/* Fix for tab content positioning */
.tab-pane.fade {
    opacity: 1;
    position: static !important;
    margin-top: 0 !important;
    display: none;
}

.tab-pane.fade.show {
    display: block;
}