/* Test case success/failure indicators */
.test-case-container {
    margin-bottom: 15px;
    border-radius: 4px;
    overflow: hidden;
}

.test-case-header.success {
    background-color: rgba(40, 167, 69, 0.1) !important;
    border-left: 4px solid #28a745;
}

.test-case-header.error {
    background-color: rgba(220, 53, 69, 0.1) !important;
    border-left: 4px solid #dc3545;
}

.test-case-header.success .collapse-icon,
.test-case-header.success h6 {
    color: #28a745;
}

.test-case-header.error .collapse-icon,
.test-case-header.error h6 {
    color: #dc3545;
}

/* Add hover states for success/error test case headers */
.test-case-header.success:hover {
    background-color: rgba(40, 167, 69, 0.2) !important;
}

.test-case-header.error:hover {
    background-color: rgba(220, 53, 69, 0.2) !important;
}

/* Make sure test case headers remain visible */
.test-case-header {
    display: flex !important;
    background-color: #f8f9fa;
    cursor: pointer;
    transition: background-color 0.2s;
    border-bottom: 1px solid #dee2e6;
    padding: 0.75rem 1rem;
}

.test-case-header:hover {
    background-color: #e9ecef;
}

.test-case-header h6 {
    margin: 0;
    color: #495057;
    font-weight: 600;
}

.test-case-header .collapse-icon {
    color: #6c757d;
    transition: transform 0.2s;
}

/* Action item status styles */
.action-item {
    position: relative;
    transition: all 0.3s ease;
}

.action-item.executing {
    background-color: #fff3cd;
    border-left: 4px solid #ffc107;
}

.action-item.success {
    background-color: #d4edda;
    border-left: 4px solid #28a745;
}

.action-item.error {
    background-color: #f8d7da;
    border-left: 4px solid #dc3545;
}

.action-status {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    margin-right: 10px;
    border-radius: 50%;
}

.action-status i {
    font-size: 16px;
}

.action-item.success .action-status i {
    color: #28a745;
}

.action-item.error .action-status i {
    color: #dc3545;
}

/* Current test case highlight */
.test-case-container.current-test-case {
    border: 2px solid #007bff;
    box-shadow: 0 0 10px rgba(0, 123, 255, 0.3);
    position: relative;
}

.test-case-container.current-test-case::before {
    content: "Current Test Case";
    position: absolute;
    top: -10px;
    right: 10px;
    background-color: #007bff;
    color: white;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
    z-index: 1;
}

/* Executing action highlight */
.action-item.executing-highlight {
    background-color: #cce5ff !important;
    border-left: 4px solid #007bff !important;
    box-shadow: 0 0 5px rgba(0, 123, 255, 0.5);
    animation: pulse-blue 1.5s infinite;
}

@keyframes pulse-blue {
    0% {
        background-color: #cce5ff;
    }
    50% {
        background-color: #e6f2ff;
    }
    100% {
        background-color: #cce5ff;
    }
}