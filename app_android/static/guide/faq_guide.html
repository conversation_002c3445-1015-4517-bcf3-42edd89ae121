<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mobile App AutoTest - FAQ & Guide</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .guide-section {
            margin-bottom: 2rem;
        }
        .guide-image {
            max-width: 100%;
            height: auto;
            border: 1px solid #ddd;
            border-radius: 8px;
            margin: 1rem 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .code-block {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 1rem;
            margin: 1rem 0;
            font-family: 'Courier New', monospace;
        }
        .faq-item {
            border-bottom: 1px solid #eee;
            padding: 1rem 0;
        }
        .faq-question {
            font-weight: bold;
            color: #0d6efd;
            cursor: pointer;
        }
        .faq-answer {
            margin-top: 0.5rem;
            padding-left: 1rem;
        }
        .nav-pills .nav-link.active {
            background-color: #0d6efd;
        }
        .step-number {
            background-color: #0d6efd;
            color: white;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <div class="row">
            <div class="col-md-3">
                <div class="nav flex-column nav-pills" id="v-pills-tab" role="tablist" aria-orientation="vertical">
                    <button class="nav-link active" id="v-pills-overview-tab" data-bs-toggle="pill" data-bs-target="#v-pills-overview" type="button" role="tab">
                        <i class="bi bi-house-door"></i> Overview
                    </button>
                    <button class="nav-link" id="v-pills-getting-started-tab" data-bs-toggle="pill" data-bs-target="#v-pills-getting-started" type="button" role="tab">
                        <i class="bi bi-play-circle"></i> Getting Started
                    </button>
                    <button class="nav-link" id="v-pills-test-creation-tab" data-bs-toggle="pill" data-bs-target="#v-pills-test-creation" type="button" role="tab">
                        <i class="bi bi-plus-circle"></i> Creating Tests
                    </button>
                    <button class="nav-link" id="v-pills-actions-tab" data-bs-toggle="pill" data-bs-target="#v-pills-actions" type="button" role="tab">
                        <i class="bi bi-gear"></i> Action Types
                    </button>
                    <button class="nav-link" id="v-pills-execution-tab" data-bs-toggle="pill" data-bs-target="#v-pills-execution" type="button" role="tab">
                        <i class="bi bi-play"></i> Test Execution
                    </button>
                    <button class="nav-link" id="v-pills-environments-tab" data-bs-toggle="pill" data-bs-target="#v-pills-environments" type="button" role="tab">
                        <i class="bi bi-globe"></i> Environments
                    </button>
                    <button class="nav-link" id="v-pills-settings-tab" data-bs-toggle="pill" data-bs-target="#v-pills-settings" type="button" role="tab">
                        <i class="bi bi-sliders"></i> Settings
                    </button>
                    <button class="nav-link" id="v-pills-troubleshooting-tab" data-bs-toggle="pill" data-bs-target="#v-pills-troubleshooting" type="button" role="tab">
                        <i class="bi bi-exclamation-triangle"></i> Troubleshooting
                    </button>
                    <button class="nav-link" id="v-pills-faq-tab" data-bs-toggle="pill" data-bs-target="#v-pills-faq" type="button" role="tab">
                        <i class="bi bi-question-circle"></i> FAQ
                    </button>
                </div>
            </div>
            <div class="col-md-9">
                <div class="tab-content" id="v-pills-tabContent">
                    <!-- Overview Section -->
                    <div class="tab-pane fade show active" id="v-pills-overview" role="tabpanel">
                        <div class="guide-section">
                            <h2><i class="bi bi-house-door"></i> Welcome to Mobile App AutoTest</h2>
                            <p class="lead">A comprehensive mobile application testing automation tool that supports both iOS and Android platforms.</p>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <h4>Key Features</h4>
                                    <ul class="list-group list-group-flush">
                                        <li class="list-group-item"><i class="bi bi-check-circle text-success"></i> Cross-platform testing (iOS & Android)</li>
                                        <li class="list-group-item"><i class="bi bi-check-circle text-success"></i> Visual test creation with device mirroring</li>
                                        <li class="list-group-item"><i class="bi bi-check-circle text-success"></i> Multiple action types (tap, swipe, type, etc.)</li>
                                        <li class="list-group-item"><i class="bi bi-check-circle text-success"></i> Image and text-based element detection</li>
                                        <li class="list-group-item"><i class="bi bi-check-circle text-success"></i> Test suite management</li>
                                        <li class="list-group-item"><i class="bi bi-check-circle text-success"></i> Environment variable support</li>
                                        <li class="list-group-item"><i class="bi bi-check-circle text-success"></i> Detailed reporting with screenshots</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h4>Supported Platforms</h4>
                                    <div class="card">
                                        <div class="card-body">
                                            <h6 class="card-title"><i class="bi bi-apple"></i> iOS</h6>
                                            <p class="card-text">Supports iOS devices and simulators using WebDriverAgent</p>
                                        </div>
                                    </div>
                                    <div class="card mt-2">
                                        <div class="card-body">
                                            <h6 class="card-title"><i class="bi bi-android2"></i> Android</h6>
                                            <p class="card-text">Supports Android devices and emulators using UiAutomator2</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Getting Started Section -->
                    <div class="tab-pane fade" id="v-pills-getting-started" role="tabpanel">
                        <div class="guide-section">
                            <h2><i class="bi bi-play-circle"></i> Getting Started</h2>
                            
                            <h4>Prerequisites</h4>
                            <div class="alert alert-info">
                                <h6>For iOS Testing:</h6>
                                <ul>
                                    <li>macOS with Xcode installed</li>
                                    <li>iOS device or simulator</li>
                                    <li>WebDriverAgent configured</li>
                                </ul>
                                
                                <h6>For Android Testing:</h6>
                                <ul>
                                    <li>Android SDK installed</li>
                                    <li>ADB in system PATH</li>
                                    <li>Android device with USB debugging enabled or emulator</li>
                                </ul>
                            </div>

                            <h4>Installation Steps</h4>
                            <div class="step-number">1</div>
                            <strong>Clone the repository</strong>
                            <div class="code-block">
                                git clone https://github.com/techietrends23/MobileApp-AutoTest.git<br>
                                cd MobileApp-AutoTest
                            </div>

                            <div class="step-number">2</div>
                            <strong>Install dependencies</strong>
                            <div class="code-block">
                                pip install -r requirements.txt
                            </div>

                            <div class="step-number">3</div>
                            <strong>Start the application</strong>
                            <div class="code-block">
                                # For iOS testing<br>
                                python run.py<br><br>
                                # For Android testing<br>
                                python run_android.py
                            </div>

                            <div class="step-number">4</div>
                            <strong>Access the web interface</strong>
                            <p>Open your browser and navigate to:</p>
                            <ul>
                                <li>iOS: <code>http://localhost:8080</code></li>
                                <li>Android: <code>http://localhost:8081</code></li>
                            </ul>
                        </div>
                    </div>

                    <!-- Test Creation Section -->
                    <div class="tab-pane fade" id="v-pills-test-creation" role="tabpanel">
                        <div class="guide-section">
                            <h2><i class="bi bi-plus-circle"></i> Creating Tests</h2>
                            
                            <h4>Creating a New Test Case</h4>
                            <div class="step-number">1</div>
                            <strong>Navigate to Test Cases tab</strong>
                            <p>Click on the "Test Cases" tab in the main navigation.</p>

                            <div class="step-number">2</div>
                            <strong>Click "Create New Test Case"</strong>
                            <p>Enter a name and description for your test case.</p>

                            <div class="step-number">3</div>
                            <strong>Add Actions</strong>
                            <p>Use the Action Builder to add test steps:</p>
                            <ul>
                                <li>Click elements on the device screen to create tap actions</li>
                                <li>Use the action dropdown to select different action types</li>
                                <li>Configure action parameters as needed</li>
                            </ul>

                            <h4>Action Builder Interface</h4>
                            <p>The Action Builder provides a visual interface for creating test actions:</p>
                            <ul>
                                <li><strong>Device Screen:</strong> Live view of your device for interactive action creation</li>
                                <li><strong>Action List:</strong> Shows all actions in your test case</li>
                                <li><strong>Action Form:</strong> Configure parameters for the selected action</li>
                                <li><strong>Controls:</strong> Save, run, and manage your test actions</li>
                            </ul>

                            <div class="alert alert-info">
                                <strong>Tip:</strong> You can reorder actions by dragging them in the action list, and enable/disable individual steps for testing.
                            </div>
                        </div>
                    </div>

                    <!-- Action Types Section -->
                    <div class="tab-pane fade" id="v-pills-actions" role="tabpanel">
                        <div class="guide-section">
                            <h2><i class="bi bi-gear"></i> Action Types</h2>

                            <div class="row">
                                <div class="col-md-6">
                                    <h4>Basic Actions</h4>
                                    <div class="card mb-3">
                                        <div class="card-header"><strong>Tap</strong></div>
                                        <div class="card-body">
                                            <p>Tap on screen elements using coordinates, text, or images.</p>
                                            <ul>
                                                <li>Coordinate-based tapping</li>
                                                <li>Text-based element detection</li>
                                                <li>Image-based element detection</li>
                                                <li>Fallback options for reliability</li>
                                            </ul>
                                        </div>
                                    </div>

                                    <div class="card mb-3">
                                        <div class="card-header"><strong>Swipe</strong></div>
                                        <div class="card-body">
                                            <p>Perform swipe gestures on the screen.</p>
                                            <ul>
                                                <li>Directional swipes (up, down, left, right)</li>
                                                <li>Custom start and end coordinates</li>
                                                <li>Configurable swipe duration</li>
                                            </ul>
                                        </div>
                                    </div>

                                    <div class="card mb-3">
                                        <div class="card-header"><strong>Type</strong></div>
                                        <div class="card-body">
                                            <p>Enter text into input fields.</p>
                                            <ul>
                                                <li>Support for environment variables</li>
                                                <li>Text clearing options</li>
                                                <li>Special character handling</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <h4>Advanced Actions</h4>
                                    <div class="card mb-3">
                                        <div class="card-header"><strong>Wait</strong></div>
                                        <div class="card-body">
                                            <p>Add delays between actions.</p>
                                            <ul>
                                                <li>Fixed time delays</li>
                                                <li>Wait for element visibility</li>
                                                <li>Configurable timeout values</li>
                                            </ul>
                                        </div>
                                    </div>

                                    <div class="card mb-3">
                                        <div class="card-header"><strong>Check If Exists</strong></div>
                                        <div class="card-body">
                                            <p>Verify element presence on screen.</p>
                                            <ul>
                                                <li>Text-based verification</li>
                                                <li>Image-based verification</li>
                                                <li>Conditional test flow</li>
                                            </ul>
                                        </div>
                                    </div>

                                    <div class="card mb-3">
                                        <div class="card-header"><strong>Take Screenshot</strong></div>
                                        <div class="card-body">
                                            <p>Capture device screenshots.</p>
                                            <ul>
                                                <li>Named screenshot capture</li>
                                                <li>Automatic file management</li>
                                                <li>Report integration</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Test Execution Section -->
                    <div class="tab-pane fade" id="v-pills-execution" role="tabpanel">
                        <div class="guide-section">
                            <h2><i class="bi bi-play"></i> Test Execution</h2>

                            <h4>Running Individual Test Cases</h4>
                            <div class="step-number">1</div>
                            <strong>Select a test case</strong>
                            <p>Navigate to the Test Cases tab and select the test you want to run.</p>

                            <div class="step-number">2</div>
                            <strong>Click "Run Test"</strong>
                            <p>The test will execute step by step, showing progress in real-time.</p>

                            <h4>Running Test Suites</h4>
                            <div class="step-number">1</div>
                            <strong>Create or load a test suite</strong>
                            <p>Test suites contain multiple test cases that run sequentially.</p>

                            <div class="step-number">2</div>
                            <strong>Configure execution settings</strong>
                            <p>Set environment variables and execution parameters.</p>

                            <div class="step-number">3</div>
                            <strong>Start execution</strong>
                            <p>Monitor progress and view detailed results.</p>

                            <h4>Execution Features</h4>
                            <ul>
                                <li><strong>Real-time monitoring:</strong> Watch test execution live</li>
                                <li><strong>Step highlighting:</strong> Current step is highlighted during execution</li>
                                <li><strong>Screenshot capture:</strong> Automatic screenshots on failures</li>
                                <li><strong>Detailed logging:</strong> Comprehensive execution logs</li>
                                <li><strong>Report generation:</strong> HTML reports with screenshots</li>
                            </ul>
                        </div>
                    </div>

                    <!-- Environments Section -->
                    <div class="tab-pane fade" id="v-pills-environments" role="tabpanel">
                        <div class="guide-section">
                            <h2><i class="bi bi-globe"></i> Environment Management</h2>

                            <h4>What are Environments?</h4>
                            <p>Environments allow you to define different sets of variables for testing different configurations (e.g., staging, production, different user accounts).</p>

                            <h4>Managing Environments</h4>
                            <div class="step-number">1</div>
                            <strong>Navigate to Environments tab</strong>
                            <p>Click on the "Environments" tab in the main navigation.</p>

                            <div class="step-number">2</div>
                            <strong>Create a new environment</strong>
                            <p>Click "Add Environment" and provide a name.</p>

                            <div class="step-number">3</div>
                            <strong>Add variables</strong>
                            <p>Define key-value pairs for your environment variables.</p>

                            <h4>Using Environment Variables</h4>
                            <p>Environment variables can be used in test actions using the <code>${variable_name}</code> syntax.</p>

                            <div class="alert alert-info">
                                <strong>Example:</strong> If you have a variable named <code>username</code> with value <code>testuser</code>, you can use <code>${username}</code> in your test actions.
                            </div>

                            <h4>Environment Features</h4>
                            <ul>
                                <li><strong>Multiple environments:</strong> Create different environments for different test scenarios</li>
                                <li><strong>Active environment:</strong> Select which environment to use for test execution</li>
                                <li><strong>Variable substitution:</strong> Automatic replacement of variables during test execution</li>
                                <li><strong>Inline editing:</strong> Edit variables directly in the interface</li>
                            </ul>
                        </div>
                    </div>

                    <!-- Settings Section -->
                    <div class="tab-pane fade" id="v-pills-settings" role="tabpanel">
                        <div class="guide-section">
                            <h2><i class="bi bi-sliders"></i> Settings Configuration</h2>

                            <h4>Directory Configuration</h4>
                            <p>Configure where your test files and reports are stored:</p>

                            <div class="row">
                                <div class="col-md-6">
                                    <h6>Test Cases Directory</h6>
                                    <p>Location where individual test case files are stored.</p>

                                    <h6>Test Suites Directory</h6>
                                    <p>Location where test suite files are stored.</p>

                                    <h6>Reports Directory</h6>
                                    <p>Location where test execution reports are saved.</p>
                                </div>
                                <div class="col-md-6">
                                    <h6>Reference Images Directory</h6>
                                    <p>Location for storing reference images used in image-based actions.</p>

                                    <h6>Files to Push Directory</h6>
                                    <p>Location for files that need to be pushed to devices.</p>

                                    <h6>Temp Files Directory</h6>
                                    <p>Location for temporary files created during test execution.</p>
                                </div>
                            </div>

                            <div class="alert alert-warning">
                                <strong>Important:</strong> Use the "Validate" button to ensure directories exist and are accessible before running tests.
                            </div>

                            <h4>Temp Files Management</h4>
                            <p>The Temp Files directory stores temporary files created during test execution:</p>
                            <ul>
                                <li>Debug images from image matching</li>
                                <li>Screenshots during test execution</li>
                                <li>Image comparison reports</li>
                                <li>Text detection output</li>
                            </ul>

                            <p>Use the "Clear Temp Files" button to clean up temporary files when needed.</p>
                        </div>
                    </div>

                    <!-- Troubleshooting Section -->
                    <div class="tab-pane fade" id="v-pills-troubleshooting" role="tabpanel">
                        <div class="guide-section">
                            <h2><i class="bi bi-exclamation-triangle"></i> Troubleshooting</h2>

                            <h4>Common Issues</h4>

                            <div class="card mb-3">
                                <div class="card-header"><strong>Device Connection Issues</strong></div>
                                <div class="card-body">
                                    <p><strong>Problem:</strong> Device not detected or connection fails</p>
                                    <p><strong>Solutions:</strong></p>
                                    <ul>
                                        <li>Ensure USB debugging is enabled (Android)</li>
                                        <li>Check device trust settings (iOS)</li>
                                        <li>Verify Appium server is running</li>
                                        <li>Check device UDID is correct</li>
                                    </ul>
                                </div>
                            </div>

                            <div class="card mb-3">
                                <div class="card-header"><strong>Element Not Found</strong></div>
                                <div class="card-body">
                                    <p><strong>Problem:</strong> Test fails to find elements on screen</p>
                                    <p><strong>Solutions:</strong></p>
                                    <ul>
                                        <li>Use fallback options (text + image)</li>
                                        <li>Increase wait times</li>
                                        <li>Update element locators</li>
                                        <li>Check screen resolution differences</li>
                                    </ul>
                                </div>
                            </div>

                            <div class="card mb-3">
                                <div class="card-header"><strong>Test Execution Slow</strong></div>
                                <div class="card-body">
                                    <p><strong>Problem:</strong> Tests run slower than expected</p>
                                    <p><strong>Solutions:</strong></p>
                                    <ul>
                                        <li>Reduce wait times where possible</li>
                                        <li>Optimize image matching settings</li>
                                        <li>Use coordinate-based actions when reliable</li>
                                        <li>Clear temp files regularly</li>
                                    </ul>
                                </div>
                            </div>

                            <h4>Debug Tools</h4>
                            <ul>
                                <li><strong>Action Logs:</strong> Check detailed execution logs</li>
                                <li><strong>Screenshots:</strong> Review captured screenshots</li>
                                <li><strong>Debug Images:</strong> Check image matching debug output</li>
                                <li><strong>Browser Console:</strong> Check for JavaScript errors</li>
                            </ul>
                        </div>
                    </div>

                    <!-- FAQ Section -->
                    <div class="tab-pane fade" id="v-pills-faq" role="tabpanel">
                        <div class="guide-section">
                            <h2><i class="bi bi-question-circle"></i> Frequently Asked Questions</h2>

                            <div class="faq-item">
                                <div class="faq-question">▶ How do I connect my device?</div>
                                <div class="faq-answer" style="display: none;">
                                    <p>For iOS devices, ensure they are trusted and WebDriverAgent is installed. For Android devices, enable USB debugging and ensure ADB recognizes the device. The device should appear in the device selection dropdown once properly connected.</p>
                                </div>
                            </div>

                            <div class="faq-item">
                                <div class="faq-question">▶ Can I run tests on multiple devices simultaneously?</div>
                                <div class="faq-answer" style="display: none;">
                                    <p>Yes, you can run multiple instances of the application on different ports. Use command line arguments to specify different ports for parallel execution.</p>
                                </div>
                            </div>

                            <div class="faq-item">
                                <div class="faq-question">▶ How do I handle dynamic content?</div>
                                <div class="faq-answer" style="display: none;">
                                    <p>Use text-based element detection instead of coordinates for dynamic content. Environment variables can help manage changing data. The "Check If Exists" action can help create conditional test flows.</p>
                                </div>
                            </div>

                            <div class="faq-item">
                                <div class="faq-question">▶ What image formats are supported?</div>
                                <div class="faq-answer" style="display: none;">
                                    <p>The tool supports PNG, JPG, and JPEG formats for reference images. PNG is recommended for best quality and transparency support.</p>
                                </div>
                            </div>

                            <div class="faq-item">
                                <div class="faq-question">▶ How do I share test cases with my team?</div>
                                <div class="faq-answer" style="display: none;">
                                    <p>Test cases are stored as JSON files in the configured directories. You can share these files or use version control systems like Git to manage test cases collaboratively.</p>
                                </div>
                            </div>

                            <div class="faq-item">
                                <div class="faq-question">▶ Can I integrate with CI/CD pipelines?</div>
                                <div class="faq-answer" style="display: none;">
                                    <p>Yes, the tool can be integrated into CI/CD pipelines. Test suites can be executed via API calls, and results are available in JSON and HTML formats for integration with reporting systems.</p>
                                </div>
                            </div>

                            <div class="faq-item">
                                <div class="faq-question">▶ How do I update reference images?</div>
                                <div class="faq-answer" style="display: none;">
                                    <p>Reference images can be updated by replacing the files in the reference images directory. Use the Tools tab to validate and compare images before updating them in your tests.</p>
                                </div>
                            </div>

                            <div class="faq-item">
                                <div class="faq-question">▶ What should I do if tests fail intermittently?</div>
                                <div class="faq-answer" style="display: none;">
                                    <p>Intermittent failures are often due to timing issues. Add appropriate wait actions, use fallback options for element detection, and ensure your test environment is stable. Review execution logs to identify patterns.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Add click handlers for FAQ items
        document.addEventListener('DOMContentLoaded', function() {
            const faqQuestions = document.querySelectorAll('.faq-question');
            faqQuestions.forEach(question => {
                question.addEventListener('click', function() {
                    const answer = this.nextElementSibling;
                    if (answer.style.display === 'none' || answer.style.display === '') {
                        answer.style.display = 'block';
                        this.innerHTML = this.innerHTML.replace('▶', '▼');
                    } else {
                        answer.style.display = 'none';
                        this.innerHTML = this.innerHTML.replace('▼', '▶');
                    }
                });
            });
        });
    </script>
</body>
</html>
