/**
 * Fixed Device Screen Manager
 * Handles the fixed device screen that stays visible during execution
 */
class FixedDeviceScreenManager {
    constructor(appInstance) {
        this.app = appInstance;
        this.originalContainer = document.getElementById('deviceScreenContainer');
        this.deviceScreen = document.getElementById('deviceScreen');
        this.overlayCanvas = document.getElementById('overlayCanvas');
        this.loadingOverlay = document.getElementById('loadingOverlay');

        this.fixedContainer = null;
        this.isFixed = false;
        this.isDragging = false;
        this.isResizing = false;
        this.dragStartX = 0;
        this.dragStartY = 0;
        this.initialWidth = 350;
        this.initialHeight = 0;
    }

    /**
     * Create the fixed device screen container
     */
    createFixedContainer() {
        if (this.fixedContainer) {
            return; // Already created
        }

        // Create the fixed container
        this.fixedContainer = document.createElement('div');
        this.fixedContainer.className = 'fixed-device-screen-container';

        // Create the header
        const header = document.createElement('div');
        header.className = 'fixed-device-screen-header';
        header.innerHTML = `
            <h5 class="fixed-device-screen-title">Device Screen</h5>
            <button class="fixed-device-screen-close" title="Close Fixed View">×</button>
        `;

        // Create status indicator
        const statusIndicator = document.createElement('div');
        statusIndicator.className = 'fixed-device-screen-status';
        statusIndicator.innerHTML = `
            <span class="current-action-text">Ready</span>
            <span class="current-action-index">0/0</span>
        `;
        this.statusIndicator = statusIndicator;

        // Create resize handle
        const resizeHandle = document.createElement('div');
        resizeHandle.className = 'fixed-device-screen-resize';

        // Add elements to the container
        this.fixedContainer.appendChild(header);
        this.fixedContainer.appendChild(statusIndicator);
        this.fixedContainer.appendChild(resizeHandle);

        // Add the container to the document body
        document.body.appendChild(this.fixedContainer);

        // Add event listener for close button
        const closeButton = this.fixedContainer.querySelector('.fixed-device-screen-close');
        closeButton.addEventListener('click', () => this.unfix());

        // Setup drag and resize functionality
        this.setupDragAndResize();
    }

    /**
     * Setup drag and resize functionality
     */
    setupDragAndResize() {
        const header = this.fixedContainer.querySelector('.fixed-device-screen-header');
        const resizeHandle = this.fixedContainer.querySelector('.fixed-device-screen-resize');

        // Drag functionality
        header.addEventListener('mousedown', (e) => {
            if (e.target.classList.contains('fixed-device-screen-close')) return;

            this.isDragging = true;
            this.dragStartX = e.clientX;
            this.dragStartY = e.clientY;

            const rect = this.fixedContainer.getBoundingClientRect();
            this.initialLeft = rect.left;
            this.initialTop = rect.top;

            document.addEventListener('mousemove', this.handleDragMove);
            document.addEventListener('mouseup', this.handleDragEnd);
        });

        // Resize functionality
        resizeHandle.addEventListener('mousedown', (e) => {
            this.isResizing = true;
            this.dragStartX = e.clientX;
            this.dragStartY = e.clientY;

            const rect = this.fixedContainer.getBoundingClientRect();
            this.initialWidth = rect.width;
            this.initialHeight = rect.height;

            document.addEventListener('mousemove', this.handleResizeMove);
            document.addEventListener('mouseup', this.handleResizeEnd);
        });
    }

    /**
     * Handle drag movement
     */
    handleDragMove = (e) => {
        if (!this.isDragging) return;

        const deltaX = e.clientX - this.dragStartX;
        const deltaY = e.clientY - this.dragStartY;

        this.fixedContainer.style.left = `${this.initialLeft + deltaX}px`;
        this.fixedContainer.style.top = `${this.initialTop + deltaY}px`;
    };

    /**
     * Handle drag end
     */
    handleDragEnd = () => {
        this.isDragging = false;
        document.removeEventListener('mousemove', this.handleDragMove);
        document.removeEventListener('mouseup', this.handleDragEnd);
    };

    /**
     * Handle resize movement
     */
    handleResizeMove = (e) => {
        if (!this.isResizing) return;

        const deltaX = e.clientX - this.dragStartX;
        const deltaY = e.clientY - this.dragStartY;

        const newWidth = this.initialWidth + deltaX;
        const newHeight = this.initialHeight + deltaY;

        if (newWidth >= 200) {
            this.fixedContainer.style.width = `${newWidth}px`;
        }
    };

    /**
     * Handle resize end
     */
    handleResizeEnd = () => {
        this.isResizing = false;
        document.removeEventListener('mousemove', this.handleResizeMove);
        document.removeEventListener('mouseup', this.handleResizeEnd);
    };

    /**
     * Fix the device screen to a floating position
     */
    fix() {
        if (this.isFixed) return;

        // Create the fixed container if it doesn't exist
        this.createFixedContainer();

        // Move the device screen to the fixed container
        if (this.deviceScreen) {
            // Clone the device screen
            const clonedScreen = this.deviceScreen.cloneNode(true);
            this.fixedContainer.appendChild(clonedScreen);

            // Clone the overlay canvas if it exists
            if (this.overlayCanvas) {
                const clonedCanvas = this.overlayCanvas.cloneNode(true);
                this.fixedContainer.appendChild(clonedCanvas);
            }

            // Clone the loading overlay if it exists
            if (this.loadingOverlay) {
                const clonedLoading = this.loadingOverlay.cloneNode(true);
                this.fixedContainer.appendChild(clonedLoading);
            }
        }

        // Show the fixed container
        this.fixedContainer.style.display = 'block';
        this.isFixed = true;

        // Set up a MutationObserver to keep the fixed screen updated
        this.setupScreenObserver();
    }

    /**
     * Set up a MutationObserver to keep the fixed screen updated
     */
    setupScreenObserver() {
        // Create a MutationObserver to watch for changes to the original device screen
        this.observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'attributes' && mutation.attributeName === 'src') {
                    // Update the src of the fixed screen
                    const fixedScreen = this.fixedContainer.querySelector('#deviceScreen');
                    if (fixedScreen && this.deviceScreen) {
                        console.log('Fixed screen observer: Updating screenshot src from', this.deviceScreen.src);
                        fixedScreen.src = this.deviceScreen.src;

                        // Also copy any error handlers
                        fixedScreen.onerror = this.deviceScreen.onerror;
                        fixedScreen.onload = this.deviceScreen.onload;
                    }
                }
            });
        });

        // Start observing the original device screen
        if (this.deviceScreen) {
            this.observer.observe(this.deviceScreen, {
                attributes: true,
                attributeFilter: ['src'] // Only watch for src changes
            });

            // Also set up a periodic sync as a fallback
            this.syncInterval = setInterval(() => {
                const fixedScreen = this.fixedContainer?.querySelector('#deviceScreen');
                if (fixedScreen && this.deviceScreen && fixedScreen.src !== this.deviceScreen.src) {
                    console.log('Fixed screen periodic sync: Updating screenshot');
                    fixedScreen.src = this.deviceScreen.src;
                }
            }, 1000); // Check every second
        }
    }

    /**
     * Update the status indicator with current action information
     * @param {string} actionText - Description of the current action
     * @param {number} currentIndex - Current action index
     * @param {number} totalActions - Total number of actions
     */
    updateStatus(actionText, currentIndex, totalActions) {
        if (!this.isFixed || !this.statusIndicator) return;

        const actionTextElement = this.statusIndicator.querySelector('.current-action-text');
        const actionIndexElement = this.statusIndicator.querySelector('.current-action-index');

        if (actionTextElement) {
            actionTextElement.textContent = actionText || 'Ready';
        }

        if (actionIndexElement) {
            actionIndexElement.textContent = `${currentIndex + 1}/${totalActions}`;
        }
    }

    /**
     * Unfix the device screen (return to normal)
     */
    unfix() {
        if (!this.isFixed) return;

        // Hide the fixed container
        if (this.fixedContainer) {
            this.fixedContainer.style.display = 'none';

            // Remove all children
            while (this.fixedContainer.firstChild) {
                this.fixedContainer.removeChild(this.fixedContainer.firstChild);
            }
        }

        this.isFixed = false;

        // Disconnect the observer
        if (this.observer) {
            this.observer.disconnect();
            this.observer = null;
        }

        // Clear the sync interval
        if (this.syncInterval) {
            clearInterval(this.syncInterval);
            this.syncInterval = null;
        }
    }
}

// Export the class
window.FixedDeviceScreenManager = FixedDeviceScreenManager;
