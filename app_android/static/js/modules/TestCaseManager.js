class TestCaseManager {
    constructor(app) {
        this.app = app; // Reference to the main AppiumAutomationApp instance
    }

    // Set up the Test Cases tab functionality
    setupTestCasesTab() {
        console.log('Setting up Test Cases tab');
        // Add click listeners for tab activation
        const testCasesTab = document.getElementById('test-cases-tab-btn');
        if (testCasesTab) {
            // Ensure listener is added only once or managed correctly if re-setup occurs
            const existingListener = testCasesTab.getAttribute('data-listener-added');
            if (!existingListener) {
                testCasesTab.addEventListener('click', () => { // Use 'click' instead of 'shown.bs.tab' if the button triggers loading
                    this.loadAllTestCases();
                });
                testCasesTab.setAttribute('data-listener-added', 'true');
            }

            // Also handle the initial load if the tab might already be active
            if (testCasesTab.classList.contains('active')) {
                 this.loadAllTestCases();
            }
        } else {
             console.error("Test cases tab button not found for setup.");
        }
    }

    // Load all test cases for the Test Cases tab
    async loadAllTestCases() {
        console.log('Loading all test cases for tab display');

        const loadingEl = document.getElementById('testCasesLoading');
        const noCasesEl = document.getElementById('noTestCasesMessage');
        const listEl = document.getElementById('testCasesList'); // Ensure this ID matches the list container in HTML

        if (!loadingEl || !noCasesEl || !listEl) {
            console.error('Required elements for test case display not found.');
            this.app.showToast('UI Error', 'Could not find test case display elements.', 'error');
            return;
        }

        // Show loading state
        loadingEl.classList.remove('d-none');
        noCasesEl.classList.add('d-none');
        listEl.innerHTML = '';

        try {
            // Get available test cases
            const result = await this.app.fetchApi('recording/list', 'GET');
            console.log("Test cases list from API:", result);

            // Store in cache
            if (result.status === 'success' && result.test_cases && Array.isArray(result.test_cases)) {
                this.app.testCasesCache = result.test_cases;
                console.log(`Cached ${this.app.testCasesCache.length} test cases.`);
                this.displayTestCases(); // Display after caching
            } else {
                 console.warn('No test cases found or invalid format:', result);
                this.app.testCasesCache = []; // Clear cache
                this.displayTestCases(); // Display empty state
            }
        } catch (error) {
            console.error('Error loading test cases:', error);
            this.app.showToast('Error', `Failed to load test cases: ${error.message}`, 'danger', 3000);
            // Ensure UI reflects error state
            if (loadingEl) loadingEl.classList.add('d-none');
             if (noCasesEl) {
                 noCasesEl.querySelector('p').textContent = 'Error loading test cases.';
                 noCasesEl.classList.remove('d-none');
             }
             if (listEl) listEl.innerHTML = '<li class="list-group-item text-danger">Failed to load test cases.</li>';
        }
    }

    // Display test cases based on current sort and filter
    displayTestCases() {
        const testCasesListContainer = document.getElementById('testCasesList'); // Changed ID
        const testCasesLoading = document.getElementById('testCasesLoading');
        const noTestCasesMessage = document.getElementById('noTestCasesMessage');

        if (!testCasesListContainer || !testCasesLoading || !noTestCasesMessage) {
            console.error("Missing UI elements for displaying test cases.");
            return;
        }

        // Clear current list
        testCasesListContainer.innerHTML = '';

        // Get search filter (ensure app and input exist)
        const searchText = this.app.testCaseSearchInput ? this.app.testCaseSearchInput.value.toLowerCase() : '';

        // Filter test cases based on search text
        let filteredTestCases = (this.app.testCasesCache || []).filter(testCase => {
            // Ensure testCase and testCase.name exist before filtering
            return testCase && testCase.name && testCase.name.toLowerCase().includes(searchText);
        });

        // Sort test cases (ensure app.testCasesSort exists)
        const sortOrder = this.app.testCasesSort || 'date'; // Default to date if undefined
        if (sortOrder === 'name') {
            filteredTestCases.sort((a, b) => (a.name || '').localeCompare(b.name || ''));
        } else {
            // Sort by date (newest first), handle potential missing 'created' field
            filteredTestCases.sort((a, b) => (new Date(b.created || 0)) - (new Date(a.created || 0)));
        }

        // Hide loading, update UI
        testCasesLoading.classList.add('d-none');

        if (filteredTestCases.length === 0) {
            if (searchText) {
                // No results for search
                noTestCasesMessage.querySelector('p').textContent = 'No test cases match your search.';
                const refreshButton = noTestCasesMessage.querySelector('button');
                if (refreshButton) refreshButton.classList.add('d-none'); // Hide refresh button if searching
            } else {
                // No test cases at all
                noTestCasesMessage.querySelector('p').textContent = 'No test cases found in the configured directory.';
                const refreshButton = noTestCasesMessage.querySelector('button');
                 if (refreshButton) refreshButton.classList.remove('d-none'); // Show refresh button if no cases exist
            }
            noTestCasesMessage.classList.remove('d-none');
            testCasesListContainer.innerHTML = ''; // Ensure list is empty
            return;
        }

        // Show test cases
        noTestCasesMessage.classList.add('d-none');

        // Create list items for each test case
        filteredTestCases.forEach(testCase => {
            const listItem = document.createElement('div');
            listItem.className = 'test-case-list-item d-flex justify-content-between align-items-center p-3 border-bottom'; // Use Bootstrap classes

            const actionCount = testCase.actions ? testCase.actions.length : (testCase.action_count !== undefined ? testCase.action_count : 0); // Use action_count if available
            const createdDate = testCase.created ? new Date(testCase.created).toLocaleDateString() : 'N/A';
            const testCaseName = testCase.name || 'Unnamed Test Case'; // Handle missing name

            listItem.innerHTML = `
                <div class="test-case-info flex-grow-1 me-3">
                    <h6 class="test-case-name mb-1" title="${testCaseName}">${testCaseName}</h6>
                    <div class="test-case-meta text-muted small">
                        <span><i class="bi bi-lightning-charge"></i> ${actionCount} action${actionCount !== 1 ? 's' : ''}</span>
                        <span class="ms-3"><i class="bi bi-calendar-event"></i> ${createdDate}</span>
                        ${testCase.filename ? `<span class="ms-3 text-truncate" style="max-width: 200px;" title="${testCase.filename}"><i class="bi bi-file-earmark"></i> ${testCase.filename}</span>` : ''}
                        ${testCase.device_id ? `<span class="ms-3"><i class="bi bi-phone"></i> ${testCase.device_id}</span>` : ''}
                    </div>
                </div>
                <div class="test-case-actions">
                    <div class="btn-group btn-group-sm" role="group">
                        <button class="btn btn-outline-primary edit-test-case" data-filename="${testCase.filename}" title="Load and Edit Test Case">
                            <i class="bi bi-pencil"></i> Load & Edit
                        </button>
                        <button class="btn btn-outline-secondary rename-test-case" data-filename="${testCase.filename}" data-name="${testCaseName}" title="Rename Test Case">
                            <i class="bi bi-pencil-square"></i> Rename
                        </button>
                        <div class="btn-group btn-group-sm" role="group">
                            <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false" title="More Options">
                                <i class="bi bi-three-dots"></i>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li><a class="dropdown-item info-test-case" href="#" data-filename="${testCase.filename}" data-name="${testCaseName}">
                                    <i class="bi bi-info-circle"></i> Info
                                </a></li>
                                <li><a class="dropdown-item duplicate-test-case" href="#" data-filename="${testCase.filename}">
                                    <i class="bi bi-files"></i> Duplicate
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-danger delete-test-case" href="#" data-filename="${testCase.filename}">
                                    <i class="bi bi-trash"></i> Delete
                                </a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            `;

            testCasesListContainer.appendChild(listItem);
        });

        // Add event listeners to buttons (important to call AFTER adding items)
        this.addTestCardEventListeners();
    }


    // Add event listeners to test case cards/list items
    addTestCardEventListeners() {
        const listContainer = document.getElementById('testCasesList');
        if (!listContainer) {
             console.error("Cannot add test card listeners: container #testCasesList not found.");
             return;
        }

        // Remove existing listener before adding a new one to prevent duplicates
        if (this.testCaseListClickListener) {
            listContainer.removeEventListener('click', this.testCaseListClickListener);
        }

        // Define the click handler using fat arrow to preserve 'this' context
        this.testCaseListClickListener = async (event) => {
            // Check if this is a button or a dropdown item
            const button = event.target.closest('button');
            const link = event.target.closest('a.dropdown-item');

            // Handle regular buttons
            if (button) {
                const filename = button.dataset.filename;
                if (!filename) {
                    console.warn("Clicked button is missing data-filename attribute.");
                    return; // Exit if button doesn't have a filename
                }

                if (button.classList.contains('edit-test-case')) {
                    console.log(`Edit button clicked for: ${filename}`);
                    // Prevent multiple loads if already loading
                    if (this.app.isLoadingSuite || this.app.isLoadingTestCase) {
                        console.warn("Ignoring edit click, already loading.");
                        return;
                    }
                    this.app.isLoadingTestCase = true; // Set flag
                    await this.loadTestCaseByFilename(filename); // Await the load
                    this.app.isLoadingTestCase = false; // Reset flag
                } else if (button.classList.contains('rename-test-case')) {
                    console.log(`Rename button clicked for: ${filename}`);
                    const currentName = button.dataset.name || '';
                    await this.renameTestCase(filename, currentName);
                }
                return;
            }

            // Handle dropdown items
            if (link) {
                event.preventDefault(); // Prevent the default action of the link
                const filename = link.dataset.filename;
                if (!filename) {
                    console.warn("Clicked link is missing data-filename attribute.");
                    return;
                }

                if (link.classList.contains('info-test-case')) {
                    console.log(`Info button clicked for: ${filename}`);
                    const currentName = link.dataset.name || '';
                    await this.showTestCaseInfoModal(filename, currentName);
                } else if (link.classList.contains('duplicate-test-case')) {
                    console.log(`Duplicate button clicked for: ${filename}`);
                    await this.duplicateTestCase(filename);
                } else if (link.classList.contains('delete-test-case')) {
                    console.log(`Delete button clicked for: ${filename}`);
                    // Find the test case name for confirmation dialog
                    const testCase = (this.app.testCasesCache || []).find(tc => tc.filename === filename);
                    const testCaseName = testCase ? testCase.name : filename;
                    if (confirm(`Are you sure you want to permanently delete the test case "${testCaseName}" from the disk? This cannot be undone.`)) {
                        await this.deleteTestCase(filename);
                    }
                }
            }
        };

        // Add the single listener to the container
        listContainer.addEventListener('click', this.testCaseListClickListener);
        console.log("Added unified event listener to #testCasesList.");
    }

    // Filter test cases based on search input
    filterTestCases() {
        // Debounce filtering to avoid excessive updates during typing (optional but good practice)
        if (this.filterTimeout) {
            clearTimeout(this.filterTimeout);
        }
        this.filterTimeout = setTimeout(() => {
             console.log('Filtering test cases...');
            this.displayTestCases();
        }, 300); // Adjust delay as needed (e.g., 300ms)
    }

    // Load a specific test case by filename into the main editor view
    async loadTestCaseByFilename(filename) {
        try {
            this.app.showLoading('Loading test case...');
            console.log(`Attempting to load test case: ${filename}`);

            // Fetch the full test case data using the /load/ endpoint
            const result = await this.app.fetchApi(`test_cases/load/${filename}`, 'GET');

            if (result.status !== 'success' || !result.test_case) {
                 throw new Error(result.error || 'Failed to load test case data from server.');
            }

            const testCase = result.test_case;
            console.log("Loaded test case data:", testCase);


            // Switch to Device Control / Actions tab
             const deviceTabButton = document.getElementById('device-tab-btn') || document.getElementById('device-tab'); // Find the correct tab button ID
             if (deviceTabButton) {
                 // Use Bootstrap's Tab API if available, otherwise fallback to click
                 const tabInstance = bootstrap.Tab.getInstance(deviceTabButton);
                 if (tabInstance) {
                     tabInstance.show();
                 } else {
                     deviceTabButton.click();
                 }
             } else {
                 console.warn("Could not find device control tab button to switch view.");
             }

            // If test case has a device_id and we're not connected to that device, select it
            if (testCase.device_id && testCase.device_id !== 'select' &&
                (!this.app.isConnected || this.app.deviceId !== testCase.device_id)) {
                console.log(`Selecting device specified in test case: ${testCase.device_id}`);
                // Pass false to selectDeviceById to prevent automatic connection attempt if not already connected
                this.app.selectDeviceById(testCase.device_id, false);
            }

            // Clear current actions in the main app
            this.app.clearActions(); // Use the app's clear method

            // Make sure the currentActions array is initialized
            if (!Array.isArray(this.app.currentActions)) {
                this.app.currentActions = [];
                console.log('Initialized currentActions array');
            }

            // Add loaded actions using the app's addActionToList method
            if (testCase.actions && Array.isArray(testCase.actions)) {
                console.log(`Loading ${testCase.actions.length} actions from test case`);

                // First, add all actions to the currentActions array
                this.app.currentActions = [...testCase.actions];
                console.log(`Updated currentActions array, now has ${this.app.currentActions.length} actions`);

                // Then, add each action to the UI
                testCase.actions.forEach((action, index) => {
                    // Use the app's actionManager to add the action to the list
                    if (this.app.actionManager && typeof this.app.actionManager.addActionToList === 'function') {
                        this.app.actionManager.addActionToList(action);
                    } else {
                        console.error('Error: actionManager or addActionToList method not available');
                        throw new Error('Action manager not properly initialized');
                    }
                });

                // Make sure step numbers are updated after loading all actions
                this.app.updateStepNumbers();

                // Force update the execution buttons
                this.app.updateExecutionButtons();

                 this.app.logAction('success', `Loaded test case "${testCase.name || filename}" with ${testCase.actions.length} actions`);

                // Save the current test case name and filename for direct saving in the main app
                this.app.currentTestCaseName = testCase.name || filename; // Use name or fallback to filename
                this.app.currentTestCaseFilename = filename;
                console.log(`Set current test case in main app: ${this.app.currentTestCaseName}, filename: ${this.app.currentTestCaseFilename}`);

                // Update the Save button title in the main app UI
                 const saveBtn = document.getElementById('saveRecordingBtn');
                 if (saveBtn) {
                     saveBtn.setAttribute('title', `Save changes to "${this.app.currentTestCaseName}"`);
                     saveBtn.disabled = false; // Always enable save button when actions are loaded
                 }
                 const saveAsBtn = document.getElementById('saveAsRecordingBtn');
                 if (saveAsBtn) {
                      saveAsBtn.disabled = false; // Always enable Save As button when actions are loaded
                 }

                 console.log('Enabled Save and Save As buttons after loading test case');

                // Show success message via app's toast
                this.app.showToast('Loaded', `Test case "${this.app.currentTestCaseName}" loaded successfully.`, 'success', 3000);
             } else {
                 this.app.logAction('warning', `Test case "${testCase.name || filename}" contains no actions`);
                 this.app.showToast('Info', `Test case "${testCase.name || filename}" has no actions.`, 'info', 3000);
                 // Ensure save buttons reflect empty state
                 const saveBtn = document.getElementById('saveRecordingBtn');
                 if (saveBtn) saveBtn.disabled = true;
                 const saveAsBtn = document.getElementById('saveAsRecordingBtn');
                 if (saveAsBtn) saveAsBtn.disabled = true; // Can't save empty test case
             }

            // Update UI (e.g., enable/disable execution buttons) via app method
            this.app.updateExecutionButtons();

        } catch (error) {
            console.error(`Error loading test case ${filename}:`, error);
            this.app.logAction('error', `Load error: ${error.message}`);
            this.app.showToast('Error', `Failed to load test case: ${error.message}`, 'danger', 5000);
        } finally {
            this.app.hideLoading();
        }
    }

    // Rename a test case
    async renameTestCase(filename, currentName) {
        // Prompt for new name
        const newName = prompt('Enter a new name for the test case:', currentName);

        // Check if user cancelled or entered an empty name
        if (!newName || newName.trim() === '') {
            this.app.showToast('Cancelled', 'Rename cancelled.', 'info');
            return;
        }

        try {
            this.app.showLoading('Renaming test case...');
            console.log(`Requesting rename of ${filename} to "${newName}"`);

            const result = await this.app.fetchApi('recording/rename', 'POST', {
                filename: filename,
                new_name: newName.trim()
            });

            this.app.hideLoading();

            if (result.status === 'success') {
                this.app.showToast('Success', `Test case renamed to "${newName}"`, 'success', 4000);
                console.log(`Renamed ${filename} to "${newName}"`);

                // Update the cache entry
                const testCaseIndex = (this.app.testCasesCache || []).findIndex(tc => tc.filename === filename);
                if (testCaseIndex !== -1) {
                    this.app.testCasesCache[testCaseIndex].name = newName;
                }

                // Refresh the list
                this.displayTestCases();

                // If this was the currently loaded test case, update the name
                if (this.app.currentTestCaseFilename === filename) {
                    this.app.currentTestCaseName = newName;
                    // Update the Save button title
                    const saveBtn = document.getElementById('saveRecordingBtn');
                    if (saveBtn) {
                        saveBtn.setAttribute('title', `Save changes to "${newName}"`);
                    }
                }
            } else {
                console.error('Rename failed:', result.error);
                this.app.showToast('Error', `Failed to rename: ${result.error || 'Unknown server error'}`, 'danger', 5000);
            }
        } catch (error) {
            this.app.hideLoading();
            console.error('Error renaming test case:', error);
            this.app.showToast('Error', `Client error renaming test case: ${error.message}`, 'danger', 5000);
        }
    }

    // Duplicate a test case
    async duplicateTestCase(filename) {
         const originalTestCase = (this.app.testCasesCache || []).find(tc => tc.filename === filename);
         const originalName = originalTestCase ? originalTestCase.name : filename;
         // const newNameSuggestion = `${originalName}_copy_${Date.now()}`; // No longer needed

         // Remove the prompt
         // const newName = prompt(`Enter a name for the duplicated test case (based on \"${originalName}\"):`, newNameSuggestion);
         // if (!newName || newName.trim() === '') {
         //     this.app.showToast('Cancelled', 'Duplication cancelled.', 'info');
         //     return;
         // }

        try {
            this.app.showLoading('Duplicating test case...');
            // console.log(`Requesting duplication of ${filename} as ${newName}`); // Old log
            console.log(`Requesting duplication of ${filename}`); // New log

            // Remove new_name from the payload
            const result = await this.app.fetchApi('recording/duplicate', 'POST', {
                filename: filename
                 // new_name: newName.trim() // Removed
            });

            this.app.hideLoading();

            if (result.status === 'success') {
                // Use the returned name/filename for the success message
                const duplicatedName = result.new_name || result.new_filename || 'the duplicated file';
                this.app.showToast('Success', `Test case "${originalName}" duplicated as "${duplicatedName}"`, 'success', 4000);
                 console.log(`Duplicated ${filename} to ${result.new_filename}`);
                await this.loadAllTestCases(); // Refresh the list in the tab
            } else {
                 console.error('Duplication failed:', result.error);
                this.app.showToast('Error', `Failed to duplicate: ${result.error || 'Unknown server error'}`, 'danger', 5000);
            }
        } catch (error) {
            this.app.hideLoading();
             console.error('Error duplicating test case:', error);
            this.app.showToast('Error', `Client error duplicating test case: ${error.message}`, 'danger', 5000);
        }
    }

    // Delete a test case from the server/disk
    async deleteTestCase(filename) {
        try {
            this.app.showLoading('Deleting test case from disk...');
            console.log(`Requesting deletion of ${filename}`);

            // --- Use the /delete_test_case/<filename> route with DELETE method ---
            const result = await this.app.fetchApi(
                `delete_test_case/${filename}`, // Endpoint with filename in path
                'DELETE',                     // Method is DELETE
                null                          // No request body needed
            );
            // --- Old call: ---
            // const result = await this.app.fetchApi('recording/delete', 'POST', {
            //     filename: filename
            // });

            this.app.hideLoading();

            // Assuming the success response format might be different, adjust if necessary
            // Let's assume it returns { success: true/false, message: '...' } based on settings.js example
            if (result.success || result.status === 'success') { // Check for different possible success indicators
                const deletedName = result.deleted_name || filename; // Use name from response if available
                this.app.showToast('Deleted', `Test case "${deletedName}" was permanently deleted.`, 'success', 4000);
                console.log(`Deleted test case: ${filename}`);

                // Remove from cache immediately for UI responsiveness
                this.app.testCasesCache = (this.app.testCasesCache || []).filter(tc => tc.filename !== filename);

                // Refresh the list display
                 this.displayTestCases();

                // Check if the deleted test case was the one currently loaded
                 if (this.app.currentTestCaseFilename === filename) {
                     console.log("Deleted test case was currently loaded. Clearing actions.");
                     this.app.clearActions();
                     this.app.currentTestCaseName = null;
                     this.app.currentTestCaseFilename = null;
                     // Optionally update save button titles/states
                     const saveBtn = document.getElementById('saveRecordingBtn');
                     if (saveBtn) saveBtn.setAttribute('title', 'Save new recording');
                 }

            } else {
                 console.error('Deletion failed:', result.error);
                this.app.showToast('Error', `Delete failed: ${result.error || 'Unknown server error'}`, 'danger', 5000);
            }
        } catch (error) {
            this.app.hideLoading();
             console.error('Error deleting test case:', error);
            this.app.showToast('Error', `Client error deleting test case: ${error.message}`, 'danger', 5000);
        }
    }

    // Show modal to view/edit test case info
    async showTestCaseInfoModal(filename, currentName) {
        try {
            this.app.showLoading('Loading test case info...');
            console.log(`Showing info for test case: ${filename}`);

            // Load the test case data
            const result = await this.app.fetchApi(`test_cases/load/${filename}`, 'GET');

            if (result.status !== 'success' || !result.test_case) {
                throw new Error(result.error || 'Failed to load test case data from server.');
            }

            const testCase = result.test_case;
            this.app.hideLoading();

            // Check if modal already exists, if not create it
            let testCaseInfoModal = document.getElementById('testCaseInfoModal');
            if (!testCaseInfoModal) {
                // Create modal HTML
                const modalHTML = `
                <div class="modal fade" id="testCaseInfoModal" tabindex="-1" aria-labelledby="testCaseInfoModalLabel" aria-hidden="true">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header bg-primary text-white">
                                <h5 class="modal-title" id="testCaseInfoModalLabel">Test Case Information</h5>
                                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <form id="testCaseInfoForm">
                                    <div class="mb-3">
                                        <label for="testCaseInfoName" class="form-label">Name</label>
                                        <input type="text" class="form-control" id="testCaseInfoName" readonly>
                                    </div>
                                    <div class="mb-3">
                                        <label for="testCaseInfoLabels" class="form-label">Labels</label>
                                        <input type="text" class="form-control" id="testCaseInfoLabels" placeholder="Add comma-separated labels">
                                    </div>
                                    <div class="mb-3">
                                        <label for="testCaseInfoDescription" class="form-label">Description</label>
                                        <textarea class="form-control" id="testCaseInfoDescription" rows="3" placeholder="Enter a description for this test case"></textarea>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Created</label>
                                        <p id="testCaseInfoCreated" class="form-control-static"></p>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Last Updated</label>
                                        <p id="testCaseInfoUpdated" class="form-control-static"></p>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Actions</label>
                                        <p id="testCaseInfoActions" class="form-control-static"></p>
                                    </div>
                                    <div class="mb-3">
                                        <label for="testCaseInfoNotes" class="form-label">Notes</label>
                                        <textarea class="form-control" id="testCaseInfoNotes" rows="4" placeholder="Add notes about this test case"></textarea>
                                    </div>
                                </form>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                <button type="button" class="btn btn-primary" id="saveTestCaseInfoBtn">Save Changes</button>
                            </div>
                        </div>
                    </div>
                </div>`;

                // Append modal to body
                document.body.insertAdjacentHTML('beforeend', modalHTML);
                testCaseInfoModal = document.getElementById('testCaseInfoModal');
            }

            // Populate modal fields
            document.getElementById('testCaseInfoName').value = testCase.name || '';
            document.getElementById('testCaseInfoLabels').value = testCase.labels ? testCase.labels.join(', ') : '';
            document.getElementById('testCaseInfoDescription').value = testCase.description || '';
            document.getElementById('testCaseInfoCreated').textContent = testCase.created ? new Date(testCase.created).toLocaleString() : 'N/A';
            document.getElementById('testCaseInfoUpdated').textContent = testCase.updated ? new Date(testCase.updated).toLocaleString() : 'N/A';
            document.getElementById('testCaseInfoActions').textContent = `${testCase.actions ? testCase.actions.length : 0} action(s)`;
            document.getElementById('testCaseInfoNotes').value = testCase.notes || '';

            // Set up the save button
            const saveBtn = document.getElementById('saveTestCaseInfoBtn');
            saveBtn.onclick = async () => {
                // Get values from form
                const description = document.getElementById('testCaseInfoDescription').value;
                const notes = document.getElementById('testCaseInfoNotes').value;
                const labels = document.getElementById('testCaseInfoLabels').value.split(',').map(label => label.trim());

                try {
                    this.app.showLoading('Saving test case info...');

                    // Update the test case data
                    testCase.description = description;
                    testCase.notes = notes;
                    testCase.labels = labels;

                    // Save the updated test case
                    const result = await this.app.fetchApi('test_cases/update_info', 'POST', {
                        filename: filename,
                        description: description,
                        notes: notes,
                        labels: labels
                    });

                    this.app.hideLoading();

                    if (result.status === 'success') {
                        // Update the cache
                        const testCaseIndex = (this.app.testCasesCache || []).findIndex(tc => tc.filename === filename);
                        if (testCaseIndex !== -1) {
                            this.app.testCasesCache[testCaseIndex].description = description;
                            this.app.testCasesCache[testCaseIndex].notes = notes;
                            this.app.testCasesCache[testCaseIndex].labels = labels;
                        }

                        // Hide the modal
                        bootstrap.Modal.getInstance(testCaseInfoModal).hide();

                        // Show success message
                        this.app.showToast('Success', 'Test case information saved successfully.', 'success', 3000);
                    } else {
                        throw new Error(result.error || 'Failed to save test case information.');
                    }
                } catch (error) {
                    this.app.hideLoading();
                    console.error('Error saving test case info:', error);
                    this.app.showToast('Error', `Failed to save test case information: ${error.message}`, 'danger', 5000);
                }
            };

            // Show the modal
            const modalInstance = new bootstrap.Modal(testCaseInfoModal);
            modalInstance.show();

        } catch (error) {
            this.app.hideLoading();
            console.error(`Error showing test case info modal for ${filename}:`, error);
            this.app.showToast('Error', `Failed to load test case information: ${error.message}`, 'danger', 5000);
        }
    }
}

// Make the class available globally for loading via script tag
window.TestCaseManager = TestCaseManager;