/**
 * Device Connector Module
 * Handles device selection from URL parameters and connection with Appium
 */

// Initialize when the DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Check for the device ID in URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const deviceId = urlParams.get('deviceId');
    const platform = urlParams.get('platform');
    const sessionId = urlParams.get('sessionId');
    
    console.log('Device Connector initialized - deviceId:', deviceId, 'platform:', platform, 'sessionId:', sessionId);
    
    if (deviceId) {
        // Try to find the device selector
        const deviceSelect = document.querySelector('#device-selector');
        
        if (deviceSelect) {
            // Create a custom option for the device ID
            const option = document.createElement('option');
            option.value = deviceId;
            option.textContent = deviceId;
            option.selected = true;
            
            // Store platform as a data attribute if available
            if (platform) {
                option.dataset.platform = platform;
                console.log(`Setting data-platform attribute to: ${platform}`);
            }
            
            // Store session ID as a data attribute if available
            if (sessionId) {
                option.dataset.sessionId = sessionId;
                console.log(`Setting data-sessionId attribute to: ${sessionId}`);
                
                // Also store in localStorage in case we need it later
                localStorage.setItem(`sessionId_${deviceId}`, sessionId);
            }
            
            // Clear existing options and add the new one
            deviceSelect.innerHTML = '';
            deviceSelect.appendChild(option);
            
            // Make the selector read-only
            deviceSelect.setAttribute('disabled', 'disabled');
            
            // Add a message to indicate the device was pre-selected
            const infoElement = document.createElement('div');
            infoElement.textContent = 'Device pre-selected from Appium Device Manager';
            infoElement.style.fontSize = '12px';
            infoElement.style.color = '#3498db';
            infoElement.style.marginTop = '5px';
            
            // Insert after the device selector
            if (deviceSelect.parentNode) {
                deviceSelect.parentNode.insertBefore(infoElement, deviceSelect.nextSibling);
            }
            
            console.log('Device pre-selected from URL:', deviceId);
            
            // Update the page title to include the device ID for easier tab identification
            document.title = `Device ${deviceId.substring(0, 8)}... - Mobile App Automation Tool`;
            
            // Immediately set the device screen image to use the session-aware screenshot endpoint
            const deviceScreen = document.getElementById('deviceScreen');
            if (deviceScreen) {
                const timestamp = Date.now();
                // Use session-aware screenshot endpoint (Flask session handles isolation)
                deviceScreen.src = `/screenshot?deviceId=${deviceId}&t=${timestamp}`;
                console.log(`Setting initial device screen image with session-aware endpoint: ${deviceScreen.src}`);

                // Add error handler for initial load
                deviceScreen.onerror = () => {
                    console.error('Failed to load initial screenshot, trying fallback');
                    setTimeout(() => {
                        const fallbackTimestamp = Date.now();
                        deviceScreen.src = `/screenshot?deviceId=${deviceId}&t=${fallbackTimestamp}`;
                    }, 1000);
                };

                deviceScreen.onload = () => {
                    console.log('Initial screenshot loaded successfully');
                };
            }
            
            // Automatically connect to the device using the app's connectToDevice method
            setTimeout(() => {
                console.log('Automatically connecting to device...');
                // Use the global app instance to connect
                if (window.app && typeof window.app.connectToDevice === 'function') {
                    // Pass the session ID to the connect method
                    window.app.deviceId = deviceId;
                    window.app.sessionId = sessionId;
                    window.app.connectToDevice();
                    console.log('Automatic connection initiated via app.connectToDevice()');
                } else {
                    console.error('Cannot auto-connect: window.app or connectToDevice method not found');
                    // Fallback to button click if app instance not available
                    const connectButton = document.querySelector('#connect-button');
                    if (connectButton) {
                        console.log('Falling back to connect button click');
                        connectButton.click();
                    } else {
                        console.error('Connect button not found either');
                    }
                }
            }, 1000);
        } else {
            console.error('Device selector not found');
        }
    }
}); 