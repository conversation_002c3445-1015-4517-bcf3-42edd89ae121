/**
 * Tap Fallback Manager
 * Handles the UI for adding and managing fallback actions for tap operations
 */
class TapFallbackManager {
    constructor(app) {
        this.app = app;
        this.fallbackType = null;
        this.fallbackData = null;
        this.fallbackActionAdded = false;
        
        // Initialize the module
        this.initEventListeners();
        console.log('TapFallbackManager initialized');
    }

    /**
     * Initialize event listeners for fallback functionality
     */
    initEventListeners() {
        // Add fallback action button
        const addFallbackBtn = document.getElementById('addFallbackAction');
        if (addFallbackBtn) {
            addFallbackBtn.addEventListener('click', () => {
                this.showFallbackSection();
            });
        } else {
            console.warn('Add fallback action button not found');
        }

        // Remove fallback action button
        const removeFallbackBtn = document.getElementById('removeFallbackAction');
        if (removeFallbackBtn) {
            removeFallbackBtn.addEventListener('click', () => {
                this.hideFallbackSection();
            });
        } else {
            console.warn('Remove fallback action button not found');
        }

        // Fallback type dropdown
        const fallbackTypeSelect = document.getElementById('fallbackType');
        if (fallbackTypeSelect) {
            fallbackTypeSelect.addEventListener('change', () => {
                this.handleFallbackTypeChange(fallbackTypeSelect.value);
            });
        } else {
            console.warn('Fallback type select not found');
        }
    }

    /**
     * Show the fallback section
     */
    showFallbackSection() {
        const fallbackSection = document.getElementById('fallbackActionSection');
        if (fallbackSection) {
            fallbackSection.classList.remove('d-none');
            this.fallbackActionAdded = true;
        }
    }

    /**
     * Hide the fallback section
     */
    hideFallbackSection() {
        const fallbackSection = document.getElementById('fallbackActionSection');
        if (fallbackSection) {
            fallbackSection.classList.add('d-none');
            this.fallbackActionAdded = false;
            
            // Reset fallback type
            const fallbackTypeSelect = document.getElementById('fallbackType');
            if (fallbackTypeSelect) {
                fallbackTypeSelect.value = '';
            }
            
            // Clear fallback content
            const fallbackContent = document.getElementById('fallbackContent');
            if (fallbackContent) {
                fallbackContent.innerHTML = '';
            }
            
            // Reset fallback data
            this.fallbackType = null;
            this.fallbackData = null;
        }
    }

    /**
     * Handle fallback type change
     * @param {string} fallbackType - The selected fallback type
     */
    handleFallbackTypeChange(fallbackType) {
        this.fallbackType = fallbackType;
        const fallbackContent = document.getElementById('fallbackContent');
        
        if (!fallbackContent) {
            console.error('Fallback content container not found');
            return;
        }
        
        // Clear previous content
        fallbackContent.innerHTML = '';
        
        // Load appropriate content based on fallback type
        switch (fallbackType) {
            case 'coordinates':
                this.loadCoordinatesFallback(fallbackContent);
                break;
            case 'image':
                this.loadImageFallback(fallbackContent);
                break;
            case 'text':
                this.loadTextFallback(fallbackContent);
                break;
            case 'locator':
                this.loadLocatorFallback(fallbackContent);
                break;
            default:
                // Empty selection, do nothing
                break;
        }
    }

    /**
     * Load coordinates fallback UI
     * @param {HTMLElement} container - The container to load the UI into
     */
    loadCoordinatesFallback(container) {
        container.innerHTML = `
            <div class="row">
                <div class="col-6">
                    <div class="form-group mb-3">
                        <label>X Coordinate</label>
                        <input type="number" id="fallbackX" class="form-control" value="0" min="0">
                    </div>
                </div>
                <div class="col-6">
                    <div class="form-group mb-3">
                        <label>Y Coordinate</label>
                        <input type="number" id="fallbackY" class="form-control" value="0" min="0">
                    </div>
                </div>
            </div>
            <button class="btn btn-outline-primary btn-sm mt-2" id="pickFallbackCoordinates">
                <i class="bi bi-cursor"></i> Pick from Screen
            </button>
        `;
        
        // Add event listener for pick coordinates button
        const pickBtn = document.getElementById('pickFallbackCoordinates');
        if (pickBtn) {
            pickBtn.addEventListener('click', () => {
                this.app.pickCoordinates('fallback');
            });
        }
    }

    /**
     * Load image fallback UI
     * @param {HTMLElement} container - The container to load the UI into
     */
    loadImageFallback(container) {
        container.innerHTML = `
            <div class="form-group">
                <label for="fallbackImageFilename" class="form-label">Reference Image:</label>
                <div class="input-group mb-2">
                    <select class="form-select" id="fallbackImageFilename">
                        <option value="">-- Select Image --</option>
                    </select>
                    <button class="btn btn-outline-secondary" type="button" id="refreshFallbackImages">
                        <i class="bi bi-arrow-clockwise"></i>
                    </button>
                </div>
                <div class="mb-3">
                    <button class="btn btn-outline-primary btn-sm" id="captureFallbackImage" data-requires-connection="true" disabled>
                        <i class="bi bi-camera"></i> Capture from Screen
                    </button>
                </div>
                <div class="row">
                    <div class="col-6">
                        <label for="fallbackThreshold" class="form-label">Similarity Threshold:</label>
                        <input type="number" class="form-control mb-2" id="fallbackThreshold" value="0.7" min="0" max="1" step="0.05">
                    </div>
                </div>
            </div>
        `;
        
        // Load reference images
        this.app.loadReferenceImages('tap', 'fallbackImageFilename');
        
        // Add event listener for refresh button
        const refreshBtn = document.getElementById('refreshFallbackImages');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                this.app.loadReferenceImages('tap', 'fallbackImageFilename');
            });
        }
        
        // Add event listener for capture button
        const captureBtn = document.getElementById('captureFallbackImage');
        if (captureBtn) {
            captureBtn.addEventListener('click', () => {
                this.app.captureScreenImage('fallback');
            });
        }
    }

    /**
     * Load text fallback UI
     * @param {HTMLElement} container - The container to load the UI into
     */
    loadTextFallback(container) {
        container.innerHTML = `
            <div class="form-group mb-3">
                <label>Text to Find</label>
                <input type="text" id="fallbackTextToFind" class="form-control" placeholder="Enter text to find on screen">
                <small class="text-muted">The text to search for on the screen using OCR</small>
            </div>
        `;
    }

    /**
     * Load locator fallback UI
     * @param {HTMLElement} container - The container to load the UI into
     */
    loadLocatorFallback(container) {
        container.innerHTML = `
            <div class="form-group mb-3">
                <label>Locator Type</label>
                <select id="fallbackLocatorType" class="form-control">
                    <option value="id">ID</option>
                    <option value="xpath">XPath</option>
                    <option value="accessibility_id">Accessibility ID</option>
                    <option value="text">Text</option>
                </select>
            </div>
            <div class="form-group mb-3">
                <label>Locator Value</label>
                <input type="text" id="fallbackLocatorValue" class="form-control" placeholder="Enter locator value">
            </div>
        `;
    }

    /**
     * Get the fallback data for the current fallback type
     * @returns {Object|null} The fallback data or null if no fallback is set
     */
    getFallbackData() {
        if (!this.fallbackType) {
            return null;
        }
        
        let fallbackData = {
            fallback_type: this.fallbackType
        };
        
        // Get specific data based on fallback type
        switch (this.fallbackType) {
            case 'coordinates':
                const x = parseInt(document.getElementById('fallbackX')?.value);
                const y = parseInt(document.getElementById('fallbackY')?.value);
                if (isNaN(x) || isNaN(y)) {
                    this.app.logAction('error', 'Invalid fallback coordinates');
                    return null;
                }
                fallbackData.x = x;
                fallbackData.y = y;
                break;
                
            case 'image':
                const imageFilename = document.getElementById('fallbackImageFilename')?.value;
                const threshold = parseFloat(document.getElementById('fallbackThreshold')?.value);
                if (!imageFilename) {
                    this.app.logAction('error', 'Fallback image is required');
                    return null;
                }
                fallbackData.image_filename = imageFilename;
                fallbackData.threshold = (!isNaN(threshold) && threshold > 0 && threshold <= 1) ? threshold : 0.7;
                break;
                
            case 'text':
                const textToFind = document.getElementById('fallbackTextToFind')?.value;
                if (!textToFind) {
                    this.app.logAction('error', 'Fallback text to find is required');
                    return null;
                }
                fallbackData.text = textToFind;
                break;
                
            case 'locator':
                const locatorType = document.getElementById('fallbackLocatorType')?.value;
                const locatorValue = document.getElementById('fallbackLocatorValue')?.value;
                if (!locatorType || !locatorValue) {
                    this.app.logAction('error', 'Fallback locator type and value are required');
                    return null;
                }
                fallbackData.locator_type = locatorType;
                fallbackData.locator_value = locatorValue;
                break;
                
            default:
                this.app.logAction('error', `Unsupported fallback type: ${this.fallbackType}`);
                return null;
        }
        
        return fallbackData;
    }

    /**
     * Clear the fallback data and UI
     */
    clearFallback() {
        this.fallbackData = null;
        this.fallbackType = null;
        this.fallbackActionAdded = false;
        
        // Reset the UI
        const fallbackSection = document.getElementById('fallbackActionSection');
        if (fallbackSection) {
            fallbackSection.classList.add('d-none');
        }
        
        const fallbackTypeSelect = document.getElementById('fallbackType');
        if (fallbackTypeSelect) {
            fallbackTypeSelect.value = '';
        }
        
        const fallbackContent = document.getElementById('fallbackContent');
        if (fallbackContent) {
            fallbackContent.innerHTML = '';
        }
    }

    /**
     * Update fallback UI based on existing data
     */
    updateFallbackInfo() {
        if (!this.fallbackData || !this.fallbackData.fallback_type) {
            return;
        }
        
        // Show the fallback section
        const fallbackSection = document.getElementById('fallbackActionSection');
        if (fallbackSection) {
            fallbackSection.classList.remove('d-none');
        }
        
        // Set the fallback type dropdown
        const fallbackTypeSelect = document.getElementById('fallbackType');
        if (fallbackTypeSelect) {
            fallbackTypeSelect.value = this.fallbackData.fallback_type;
            this.handleFallbackTypeChange(this.fallbackData.fallback_type);
        }
        
        // Populate fields based on fallback type
        switch (this.fallbackData.fallback_type) {
            case 'coordinates':
                if (document.getElementById('fallbackX')) {
                    document.getElementById('fallbackX').value = this.fallbackData.x || 0;
                }
                if (document.getElementById('fallbackY')) {
                    document.getElementById('fallbackY').value = this.fallbackData.y || 0;
                }
                break;
                
            case 'image':
                setTimeout(() => {
                    if (document.getElementById('fallbackImageFilename')) {
                        document.getElementById('fallbackImageFilename').value = this.fallbackData.image_filename || '';
                    }
                    if (document.getElementById('fallbackThreshold')) {
                        document.getElementById('fallbackThreshold').value = this.fallbackData.threshold || 0.7;
                    }
                }, 500); // Small delay to ensure dropdown is populated
                break;
                
            case 'text':
                if (document.getElementById('fallbackTextToFind')) {
                    document.getElementById('fallbackTextToFind').value = this.fallbackData.text || '';
                }
                break;
                
            case 'locator':
                if (document.getElementById('fallbackLocatorType')) {
                    document.getElementById('fallbackLocatorType').value = this.fallbackData.locator_type || 'id';
                }
                if (document.getElementById('fallbackLocatorValue')) {
                    document.getElementById('fallbackLocatorValue').value = this.fallbackData.locator_value || '';
                }
                break;
        }
    }

    /**
     * Load fallback data from an action object
     * @param {Object} action - The action object containing fallback data
     */
    loadFallbackData(action) {
        if (!action || !action.fallback_type) {
            return;
        }
        
        const fallbackData = {
            fallback_type: action.fallback_type
        };
        
        // Populate based on fallback type
        switch (action.fallback_type) {
            case 'coordinates':
                fallbackData.x = action.fallback_x;
                fallbackData.y = action.fallback_y;
                break;
                
            case 'image':
                fallbackData.image_filename = action.fallback_image_filename;
                fallbackData.threshold = action.fallback_threshold || 0.7;
                break;
                
            case 'text':
                fallbackData.text = action.fallback_text;
                break;
                
            case 'locator':
                fallbackData.locator_type = action.fallback_locator_type;
                fallbackData.locator_value = action.fallback_locator_value;
                break;
        }
        
        this.fallbackData = fallbackData;
        this.fallbackType = action.fallback_type;
        this.fallbackActionAdded = true;
        
        // Update the UI
        this.updateFallbackInfo();
    }
}
