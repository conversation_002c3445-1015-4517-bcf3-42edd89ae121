// repeat-steps-action.js - Module for handling Repeat Steps actions

class RepeatStepsAction {
    constructor(appInstance) {
        this.app = appInstance;
        this.testCases = [];
        this.init();
    }

    init() {
        // Initialize event listeners
        this.initEventListeners();

        // Load test cases for the dropdown
        this.loadTestCases();
    }

    initEventListeners() {
        // Add event listener for the refresh button
        const refreshButton = document.getElementById('refreshRepeatStepsTestCases');
        if (refreshButton) {
            refreshButton.addEventListener('click', () => {
                this.loadTestCases();
            });
        }

        // Add event listener for the test case dropdown
        const testCaseSelect = document.getElementById('repeatStepsTestCase');
        if (testCaseSelect) {
            testCaseSelect.addEventListener('change', () => {
                this.handleTestCaseSelection();
            });
        }

        // Add event listener for the repeat count input to validate it's a positive number
        const repeatCountInput = document.getElementById('repeatStepsCount');
        if (repeatCountInput) {
            repeatCountInput.addEventListener('input', () => {
                let value = parseInt(repeatCountInput.value);
                if (isNaN(value) || value < 1) {
                    repeatCountInput.value = 1;
                }
            });
            // Set default value
            repeatCountInput.value = 1;
        }
    }

    loadTestCases() {
        // Show loading indicator
        const testCaseSelect = document.getElementById('repeatStepsTestCase');
        if (testCaseSelect) {
            testCaseSelect.innerHTML = '<option value="">Loading test cases...</option>';
        }

        // Fetch test cases from the server - using the same endpoint as MultiStep
        fetch('/api/test_cases_for_multi_step')
            .then(response => response.json())
            .then(data => {
                this.testCases = data.test_cases || [];
                this.populateTestCaseDropdown();
            })
            .catch(error => {
                console.error('Error loading test cases:', error);
                this.app.logAction('error', 'Failed to load test cases for Repeat Steps action');

                // Reset dropdown with error message
                if (testCaseSelect) {
                    testCaseSelect.innerHTML = '<option value="">Error loading test cases</option>';
                }
            });
    }

    populateTestCaseDropdown() {
        const testCaseSelect = document.getElementById('repeatStepsTestCase');
        if (!testCaseSelect) return;

        // Clear the dropdown
        testCaseSelect.innerHTML = '<option value="">-- Select Test Case --</option>';

        // Add test cases to the dropdown
        this.testCases.forEach(testCase => {
            const option = document.createElement('option');
            option.value = testCase.id;
            option.textContent = `${testCase.name} (${testCase.steps_count} steps)`;
            option.dataset.name = testCase.name;
            option.dataset.stepsCount = testCase.steps_count;
            option.dataset.description = testCase.description || '';
            testCaseSelect.appendChild(option);
        });
    }

    handleTestCaseSelection() {
        const testCaseSelect = document.getElementById('repeatStepsTestCase');
        const testCaseInfo = document.getElementById('repeatStepsTestCaseInfo');
        const testCaseName = document.getElementById('repeatStepsTestCaseName');
        const testCaseSteps = document.getElementById('repeatStepsTestCaseSteps');
        const testCaseDescription = document.getElementById('repeatStepsTestCaseDescription');

        if (!testCaseSelect || !testCaseInfo || !testCaseName || !testCaseSteps || !testCaseDescription) return;

        const selectedOption = testCaseSelect.options[testCaseSelect.selectedIndex];

        if (selectedOption && selectedOption.value) {
            // Show the test case info
            testCaseInfo.classList.remove('d-none');

            // Set the test case details
            testCaseName.textContent = selectedOption.dataset.name || '';
            testCaseSteps.textContent = selectedOption.dataset.stepsCount || '0';
            testCaseDescription.textContent = selectedOption.dataset.description || 'No description available';

            // Pre-load the test case steps
            this.preLoadTestCaseSteps(selectedOption.value);
        } else {
            // Hide the test case info
            testCaseInfo.classList.add('d-none');

            // Clear any stored steps
            delete testCaseSelect.dataset.loadedSteps;
        }
    }

    // Pre-load test case steps for better performance
    preLoadTestCaseSteps(testCaseId) {
        const testCaseSelect = document.getElementById('repeatStepsTestCase');
        if (!testCaseSelect) return;

        // Show loading indicator
        const loadingIndicator = document.getElementById('repeatStepsLoadingIndicator');
        if (loadingIndicator) {
            loadingIndicator.classList.remove('d-none');
        }

        // Load the test case steps
        this.loadTestCaseSteps(testCaseId)
            .then(steps => {
                // Store the steps in a data attribute for later use
                testCaseSelect.dataset.loadedSteps = JSON.stringify(steps);
                console.log(`Pre-loaded ${steps.length} steps for test case ${testCaseId}`);

                // Hide loading indicator
                if (loadingIndicator) {
                    loadingIndicator.classList.add('d-none');
                }
            })
            .catch(error => {
                console.error('Error pre-loading test case steps:', error);

                // Hide loading indicator
                if (loadingIndicator) {
                    loadingIndicator.classList.add('d-none');
                }
            });
    }

    // Method to load test case steps for execution
    loadTestCaseSteps(testCaseId) {
        return new Promise((resolve, reject) => {
            fetch(`/api/test_cases/load/${testCaseId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success' && data.test_case) {
                        resolve(data.test_case.actions || []);
                    } else {
                        reject(new Error(data.error || 'Failed to load test case steps'));
                    }
                })
                .catch(error => {
                    console.error('Error loading test case steps:', error);
                    reject(error);
                });
        });
    }

    // Generate a unique alphanumeric ID for an action
    generateActionId() {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let result = '';
        for (let i = 0; i < 10; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }

        return result;
    }

    // Method to create a Repeat Steps action
    createRepeatStepsAction(testCaseId, testCaseName, stepsCount, repeatCount, steps = null) {
        const action = {
            type: 'repeatSteps',
            timestamp: Date.now(),
            test_case_id: testCaseId,
            test_case_name: testCaseName,
            test_case_steps_count: stepsCount,
            repeat_count: repeatCount || 1,
            expanded: false, // Default to collapsed view
            action_id: this.generateActionId() // Add unique action ID for the action
        };

        // Always include steps in the action - either provided steps or load them
        if (steps && Array.isArray(steps)) {
            // Preserve existing action IDs in the steps
            action.test_case_steps = steps;
            action.steps_loaded = true;
        } else {
            // If steps are not provided, load them immediately
            this.loadTestCaseSteps(testCaseId)
                .then(loadedSteps => {
                    // Preserve existing action IDs in the loaded steps
                    action.test_case_steps = loadedSteps;
                    action.steps_loaded = true;
                    console.log(`Loaded ${loadedSteps.length} steps for test case ${testCaseId}`);
                })
                .catch(error => {
                    console.error(`Error loading steps for test case ${testCaseId}:`, error);
                    // Set empty array to avoid null reference errors
                    action.test_case_steps = [];
                    action.steps_loaded = false;
                });
        }

        return action;
    }
}

// Export the class
window.RepeatStepsAction = RepeatStepsAction; 