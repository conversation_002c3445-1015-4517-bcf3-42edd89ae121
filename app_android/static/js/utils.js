// Utility functions for Mobile App Automation Tool

/**
 * Format a date for display in the UI
 * @param {string|Date} dateString - The date to format
 * @returns {string} - Formatted date string
 */
function formatDate(dateString) {
    if (!dateString) return '-';
    
    try {
        // Handle ISO format (with T and Z)
        if (typeof dateString === 'string' && dateString.includes('T')) {
            const date = new Date(dateString);
            return date.toLocaleString();
        }
        
        // If it's already a Date object
        if (dateString instanceof Date) {
            return dateString.toLocaleString();
        }
        
        // Handle simple date format
        return dateString;
    } catch (e) {
        console.error("Error formatting date:", e);
        return dateString || '-';
    }
}

/**
 * Show a toast notification in the UI
 * @param {string} title - The toast title
 * @param {string} message - The toast message
 * @param {string} type - The toast type (success, error, warning, info)
 */
function showToast(title, message, type = 'info') {
    // Check if bootstrap is available
    if (typeof bootstrap === 'undefined') {
        console.error("Bootstrap is not available");
        alert(`${title}: ${message}`);
        return;
    }
    
    // Create toast container if it doesn't exist
    let toastContainer = document.getElementById('toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toast-container';
        toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
        toastContainer.style.zIndex = "1070";
        document.body.appendChild(toastContainer);
    }
    
    // Create a unique ID for this toast
    const toastId = 'toast-' + Date.now();
    
    // Set appropriate background color based on type
    let bgClass;
    switch(type) {
        case 'success':
            bgClass = 'bg-success';
            break;
        case 'error':
            bgClass = 'bg-danger';
            break;
        case 'warning':
            bgClass = 'bg-warning';
            break;
        default:
            bgClass = 'bg-info';
    }
    
    // Create toast HTML
    const toastHtml = `
        <div id="${toastId}" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header ${bgClass} text-white">
                <strong class="me-auto">${title}</strong>
                <small>${new Date().toLocaleTimeString()}</small>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
            <div class="toast-body">
                ${message}
            </div>
        </div>
    `;
    
    // Add toast to the container
    toastContainer.insertAdjacentHTML('beforeend', toastHtml);
    
    // Initialize and show the toast
    const toastElement = document.getElementById(toastId);
    const toast = new bootstrap.Toast(toastElement, {
        autohide: true,
        delay: 5000
    });
    toast.show();
    
    // Remove the toast from DOM after it's hidden
    toastElement.addEventListener('hidden.bs.toast', function() {
        toastElement.remove();
    });
}

/**
 * Create a loading overlay
 * @param {boolean} show - Whether to show or hide the loading overlay
 * @param {string} message - The loading message to display
 */
function showLoading(show, message = 'Loading...') {
    const loadingOverlay = document.getElementById('loadingOverlay');
    const loadingMessage = document.getElementById('loadingMessage');
    
    if (!loadingOverlay || !loadingMessage) {
        console.error("Loading overlay elements not found");
        return;
    }
    
    if (show) {
        loadingMessage.textContent = message;
        loadingOverlay.classList.remove('d-none');
    } else {
        loadingOverlay.classList.add('d-none');
    }
}

/**
 * Add an entry to the action log
 * @param {string} source - The source of the log entry
 * @param {string} message - The log message
 * @param {string} type - The log type (info, success, warning, error)
 */
function logAction(source, message, type = 'info') {
    const actionLog = document.getElementById('actionLog');
    if (!actionLog) {
        console.error("Action log element not found");
        return;
    }
    
    const timestamp = new Date().toLocaleTimeString();
    let badgeClass;
    
    switch (type) {
        case 'success':
            badgeClass = 'bg-success';
            break;
        case 'warning':
            badgeClass = 'bg-warning text-dark';
            break;
        case 'error':
            badgeClass = 'bg-danger';
            break;
        default:
            badgeClass = 'bg-info';
    }
    
    const logEntry = document.createElement('div');
    logEntry.className = 'log-entry border-bottom p-2';
    logEntry.innerHTML = `
        <div class="d-flex align-items-start">
            <span class="badge ${badgeClass} me-2">${source}</span>
            <div class="flex-grow-1">
                <small class="text-muted">${timestamp}</small>
                <div>${message}</div>
            </div>
        </div>
    `;
    
    actionLog.appendChild(logEntry);
    actionLog.scrollTop = actionLog.scrollHeight;
}

// Export functions
window.formatDate = formatDate;
window.showToast = showToast;
window.showLoading = showLoading;
window.logAction = logAction; 