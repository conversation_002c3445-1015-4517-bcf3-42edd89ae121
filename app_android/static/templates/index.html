<!-- Add this floating status panel after the main content -->
<div id="execution-status-panel" class="execution-status-panel">
  <div class="status-header">
    <h3>Execution Status</h3>
    <button class="minimize-btn" id="minimize-status-panel">_</button>
  </div>
  <div class="status-content">
    <div class="progress-container">
      <div class="progress-bar" id="execution-progress-bar"></div>
    </div>
    <div class="status-info">
      <span id="current-action-label">Ready</span>
      <span id="progress-text">0/0</span>
    </div>
    <div class="status-log" id="execution-log"></div>
  </div>
</div>

<!-- Add this after the test case actions table -->
<div class="panel panel-default">
  <div class="panel-heading">
    <h3 class="panel-title">Action Log</h3>
  </div>
  <div class="panel-body">
    <div id="action-log" class="action-log-container">
      <!-- Log entries will be displayed here -->
    </div>
  </div>
</div>

<!-- Change all relative static references to use /static/ prefix -->
<link rel="stylesheet" href="/static/css/styles.css">
<script src="/static/js/app.js"></script> 