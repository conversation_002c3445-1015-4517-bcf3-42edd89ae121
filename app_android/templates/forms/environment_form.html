<div id="environments-section" class="settings-section">
    <h3>Environments Management</h3>
    <p>Manage your test environments and variables. Similar to Postman, you can define sets of variables for different environments (e.g., dev, staging, prod) and easily switch between them.</p>

    <div class="environments-container">
        <div class="environment-list-panel">
            <h4>Environments</h4>
            <button id="add-new-environment-btn" class="btn btn-primary btn-sm mb-2"> <i class="fas fa-plus"></i> New Environment</button>
            <ul id="environments-list" class="list-group environments-list-items"></ul>
        </div>

        <div class="environment-details-panel" style="display: none;">
            <h4 id="environment-form-title">Environment Details</h4>
            <form id="environment-form">
                <input type="hidden" id="environment-id" name="environment_id">
                <div class="form-group">
                    <label for="environment-name">Name:</label>
                    <input type="text" id="environment-name" name="name" class="form-control" required>
                </div>
                <div class="form-group">
                    <label for="environment-description">Description (Optional):</label>
                    <textarea id="environment-description" name="description" class="form-control" rows="2"></textarea>
                </div>
                <button type="submit" id="save-environment-btn" class="btn btn-success btn-sm">Save Environment</button>
                <button type="button" id="set-active-environment-btn" class="btn btn-info btn-sm" style="display: none;">Set Active</button>
                <button type="button" id="delete-environment-btn" class="btn btn-danger btn-sm" style="display: none;">Delete Environment</button>
                <button type="button" id="cancel-edit-environment-btn" class="btn btn-secondary btn-sm">Cancel</button>
            </form>

            <div id="variables-section" style="display: none;" class="mt-4">
                <h5>Variables for <span id="current-env-name-for-vars"></span></h5>
                <table class="table table-sm table-bordered" id="environment-variables-table">
                    <thead>
                        <tr>
                            <th>Variable Name</th>
                            <th>Value</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="environment-variables-tbody">
                        <!-- Variable rows will be injected here -->
                    </tbody>
                </table>
                <button id="add-variable-btn" class="btn btn-primary btn-sm"><i class="fas fa-plus"></i> Add Variable</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal for Adding/Editing Variable -->
<div class="modal fade" id="variable-modal" tabindex="-1" role="dialog" aria-labelledby="variableModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="variableModalLabel">Add/Edit Variable</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="variable-form">
                    <input type="hidden" id="variable-id">
                    <input type="hidden" id="variable-env-id">
                    <div class="form-group">
                        <label for="variable-name">Name:</label>
                        <input type="text" id="variable-name" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label for="variable-value">Value:</label>
                        <input type="text" id="variable-value" class="form-control">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="button" id="save-variable-btn" class="btn btn-primary">Save Variable</button>
            </div>
        </div>
    </div>
</div>

<style>
.environments-container {
    display: flex;
    gap: 20px;
}
.environment-list-panel {
    flex: 1;
    max-width: 300px; /* Adjust as needed */
}
.environment-details-panel {
    flex: 2;
}
.environments-list-items .list-group-item {
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.environments-list-items .list-group-item.active-environment {
    font-weight: bold;
    background-color: #d1ecf1; /* A light blue, adjust as needed */
}
.environments-list-items .list-group-item .active-indicator {
    font-size: 0.8em;
    color: green;
    margin-left: 10px;
}
</style> 