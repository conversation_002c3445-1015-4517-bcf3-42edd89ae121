<!-- Tap on Text Form -->
<div id="tapOnTextActionForm" class="action-form d-none">
    <div class="row">
        <div class="col-12">
            <div class="form-group mb-3">
                <label>Text to Find</label>
                <input type="text" id="tapOnTextToFind" class="form-control" placeholder="Enter text to find on screen">
                <small class="text-muted">The text to search for on the screen using OCR</small>
            </div>
            <div class="form-group mb-3">
                <label>Timeout (seconds)</label>
                <input type="number" id="tapOnTextTimeout" class="form-control" value="30" min="1" step="1">
                <small class="text-muted">Maximum time to wait for the text to appear (default: 30s)</small>
            </div>
            <div class="form-group mb-3">
                <div class="form-check">
                    <input type="checkbox" id="tapOnTextDoubleTap" class="form-check-input">
                    <label class="form-check-label" for="tapOnTextDoubleTap">Double Tap</label>
                </div>
            </div>
            <div class="alert alert-info">
                <i class="bi bi-info-circle-fill me-2"></i>
                <strong>Tip:</strong> This action uses OCR (Optical Character Recognition) to find text on the screen and tap it. It works best with clear, high-contrast text.
            </div>
        </div>
    </div>
</div>
