{% extends "base.html" %}

{% block content %}
<div class="container-fluid">
    <div class="row mt-3">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center bg-primary text-white">
                    <h5 class="mb-0"><i class="bi bi-collection"></i> Test Suites</h5>
                    <button id="createTestSuiteBtn" class="btn btn-light" data-bs-toggle="modal" data-bs-target="#createTestSuiteModal">
                        <i class="bi bi-plus-circle"></i> Create Test Suite
                    </button>
                </div>
                <div class="card-body">
                    <!-- Available Test Cases Section -->
                    <div class="mb-4">
                        <h6 class="mb-3">Available Test Cases</h6>
                        <div class="row" id="availableTestCases">
                            <!-- Test cases will be populated here -->
                        </div>
                    </div>

                    <!-- Existing Test Suites Section -->
                    <div>
                        <h6 class="mb-3">Existing Test Suites</h6>
                        <div class="row" id="testSuitesList">
                            <!-- Test suites will be populated here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Create Test Suite Modal -->
<div class="modal fade" id="createTestSuiteModal" tabindex="-1" aria-labelledby="createTestSuiteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="createTestSuiteModalLabel">Create New Test Suite</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="createTestSuiteForm">
                    <div class="mb-3">
                        <label for="testSuiteName" class="form-label">Test Suite Name</label>
                        <input type="text" class="form-control" id="testSuiteName" required>
                    </div>
                    <div class="mb-3">
                        <label for="testSuiteDescription" class="form-label">Description (Optional)</label>
                        <textarea class="form-control" id="testSuiteDescription" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Add Test Cases</label>
                        <div class="input-group mb-3">
                            <select id="testCaseSelector" class="form-select">
                                <option value="" selected disabled>Select a test case to add</option>
                                <!-- Test cases will be populated here -->
                            </select>
                            <button class="btn btn-outline-primary" type="button" id="addTestCaseBtn">
                                <i class="bi bi-plus"></i> Add
                            </button>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Selected Test Cases</label>
                        <p class="text-muted small">Drag and drop to reorder test cases</p>
                        <div id="selectedTestCases" class="list-group sortable-test-cases">
                            <!-- Selected test cases will be listed here -->
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveTestSuiteBtn">Save Test Suite</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
<script src="{{ url_for('static', filename='js/test_suites.js') }}"></script>
{% endblock %}