import os
import json
import time
from pathlib import Path
import uuid
import sys

# Add parent directory to path to import config
parent_dir = Path(__file__).resolve().parent.parent
if str(parent_dir) not in sys.path:
    sys.path.insert(0, str(parent_dir))
from config import DIRECTORIES

class TestSuitesManager:
    def __init__(self):
        self.suites_dir = Path(DIRECTORIES['TEST_SUITES'])
        self.suites_dir.mkdir(parents=True, exist_ok=True)

    def load_test_suites(self):
        """Load all test suites from the suites directory"""
        suites = []
        for file in self.suites_dir.glob('*.json'):
            try:
                with open(file, 'r') as f:
                    suite_data = json.load(f)
                    suite_data['id'] = file.stem
                    suites.append(suite_data)
            except Exception as e:
                print(f"Error loading test suite {file}: {str(e)}")
        return suites

    def create_test_suite(self, name, description, test_cases):
        """Create a new test suite"""
        suite_id = str(uuid.uuid4())
        suite_data = {
            'id': suite_id,
            'name': name,
            'description': description,
            'test_cases': test_cases,
            'created': time.strftime("%Y-%m-%d %H:%M:%S"),
            'updated': time.strftime("%Y-%m-%d %H:%M:%S")
        }

        file_path = self.suites_dir / f"{suite_id}.json"
        with open(file_path, 'w') as f:
            json.dump(suite_data, f, indent=4)

        # Save to database
        try:
            from app_android.utils.database import save_test_suite
            # Prepare data for database
            db_suite_data = {
                'id': suite_id,
                'name': name,
                'status': 'created',
                'passed': 0,
                'failed': 0,
                'skipped': 0,
                'timestamp': time.strftime("%Y-%m-%d %H:%M:%S"),
                'testCases': []
            }
            save_test_suite(db_suite_data)
            print(f"Saved test suite {suite_id} to database")
        except Exception as e:
            print(f"Error saving test suite to database: {str(e)}")

        return suite_data

    def update_test_suite(self, suite_id, updated_data):
        """Update an existing test suite by ID"""
        file_path = self.suites_dir / f"{suite_id}.json"
        if not file_path.exists():
            return None # Indicate suite not found

        try:
            # Prepare data for saving (ensure ID is consistent, add updated timestamp)
            suite_data_to_save = updated_data.copy()
            suite_data_to_save['id'] = suite_id # Ensure ID matches the file
            suite_data_to_save['updated'] = time.strftime("%Y-%m-%d %H:%M:%S")
            # Preserve original creation time if it exists in the file
            try:
                 with open(file_path, 'r') as f_read:
                    original_data = json.load(f_read)
                    if 'created' in original_data:
                         suite_data_to_save['created'] = original_data['created']
                    else: # Add created time if missing from original
                         suite_data_to_save['created'] = suite_data_to_save['updated']
            except Exception:
                 # If reading fails, just use current time for created too
                 suite_data_to_save['created'] = suite_data_to_save['updated']

            # Overwrite the file with updated data
            with open(file_path, 'w') as f:
                json.dump(suite_data_to_save, f, indent=4)

            # Save to database
            try:
                from app_android.utils.database import save_test_suite
                # Prepare data for database
                db_suite_data = {
                    'id': suite_id,
                    'name': suite_data_to_save.get('name', 'Unknown Suite'),
                    'status': 'updated',
                    'passed': 0,
                    'failed': 0,
                    'skipped': 0,
                    'timestamp': time.strftime("%Y-%m-%d %H:%M:%S"),
                    'testCases': []
                }
                save_test_suite(db_suite_data)
                print(f"Saved updated test suite {suite_id} to database")
            except Exception as db_error:
                print(f"Error saving updated test suite to database: {str(db_error)}")

            # Return the updated data (which includes the ID)
            return suite_data_to_save
        except Exception as e:
            print(f"Error updating test suite {suite_id}: {str(e)}") # Use logger if available
            # logger.error(f"Error updating test suite {suite_id}: {str(e)}")
            return None # Indicate failure

    def duplicate_test_suite(self, suite_id):
        """Duplicate a test suite by ID

        Args:
            suite_id: The ID of the test suite to duplicate

        Returns:
            dict: The duplicated test suite data, or None if failed
        """
        # Load the original test suite
        file_path = self.suites_dir / f"{suite_id}.json"
        if not file_path.exists():
            print(f"Cannot duplicate non-existent test suite: {suite_id}")
            return None

        try:
            # Load the original data
            with open(file_path, 'r') as f:
                original_data = json.load(f)

            # Create a deep copy of the data
            new_data = json.loads(json.dumps(original_data))

            # Generate a new name with timestamp
            original_name = new_data.get('name', 'Unnamed Suite')
            timestamp = time.strftime("%Y%m%d%H%M%S")
            new_data['name'] = f"{original_name}_Copy_{timestamp}"

            # Create a new test suite with the copied data
            return self.create_test_suite(
                name=new_data['name'],
                description=new_data.get('description', ''),
                test_cases=new_data.get('test_cases', [])
            )
        except Exception as e:
            print(f"Error duplicating test suite {suite_id}: {str(e)}")
            return None

    def rename_test_suite(self, suite_id, new_name):
        """Rename a test suite by ID and update the filename to match the new name.

        Args:
            suite_id: The ID of the test suite to rename
            new_name: The new name for the test suite

        Returns:
            dict: A dictionary with the updated test suite data and new filename, or None if failed
        """
        # Load the original test suite
        old_file_path = self.suites_dir / f"{suite_id}.json"
        if not old_file_path.exists():
            print(f"Cannot rename non-existent test suite: {suite_id}")
            return None

        try:
            # Load the original data
            with open(old_file_path, 'r') as f:
                suite_data = json.load(f)

            # Update the name in the suite data
            suite_data['name'] = new_name

            # Update the 'updated' timestamp
            suite_data['updated'] = time.strftime("%Y-%m-%d %H:%M:%S")

            # Generate a new filename based on the new name
            # Remove spaces and special characters
            base_name = ''.join(c for c in new_name if c.isalnum() or c in '_ ').strip()
            base_name = base_name.replace(' ', '_')

            # Use current timestamp for new filename
            timestamp = time.strftime("%Y%m%d%H%M%S")
            new_filename = f"{base_name}_{timestamp}.json"
            new_file_path = self.suites_dir / new_filename

            # Save the suite with the new filename
            with open(new_file_path, 'w') as f:
                json.dump(suite_data, f, indent=4)

            # Delete the old file
            try:
                if old_file_path.exists():
                    old_file_path.unlink()
                    print(f"Deleted old test suite file: {suite_id}.json")
            except Exception as e:
                print(f"Failed to delete old test suite file {suite_id}.json: {e}")

            # Update database
            try:
                from app_android.utils.database import save_test_suite
                # Prepare data for database
                db_suite_data = {
                    'id': new_filename.replace('.json', ''),  # Use new filename as ID
                    'name': new_name,
                    'status': 'renamed',
                    'passed': 0,
                    'failed': 0,
                    'skipped': 0,
                    'timestamp': time.strftime("%Y-%m-%d %H:%M:%S"),
                    'testCases': []
                }
                save_test_suite(db_suite_data)
                print(f"Saved renamed test suite to database with new ID: {new_filename.replace('.json', '')}")
            except Exception as e:
                print(f"Error saving renamed test suite to database: {str(e)}")

            print(f"Successfully renamed test suite {suite_id} to '{new_name}' with new filename {new_filename}")
            return {
                'id': new_filename.replace('.json', ''),
                'filename': new_filename,
                'suite_data': suite_data,
                'old_id': suite_id
            }

        except Exception as e:
            print(f"Error renaming test suite {suite_id}: {str(e)}")
            return None

    def delete_test_suite(self, suite_id):
        """Delete a test suite by ID.

        Args:
            suite_id: The ID of the test suite to delete

        Returns:
            bool: True if deletion was successful, False otherwise
        """
        file_path = self.suites_dir / f"{suite_id}.json"
        if not file_path.exists():
            print(f"Cannot delete non-existent test suite: {suite_id}")
            return False

        try:
            # Delete the file
            file_path.unlink()
            print(f"Deleted test suite file: {suite_id}.json")
            return True
        except Exception as e:
            print(f"Error deleting test suite {suite_id}: {str(e)}")
            return False

    def delete_test_suite(self, suite_id):
        """Delete a test suite by ID"""
        file_path = self.suites_dir / f"{suite_id}.json"
        if file_path.exists():
            file_path.unlink()

            # Delete from database
            try:
                import sqlite3
                from app_android.utils.database import DB_PATH
                conn = sqlite3.connect(DB_PATH)
                cursor = conn.cursor()

                # Delete from test_suites table
                cursor.execute('DELETE FROM test_suites WHERE suite_id = ?', (suite_id,))

                # Delete from test_cases table
                cursor.execute('DELETE FROM test_cases WHERE suite_id = ?', (suite_id,))

                # Delete from test_steps table
                cursor.execute('DELETE FROM test_steps WHERE suite_id = ?', (suite_id,))

                # Delete from screenshots table
                cursor.execute('DELETE FROM screenshots WHERE suite_id = ?', (suite_id,))

                conn.commit()
                conn.close()

                print(f"Deleted test suite {suite_id} from database")
            except Exception as e:
                print(f"Error deleting test suite from database: {str(e)}")

            return True
        return False