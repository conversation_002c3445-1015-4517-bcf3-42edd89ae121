import math
import logging

logger = logging.getLogger(__name__)

def validate_coordinates(coords, device_width=None, device_height=None):
    """
    Validate coordinates to ensure they are finite and within device bounds.
    
    Args:
        coords: Tuple of (x, y) coordinates or None
        device_width: Optional device width for bounds checking
        device_height: Optional device height for bounds checking
        
    Returns:
        Tuple of validated (x, y) coordinates or None if invalid
    """
    # Check if coords is None or not a tuple/list
    if coords is None:
        return None
    
    if not isinstance(coords, (tuple, list)) or len(coords) < 2:
        logger.error(f"Invalid coordinate format: {coords}")
        return None
    
    x, y = coords
    
    # Check for NaN or infinity
    if (not isinstance(x, (int, float)) or not isinstance(y, (int, float)) or
            math.isnan(x) or math.isnan(y) or
            math.isinf(x) or math.isinf(y)):
        logger.error(f"Invalid coordinates (NaN or infinity): ({x}, {y})")
        return None
    
    # Check if coordinates are within device bounds (if provided)
    if device_width is not None and device_height is not None:
        if x < 0 or y < 0 or x >= device_width or y >= device_height:
            logger.warning(f"Coordinates ({x}, {y}) outside device bounds ({device_width}x{device_height})")
            # Clamp coordinates to device bounds
            x = max(0, min(x, device_width - 1))
            y = max(0, min(y, device_height - 1))
            logger.info(f"Clamped coordinates to ({x}, {y})")
    
    return (x, y)
