"""
Database update utility to add action_id column to existing tables
"""

import os
import sqlite3
import logging

logger = logging.getLogger(__name__)

# Import the database path from the database module
from app_android.utils.database import DB_PATH

def update_database_schema():
    """
    Update the database schema to include action_id column in all tables
    
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        logger.info("=== UPDATING DATABASE SCHEMA TO ADD ACTION_ID COLUMN ===")
        
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        # Update test_steps table
        try:
            cursor.execute("SELECT action_id FROM test_steps LIMIT 1")
            logger.info("action_id column already exists in test_steps table")
        except sqlite3.OperationalError:
            logger.info("Adding action_id column to test_steps table...")
            cursor.execute("ALTER TABLE test_steps ADD COLUMN action_id TEXT")
            logger.info("action_id column added successfully to test_steps table")
        
        # Update screenshots table
        try:
            cursor.execute("SELECT action_id FROM screenshots LIMIT 1")
            logger.info("action_id column already exists in screenshots table")
        except sqlite3.OperationalError:
            logger.info("Adding action_id column to screenshots table...")
            cursor.execute("ALTER TABLE screenshots ADD COLUMN action_id TEXT")
            logger.info("action_id column added successfully to screenshots table")
        
        # Update execution_tracking table
        try:
            cursor.execute("SELECT action_id FROM execution_tracking LIMIT 1")
            logger.info("action_id column already exists in execution_tracking table")
        except sqlite3.OperationalError:
            logger.info("Adding action_id column to execution_tracking table...")
            cursor.execute("ALTER TABLE execution_tracking ADD COLUMN action_id TEXT")
            logger.info("action_id column added successfully to execution_tracking table")
        
        # Commit and close
        conn.commit()
        conn.close()
        
        logger.info("Successfully updated database schema to include action_id column")
        return True
    except Exception as e:
        logger.error(f"Error updating database schema: {str(e)}")
        if 'conn' in locals() and conn:
            conn.rollback()
            conn.close()
        return False
