import re
import logging
from .directory_paths_db import directory_paths_db

logger = logging.getLogger(__name__)

def resolve_text_with_env_variables(text_to_process: str, environment_id: int):
    """
    Resolves environment variable placeholders like env[variable_name] in a given string.

    Args:
        text_to_process: The string containing potential environment variable placeholders.
        environment_id: The ID of the environment to fetch variables from.

    Returns:
        The string with placeholders replaced by their resolved values.
        If an environment or variable is not found, or if environment_id is None,
        the original placeholder is kept.
    """
    if environment_id is None:
        logger.warning("No environment selected. Cannot resolve environment variables.")
        return text_to_process

    if not isinstance(text_to_process, str):
        # If the input is not a string (e.g. int, bool, list, dict), return it as is.
        # This prevents errors if non-string action parameters are passed through the resolver.
        return text_to_process

    def replace_match(match):
        variable_name = match.group(1)
        try:
            variables_in_env = directory_paths_db.get_variables_for_environment(environment_id)
            if not variables_in_env:
                logger.warning(f"No variables found for environment ID {environment_id} or environment does not exist when resolving '{variable_name}'.")
                return match.group(0) # Return original placeholder env[var_name]

            for var in variables_in_env:
                if var['name'] == variable_name:
                    resolved_value = var.get('current_value')
                    if resolved_value is None:
                        logger.warning(f"Variable '{variable_name}' in environment ID {environment_id} has a current_value of None.")
                        return "" # Or some other default like match.group(0) or an empty string
                    logger.info(f"Resolved 'env[{variable_name}]' to '{resolved_value}' for environment ID {environment_id}")
                    return str(resolved_value)
            
            logger.warning(f"Variable '{variable_name}' not found in environment ID {environment_id}.")
            return match.group(0) # Return original placeholder
        except Exception as e:
            logger.error(f"Error resolving variable '{variable_name}' for environment ID {environment_id}: {str(e)}")
            return match.group(0) # Return original placeholder on error

    # Regex to find env[variable_name]
    # It looks for 'env[' followed by one or more characters that are not ']' (captured as group 1),
    # followed by ']'
    # Using a non-greedy match for the variable name: (.+?)
    return re.sub(r'env\[(.+?)\]', replace_match, text_to_process)

def get_resolved_variable_value(variable_name: str, environment_id: int):
    """
    Fetches the current_value of a specific variable from a specific environment.

    Args:
        variable_name: The name of the variable to resolve.
        environment_id: The ID of the environment.

    Returns:
        The current_value of the variable, or None if not found or an error occurs.
    """
    if environment_id is None:
        logger.warning("No environment ID provided for get_resolved_variable_value.")
        return None
    try:
        variables_in_env = directory_paths_db.get_variables_for_environment(environment_id)
        if not variables_in_env:
            logger.warning(f"No variables found for environment ID {environment_id} or environment does not exist when trying to get value for '{variable_name}'.")
            return None

        for var in variables_in_env:
            if var['name'] == variable_name:
                return var.get('current_value')
        
        logger.warning(f"Variable '{variable_name}' not found in environment ID {environment_id} for get_resolved_variable_value.")
        return None
    except Exception as e:
        logger.error(f"Error in get_resolved_variable_value for '{variable_name}', env ID {environment_id}: {str(e)}")
        return None

if __name__ == '__main__':
    # Example Usage (requires a running DB setup or mocks)
    # This part is for testing and won't be run when imported

    # Mocking directory_paths_db for standalone testing:
    class MockDirectoryPathsDB:
        def get_variables_for_environment(self, env_id):
            if env_id == 1: # Mock Env 1
                return [
                    {"id": 1, "name": "username", "type": "string", "initial_value": "user_init", "current_value": "user_current"},
                    {"id": 2, "name": "password", "type": "secret", "initial_value": "pass_init", "current_value": "pass_current"},
                    {"id": 3, "name": "api_url", "type": "string", "initial_value": "url_init", "current_value": "http://test.com/api"},
                    {"id": 4, "name": "timeout", "type": "integer", "initial_value": "30", "current_value": "60"},
                    {"id": 5, "name": "empty_var", "type": "string", "initial_value": "", "current_value": ""},
                    {"id": 6, "name": "null_var", "type": "string", "initial_value": None, "current_value": None},
                ]
            elif env_id == 2: # Mock Env 2 (empty)
                return []
            return [] # Default for other env_ids

        def get_environment_by_id(self, env_id):
            if env_id == 1:
                return {"id": 1, "name": "Test Env 1"}
            return None

    # Replace the actual db instance with the mock for this test block
    original_db = directory_paths_db
    directory_paths_db = MockDirectoryPathsDB()
    
    logging.basicConfig(level=logging.INFO)
    logger.info("Testing environment variable resolution...")

    test_env_id = 1
    
    print(f"\\n--- Testing with Environment ID: {test_env_id} ---")
    
    strings_to_test = [
        "User is env[username] with password env[password]",
        "URL: env[api_url], Timeout: env[timeout] seconds",
        "No variables here.",
        "env[non_existent_var]",
        "Empty: env[empty_var], Null: env[null_var]",
        "Multiple: env[username] and env[username] again.",
        "env[username], env[password], env[api_url]",
        {"key": "value", "text": "User is env[username]"}, # Test non-string input
        12345 # Test integer input
    ]

    for s in strings_to_test:
        original_s = s
        if isinstance(s, dict): # make a copy for dicts to show original
            original_s = s.copy()

        print(f"Original: {original_s}")
        if isinstance(s, dict): # Handle dict case for processing
             processed_s = {k: resolve_text_with_env_variables(v, test_env_id) if isinstance(v, str) else v for k,v in s.items()}
        else:
            processed_s = resolve_text_with_env_variables(s, test_env_id)
        print(f"Resolved: {processed_s}\\n")

    print("\\n--- Testing with Non-existent Environment ID: 3 ---")
    print(f"Original: User is env[username]")
    print(f"Resolved: {resolve_text_with_env_variables('User is env[username]', 3)}\\n")
    
    print("\\n--- Testing with None Environment ID ---")
    print(f"Original: User is env[username]")
    print(f"Resolved: {resolve_text_with_env_variables('User is env[username]', None)}\\n")

    print("\\n--- Testing get_resolved_variable_value ---")
    print(f"Value of 'username' in env {test_env_id}: {get_resolved_variable_value('username', test_env_id)}")
    print(f"Value of 'non_existent_var' in env {test_env_id}: {get_resolved_variable_value('non_existent_var', test_env_id)}")
    print(f"Value of 'username' in env 3: {get_resolved_variable_value('username', 3)}")
    print(f"Value of 'username' with env None: {get_resolved_variable_value('username', None)}")

    # Restore original db instance if it was mocked
    directory_paths_db = original_db 