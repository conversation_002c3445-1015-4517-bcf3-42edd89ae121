import sqlite3
import os
import json
import logging
from pathlib import Path

# Set up logging
logger = logging.getLogger(__name__)

class GlobalValuesDB:
    """
    Manages SQLite database operations for global values.
    """
    def __init__(self, db_path=None):
        """
        Initialize the database manager.

        Args:
            db_path: Path to the SQLite database file. If None, uses the default path.
        """
        if db_path is None:
            # Use a default path in the app directory, with instance-specific suffix if needed
            import os
            instance_suffix = os.environ.get('INSTANCE_DB_SUFFIX', '')
            if instance_suffix:
                db_filename = f'global_values{instance_suffix}.db'
            else:
                db_filename = 'global_values.db'
            self.db_path = Path(__file__).resolve().parent.parent.parent / 'app' / 'data' / db_filename
        else:
            self.db_path = Path(db_path)

        # Ensure the directory exists
        self.db_path.parent.mkdir(parents=True, exist_ok=True)

        logger.info(f"Using global values database at: {self.db_path}")

        # Initialize the database
        self._init_db()

        # Initialize default values if they don't exist
        self._init_default_values()

    def _init_db(self):
        """
        Initialize the database schema if it doesn't exist.
        """
        try:
            conn = sqlite3.connect(str(self.db_path))
            cursor = conn.cursor()

            # Create table if it doesn't exist
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS global_values (
                name TEXT PRIMARY KEY,
                value TEXT,
                type TEXT
            )
            ''')

            conn.commit()
            conn.close()
            logger.info("Global values database initialized successfully")
        except Exception as e:
            logger.error(f"Error initializing global values database: {str(e)}")
            raise

    def _init_default_values(self):
        """
        Initialize default values in the database if they don't exist.
        """
        try:
            # Default values
            default_values = {
                'default_element_timeout': 60,  # Default timeout in seconds for element interactions
                'Test Run Retry': 2,  # Default number of test run retries
                'Auto Rerun Failed': False,  # Default for auto rerun failed tests toggle
                'Test Case Delay': 10,  # Default delay between test cases in seconds
                'Max Step Execution Time': 300,  # Default maximum time for a step to execute (5 minutes)
            }

            # Try to get values from config.py if available
            try:
                # Import config to get global values
                import sys
                from pathlib import Path
                sys.path.append(str(Path(__file__).resolve().parent.parent.parent))
                import config

                # If config.py has GLOBAL_VALUES, use those values
                if hasattr(config, 'GLOBAL_VALUES') and isinstance(config.GLOBAL_VALUES, dict):
                    logger.info("Using global values from config.py")
                    for name, value in config.GLOBAL_VALUES.items():
                        default_values[name] = value
                    logger.info(f"Updated default values from config.py: {default_values}")
            except Exception as config_err:
                logger.warning(f"Could not import values from config.py: {config_err}")

            # Check if each default value exists, and set it if it doesn't
            for name, value in default_values.items():
                if self.get_value(name) is None:
                    self.set_value(name, value)
                    logger.info(f"Initialized default value: {name}={value}")
        except Exception as e:
            logger.error(f"Error initializing default values: {str(e)}")

    def get_value(self, name, default=None):
        """
        Get a global value from the database.

        Args:
            name: The parameter name
            default: Default value if the parameter doesn't exist

        Returns:
            The parameter value or the default value
        """
        try:
            conn = sqlite3.connect(str(self.db_path))
            cursor = conn.cursor()

            cursor.execute("SELECT value, type FROM global_values WHERE name = ?", (name,))
            result = cursor.fetchone()

            conn.close()

            if result:
                value, value_type = result
                # Convert value based on type
                if value_type == 'int':
                    return int(value)
                elif value_type == 'float':
                    return float(value)
                elif value_type == 'bool':
                    return value.lower() == 'true'
                else:
                    return value
            return default
        except Exception as e:
            logger.error(f"Error getting global value {name}: {str(e)}")
            return default

    def set_value(self, name, value):
        """
        Set a global value in the database.

        Args:
            name: The parameter name
            value: The parameter value

        Returns:
            True if successful, False otherwise
        """
        try:
            conn = sqlite3.connect(str(self.db_path))
            cursor = conn.cursor()

            # Determine the type of the value
            if isinstance(value, bool):
                value_type = 'bool'
                value_str = str(value).lower()
            elif isinstance(value, int):
                value_type = 'int'
                value_str = str(value)
            elif isinstance(value, float):
                value_type = 'float'
                value_str = str(value)
            else:
                value_type = 'str'
                value_str = str(value)

            cursor.execute(
                "INSERT OR REPLACE INTO global_values (name, value, type) VALUES (?, ?, ?)",
                (name, value_str, value_type)
            )

            conn.commit()
            conn.close()
            logger.info(f"Global value saved: {name}={value} (type: {value_type})")
            return True
        except Exception as e:
            logger.error(f"Error setting global value {name}={value}: {str(e)}")
            return False

    def delete_value(self, name):
        """
        Delete a global value from the database.

        Args:
            name: The parameter name

        Returns:
            True if successful, False otherwise
        """
        try:
            conn = sqlite3.connect(str(self.db_path))
            cursor = conn.cursor()

            cursor.execute("DELETE FROM global_values WHERE name = ?", (name,))

            conn.commit()
            conn.close()
            logger.info(f"Global value deleted: {name}")
            return True
        except Exception as e:
            logger.error(f"Error deleting global value {name}: {str(e)}")
            return False

    def get_all_values(self):
        """
        Get all global values from the database.

        Returns:
            Dictionary of all global values
        """
        try:
            conn = sqlite3.connect(str(self.db_path))
            cursor = conn.cursor()

            cursor.execute("SELECT name, value, type FROM global_values")
            results = cursor.fetchall()

            conn.close()

            global_values = {}
            for name, value, value_type in results:
                # Convert value based on type
                if value_type == 'int':
                    global_values[name] = int(value)
                elif value_type == 'float':
                    global_values[name] = float(value)
                elif value_type == 'bool':
                    global_values[name] = value.lower() == 'true'
                else:
                    global_values[name] = value

            return global_values
        except Exception as e:
            logger.error(f"Error getting all global values: {str(e)}")
            return {}

    def save_values(self, global_values):
        """
        Save a dictionary of global values to the database.
        This replaces all existing global values.

        Args:
            global_values: Dictionary of global values

        Returns:
            True if successful, False otherwise
        """
        try:
            conn = sqlite3.connect(str(self.db_path))
            cursor = conn.cursor()

            # Clear existing global values
            cursor.execute("DELETE FROM global_values")

            # Insert new global values
            for name, value in global_values.items():
                # Determine the type of the value
                if isinstance(value, bool):
                    value_type = 'bool'
                    value_str = str(value).lower()
                elif isinstance(value, int):
                    value_type = 'int'
                    value_str = str(value)
                elif isinstance(value, float):
                    value_type = 'float'
                    value_str = str(value)
                else:
                    value_type = 'str'
                    value_str = str(value)

                cursor.execute(
                    "INSERT INTO global_values (name, value, type) VALUES (?, ?, ?)",
                    (name, value_str, value_type)
                )

            conn.commit()
            conn.close()
            logger.info(f"All global values saved: {global_values}")
            return True
        except Exception as e:
            logger.error(f"Error saving global values: {str(e)}")
            return False

# Create a singleton instance
global_values_db = GlobalValuesDB()
