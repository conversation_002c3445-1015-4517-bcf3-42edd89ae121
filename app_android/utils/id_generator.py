"""
Utility for generating unique action IDs for test cases
"""

import random
import string

def generate_action_id(length=10):
    """
    Generate a unique alphanumeric ID for an action

    Args:
        length (int): Length of the ID to generate (default: 10)

    Returns:
        str: A unique alphanumeric ID
    """
    # Generate a random alphanumeric string of specified length
    chars = string.ascii_letters + string.digits
    random_id = ''.join(random.choice(chars) for _ in range(length))

    return random_id

def add_action_ids_to_test_case(test_case_data):
    """
    Add unique action IDs to all actions in a test case

    Args:
        test_case_data (dict): The test case data

    Returns:
        dict: The updated test case data with action IDs
    """
    # Make a copy of the test case data to avoid modifying the original
    updated_test_case = test_case_data.copy()

    # Add action IDs to each action
    if 'actions' in updated_test_case:
        for action in updated_test_case['actions']:
            # Skip if action already has an ID
            if 'action_id' in action:
                continue

            # Generate and add the action ID
            action['action_id'] = generate_action_id()

            # Handle multi-step actions
            if action.get('type') == 'multiStep' and 'test_case_steps' in action:
                for step in action['test_case_steps']:
                    # Skip if step already has an ID
                    if 'action_id' in step:
                        continue

                    # Generate and add the action ID
                    step['action_id'] = generate_action_id()

    return updated_test_case
