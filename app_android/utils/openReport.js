/**
 * Utility to open test reports in a browser
 */

const { exec } = require('child_process');
const path = require('path');
const os = require('os');
const reportGenerator = require('./reportGenerator');

/**
 * Open the latest test report in the default browser
 * 
 * @returns {Promise<boolean>} - True if a report was opened, false otherwise
 */
function openLatestReport() {
    const reportUrl = reportGenerator.getLatestReportUrl();
    
    if (!reportUrl) {
        console.log('No test reports found.');
        return Promise.resolve(false);
    }
    
    return openUrlInBrowser(reportUrl);
}

/**
 * Open a specific test report by name
 * 
 * @param {string} reportName - Name of the report file (e.g., "TestSuite_Execution_20231115_133045.html")
 * @returns {Promise<boolean>} - True if the report was opened, false otherwise
 */
function openReport(reportName) {
    if (!reportName) {
        return openLatestReport();
    }
    
    const reportUrl = `reports/${reportName}`;
    return openUrlInBrowser(reportUrl);
}

/**
 * Open a URL in the default browser
 * 
 * @param {string} url - URL to open
 * @returns {Promise<boolean>} - True if the URL was opened, false otherwise
 */
function openUrlInBrowser(url) {
    // Determine the command to open a URL based on the OS
    let command;
    
    switch (os.platform()) {
        case 'win32':
            command = `start "${url}"`;
            break;
        case 'darwin': // macOS
            command = `open "${url}"`;
            break;
        default: // Linux and others
            command = `xdg-open "${url}"`;
            break;
    }
    
    return new Promise((resolve, reject) => {
        exec(command, (error) => {
            if (error) {
                console.error(`Failed to open report: ${error.message}`);
                resolve(false);
            } else {
                console.log(`Opened report: ${url}`);
                resolve(true);
            }
        });
    });
}

module.exports = {
    openLatestReport,
    openReport
}; 