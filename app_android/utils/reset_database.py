#!/usr/bin/env python3
"""
Database Reset Script

This script completely resets the database by:
1. Deleting the database file
2. Creating a new empty database
3. Initializing the schema

Usage:
    python reset_database.py
"""

import os
import sys
import sqlite3
import time
import glob
import shutil
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s] %(levelname)s: %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

# Get the absolute path to the app directory
APP_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
DB_PATH = os.path.join(APP_DIR, 'database.db')

def reset_database():
    """
    Completely reset the database by deleting it and creating a new one.
    """
    logger.info(f"Starting aggressive database reset for: {DB_PATH}")
    
    # Check if database exists
    if not os.path.exists(DB_PATH):
        logger.info(f"Database file does not exist: {DB_PATH}")
        create_new_database()
        return True
    
    # Try to close any open connections
    try:
        logger.info("Attempting to close any open database connections...")
        conn = sqlite3.connect(DB_PATH)
        conn.close()
        logger.info("Closed database connection")
    except Exception as e:
        logger.warning(f"Error closing database connection: {str(e)}")
    
    # Wait a moment to ensure connections are closed
    time.sleep(0.5)
    
    # Create a backup before deleting
    backup_path = f"{DB_PATH}.bak"
    try:
        shutil.copy2(DB_PATH, backup_path)
        logger.info(f"Created database backup at: {backup_path}")
    except Exception as e:
        logger.warning(f"Error creating backup: {str(e)}")
    
    # Delete the database file
    try:
        os.remove(DB_PATH)
        logger.info(f"Deleted database file: {DB_PATH}")
    except Exception as e:
        logger.error(f"Error deleting database file: {str(e)}")
        
        # If we can't delete the file, try to truncate all tables
        try:
            logger.info("Attempting to truncate all tables...")
            conn = sqlite3.connect(DB_PATH)
            cursor = conn.cursor()
            
            # Get all table names
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()
            
            # Delete all data from each table
            for table in tables:
                table_name = table[0]
                if table_name != 'sqlite_sequence':  # Skip SQLite internal tables
                    logger.info(f"Truncating table: {table_name}")
                    cursor.execute(f"DELETE FROM {table_name};")
            
            # Reset all auto-increment counters
            cursor.execute("DELETE FROM sqlite_sequence;")
            
            # Commit changes
            conn.commit()
            conn.close()
            logger.info("Truncated all tables in database")
            
            # Vacuum the database to reclaim space
            conn = sqlite3.connect(DB_PATH)
            conn.execute("VACUUM;")
            conn.close()
            logger.info("Vacuumed database")
            
            return True
        except Exception as trunc_error:
            logger.error(f"Error truncating tables: {str(trunc_error)}")
            return False
    
    # Also delete any journal or temporary files
    for temp_file in glob.glob(f"{DB_PATH}-*"):
        try:
            os.remove(temp_file)
            logger.info(f"Deleted temporary database file: {temp_file}")
        except Exception as temp_error:
            logger.warning(f"Error deleting temporary file {temp_file}: {str(temp_error)}")
    
    # Create a new database
    create_new_database()
    return True

def create_new_database():
    """
    Create a new database with the required schema.
    """
    logger.info(f"Creating new database at: {DB_PATH}")
    
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    # Create tables
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS test_suites (
        id TEXT PRIMARY KEY,
        name TEXT,
        timestamp TEXT,
        status TEXT,
        duration TEXT
    )
    ''')
    
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS test_cases (
        id TEXT PRIMARY KEY,
        suite_id TEXT,
        name TEXT,
        status TEXT,
        duration TEXT,
        FOREIGN KEY (suite_id) REFERENCES test_suites(id)
    )
    ''')
    
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS test_steps (
        id TEXT PRIMARY KEY,
        case_id TEXT,
        name TEXT,
        status TEXT,
        duration TEXT,
        error TEXT,
        FOREIGN KEY (case_id) REFERENCES test_cases(id)
    )
    ''')
    
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS screenshots (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        suite_id TEXT,
        test_idx INTEGER,
        step_idx INTEGER,
        filename TEXT,
        path TEXT,
        timestamp TEXT
    )
    ''')
    
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS standardized_screenshots (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        test_idx INTEGER,
        step_idx INTEGER,
        filename TEXT,
        timestamp TEXT
    )
    ''')
    
    # Commit changes and close connection
    conn.commit()
    conn.close()
    
    logger.info("Created new database with schema")

def check_database():
    """
    Check the database state after reset.
    """
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        # Check tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        logger.info(f"Database tables: {[table[0] for table in tables]}")
        
        # Check screenshot count
        cursor.execute("SELECT COUNT(*) FROM screenshots;")
        screenshot_count = cursor.fetchone()[0]
        logger.info(f"Screenshot count: {screenshot_count}")
        
        # Check standardized screenshot count
        cursor.execute("SELECT COUNT(*) FROM standardized_screenshots;")
        std_screenshot_count = cursor.fetchone()[0]
        logger.info(f"Standardized screenshot count: {std_screenshot_count}")
        
        conn.close()
        
        return screenshot_count == 0 and std_screenshot_count == 0
    except Exception as e:
        logger.error(f"Error checking database: {str(e)}")
        return False

if __name__ == "__main__":
    logger.info("Starting database reset script")
    
    success = reset_database()
    if success:
        logger.info("Database reset successful")
        
        # Verify the reset
        if check_database():
            logger.info("Database verification successful - all screenshot tables are empty")
        else:
            logger.warning("Database verification failed - screenshot tables may not be empty")
    else:
        logger.error("Database reset failed")
