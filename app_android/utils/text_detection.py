#!/usr/bin/env python3
"""
Text Detection Utility

This module provides enhanced text detection capabilities for finding text in screenshots.
It uses a combination of OCR methods to reliably detect text on mobile device screens.
"""

import os
import cv2
import numpy as np
import pytesseract
import logging
from PIL import Image

# Configure logging
logger = logging.getLogger(__name__)

def detect_text_in_image(image_path, text_to_find, output_dir=None):
    """
    Find specific text in an image using multiple detection methods.
    
    Args:
        image_path (str): Path to the input image
        text_to_find (str): Text to find in the image
        output_dir (str, optional): Directory to save output images
        
    Returns:
        dict or None: Dictionary with text and coordinates if found, None otherwise
    """
    logger.info(f"Looking for '{text_to_find}' in {image_path}")
    
    # Check if the image exists
    if not os.path.exists(image_path):
        logger.error(f"Error: Image file '{image_path}' not found.")
        return None
    
    # Read the image
    img = cv2.imread(image_path)
    if img is None:
        logger.error(f"Error: Could not read image at {image_path}")
        return None
    
    # Get image dimensions
    height, width, _ = img.shape
    logger.info(f"Image dimensions: {width}x{height}")
    
    # Create a copy for visualization
    img_copy = img.copy()
    
    # Convert to grayscale
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    
    # Use Pytesseract to get detailed information about detected text
    data = pytesseract.image_to_data(gray, output_type=pytesseract.Output.DICT)
    
    # Find text regions
    text_regions = []
    n_boxes = len(data['text'])
    
    for i in range(n_boxes):
        if int(data['conf'][i]) > 0:  # Only consider text with confidence > 0
            text = data['text'][i].strip()
            if text:  # Only consider non-empty text
                x = data['left'][i]
                y = data['top'][i]
                w = data['width'][i]
                h = data['height'][i]
                
                # Calculate center coordinates
                center_x = x + w // 2
                center_y = y + h // 2
                
                # Add to results
                text_regions.append({
                    'text': text,
                    'coordinates': {
                        'x1': x,
                        'y1': y,
                        'x2': x + w,
                        'y2': y + h,
                        'center_x': center_x,
                        'center_y': center_y,
                        'width': w,
                        'height': h
                    },
                    'confidence': int(data['conf'][i]) / 100.0
                })
                
                # Draw rectangle on the copy
                cv2.rectangle(img_copy, (x, y), (x + w, y + h), (0, 255, 0), 2)
                
                # Add text label
                cv2.putText(img_copy, text, (x, y - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
    
    # Save the annotated image if output_dir is provided
    if output_dir:
        os.makedirs(output_dir, exist_ok=True)
        output_path = os.path.join(output_dir, f"all_text_{os.path.basename(image_path)}")
        cv2.imwrite(output_path, img_copy)
        logger.info(f"Saved annotated image to {output_path}")
    
    # Print all detected text regions
    logger.info(f"Found {len(text_regions)} text regions")
    
    # Find the region that contains the text we're looking for
    matching_regions = []
    for region in text_regions:
        if text_to_find.lower() in region['text'].lower():
            matching_regions.append(region)
    
    if matching_regions:
        logger.info(f"Found {len(matching_regions)} regions containing '{text_to_find}'")
        
        # Return the first matching region
        result = matching_regions[0]
        
        # Draw a circle at the center point on the copy
        cv2.circle(img_copy, (result['coordinates']['center_x'], result['coordinates']['center_y']), 10, (0, 0, 255), -1)
        
        # Add text label
        cv2.putText(img_copy, f"{text_to_find} (match)", 
                   (result['coordinates']['center_x'] - 60, result['coordinates']['center_y'] - 20), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
        
        # Save the final image if output_dir is provided
        if output_dir:
            final_path = os.path.join(output_dir, f"found_{text_to_find}_{os.path.basename(image_path)}")
            cv2.imwrite(final_path, img_copy)
            logger.info(f"Saved match image to {final_path}")
        
        return result
    
    logger.info(f"No regions containing '{text_to_find}' found")
    
    # If no exact match, try to find the text in the bottom navigation area
    # This is common for tabs like "Sharing", "Browse", etc.
    bottom_area = gray[int(height * 0.8):height, :]
    bottom_text = pytesseract.image_to_string(bottom_area).strip()
    logger.info(f"Text in bottom area: {bottom_text}")
    
    if text_to_find.lower() in bottom_text.lower():
        logger.info(f"Found '{text_to_find}' in bottom area text")
        
        # For bottom navigation, we'll use a more sophisticated approach
        # We'll analyze the bottom area to find the tab positions
        
        # First, try to find all text in the bottom area
        bottom_data = pytesseract.image_to_data(bottom_area, output_type=pytesseract.Output.DICT)
        bottom_regions = []
        
        for i in range(len(bottom_data['text'])):
            if int(bottom_data['conf'][i]) > 0:
                text = bottom_data['text'][i].strip()
                if text:
                    x = bottom_data['left'][i]
                    y = bottom_data['top'][i] + int(height * 0.8)  # Adjust y for the bottom area offset
                    w = bottom_data['width'][i]
                    h = bottom_data['height'][i]
                    
                    # Calculate center coordinates
                    center_x = x + w // 2
                    center_y = y + h // 2
                    
                    bottom_regions.append({
                        'text': text,
                        'coordinates': {
                            'center_x': center_x,
                            'center_y': center_y
                        }
                    })
                    
                    # Draw on the visualization
                    cv2.rectangle(img_copy, (x, y), (x + w, y + h), (255, 0, 0), 2)
                    cv2.putText(img_copy, text, (x, y - 5), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 2)
        
        # If we found text regions in the bottom area, try to find our target
        for region in bottom_regions:
            if text_to_find.lower() in region['text'].lower():
                logger.info(f"Found '{text_to_find}' in bottom navigation at ({region['coordinates']['center_x']}, {region['coordinates']['center_y']})")
                
                # Draw a circle at the center point
                cv2.circle(img_copy, (region['coordinates']['center_x'], region['coordinates']['center_y']), 10, (0, 0, 255), -1)
                
                # Save the final image if output_dir is provided
                if output_dir:
                    final_path = os.path.join(output_dir, f"bottom_nav_{text_to_find}_{os.path.basename(image_path)}")
                    cv2.imwrite(final_path, img_copy)
                    logger.info(f"Saved bottom nav match image to {final_path}")
                
                return {
                    'text': text_to_find,
                    'coordinates': {
                        'center_x': region['coordinates']['center_x'],
                        'center_y': region['coordinates']['center_y'],
                        'x1': region['coordinates']['center_x'] - 10,
                        'y1': region['coordinates']['center_y'] - 10,
                        'x2': region['coordinates']['center_x'] + 10,
                        'y2': region['coordinates']['center_y'] + 10,
                        'width': 20,
                        'height': 20
                    }
                }
        
        # If we still couldn't find the exact tab, estimate its position based on the layout
        # For iOS bottom navigation, tabs are typically evenly spaced
        logger.info("Estimating tab position based on bottom navigation layout")
        
        # Get all visible tabs in the bottom area
        visible_tabs = []
        for region in bottom_regions:
            # Filter out non-tab text (tabs are usually short words)
            if len(region['text']) < 15:
                visible_tabs.append(region)
        
        # Sort tabs by x-coordinate
        visible_tabs.sort(key=lambda tab: tab['coordinates']['center_x'])
        
        # Log the visible tabs
        for i, tab in enumerate(visible_tabs):
            logger.info(f"Tab {i+1}: '{tab['text']}' at x={tab['coordinates']['center_x']}")
        
        # If we have at least 2 tabs, we can estimate positions
        if len(visible_tabs) >= 2:
            # Calculate the average y-coordinate for the tabs
            avg_y = sum(tab['coordinates']['center_y'] for tab in visible_tabs) / len(visible_tabs)
            
            # Calculate the average spacing between tabs
            if len(visible_tabs) > 1:
                spacings = [visible_tabs[i+1]['coordinates']['center_x'] - visible_tabs[i]['coordinates']['center_x'] 
                           for i in range(len(visible_tabs)-1)]
                avg_spacing = sum(spacings) / len(spacings)
                logger.info(f"Average spacing between tabs: {avg_spacing}")
            
            # Try to find where our target tab should be
            # For common 3-tab layouts: Summary, Sharing, Browse
            if len(visible_tabs) == 2:
                # If we see 2 tabs, assume they're the first and last, and our target is in the middle
                if visible_tabs[0]['text'].lower() == "summary" and visible_tabs[1]['text'].lower() == "browse":
                    # Our target (Sharing) should be in the middle
                    estimated_x = (visible_tabs[0]['coordinates']['center_x'] + visible_tabs[1]['coordinates']['center_x']) // 2
                    estimated_y = avg_y
                    logger.info(f"Estimated position for '{text_to_find}' between Summary and Browse: ({estimated_x}, {estimated_y})")
                else:
                    # If we can't determine the exact layout, use a generic approach
                    # Divide the screen width into equal sections based on the number of expected tabs
                    num_expected_tabs = 3  # Assume 3 tabs: Summary, Sharing, Browse
                    section_width = width / num_expected_tabs
                    
                    # For "Sharing" (middle tab in a 3-tab layout)
                    if text_to_find.lower() in ["sharing", "share"]:
                        estimated_x = int(width / 2)  # Middle of screen
                    # For "Summary" (left tab in a 3-tab layout)
                    elif text_to_find.lower() == "summary":
                        estimated_x = int(section_width / 2)  # Middle of first section
                    # For "Browse" (right tab in a 3-tab layout)
                    elif text_to_find.lower() == "browse":
                        estimated_x = int(width - section_width / 2)  # Middle of last section
                    else:
                        # For unknown tabs, use the middle of the screen
                        estimated_x = int(width / 2)
                    
                    estimated_y = int(height * 0.95)  # Near the bottom of the screen
                    logger.info(f"Estimated position for '{text_to_find}' using section approach: ({estimated_x}, {estimated_y})")
            else:
                # For unknown layouts, use a generic approach
                # Divide the screen width into equal sections based on the number of expected tabs
                num_expected_tabs = 3  # Assume 3 tabs: Summary, Sharing, Browse
                section_width = width / num_expected_tabs
                
                # For "Sharing" (middle tab in a 3-tab layout)
                if text_to_find.lower() in ["sharing", "share"]:
                    estimated_x = int(width / 2)  # Middle of screen
                # For "Summary" (left tab in a 3-tab layout)
                elif text_to_find.lower() == "summary":
                    estimated_x = int(section_width / 2)  # Middle of first section
                # For "Browse" (right tab in a 3-tab layout)
                elif text_to_find.lower() == "browse":
                    estimated_x = int(width - section_width / 2)  # Middle of last section
                else:
                    # For unknown tabs, use the middle of the screen
                    estimated_x = int(width / 2)
                
                estimated_y = int(height * 0.95)  # Near the bottom of the screen
                logger.info(f"Estimated position for '{text_to_find}' using section approach: ({estimated_x}, {estimated_y})")
        else:
            # If we can't find any tabs, use a generic approach
            # Divide the screen width into equal sections based on the number of expected tabs
            num_expected_tabs = 3  # Assume 3 tabs: Summary, Sharing, Browse
            section_width = width / num_expected_tabs
            
            # For "Sharing" (middle tab in a 3-tab layout)
            if text_to_find.lower() in ["sharing", "share"]:
                estimated_x = int(width / 2)  # Middle of screen
            # For "Summary" (left tab in a 3-tab layout)
            elif text_to_find.lower() == "summary":
                estimated_x = int(section_width / 2)  # Middle of first section
            # For "Browse" (right tab in a 3-tab layout)
            elif text_to_find.lower() == "browse":
                estimated_x = int(width - section_width / 2)  # Middle of last section
            else:
                # For unknown tabs, use the middle of the screen
                estimated_x = int(width / 2)
            
            estimated_y = int(height * 0.95)  # Near the bottom of the screen
            logger.info(f"Estimated position for '{text_to_find}' using generic approach: ({estimated_x}, {estimated_y})")
        
        # Draw the estimated position
        cv2.circle(img_copy, (estimated_x, estimated_y), 15, (0, 0, 255), -1)
        cv2.putText(img_copy, f"{text_to_find} (estimated)", 
                   (estimated_x - 80, estimated_y - 20), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
        
        # Save the final image if output_dir is provided
        if output_dir:
            final_path = os.path.join(output_dir, f"estimated_{text_to_find}_{os.path.basename(image_path)}")
            cv2.imwrite(final_path, img_copy)
            logger.info(f"Saved estimated position image to {final_path}")
        
        return {
            'text': text_to_find,
            'coordinates': {
                'center_x': estimated_x,
                'center_y': estimated_y,
                'x1': estimated_x - 10,
                'y1': estimated_y - 10,
                'x2': estimated_x + 10,
                'y2': estimated_y + 10,
                'width': 20,
                'height': 20,
                'estimated': True
            }
        }
    
    return None

def scale_coordinates(x, y, img_width, img_height, device_width, device_height):
    """
    Scale coordinates from image dimensions to device dimensions
    
    Args:
        x (int): X-coordinate in the image
        y (int): Y-coordinate in the image
        img_width (int): Width of the image
        img_height (int): Height of the image
        device_width (int): Width of the device screen
        device_height (int): Height of the device screen
        
    Returns:
        tuple: (scaled_x, scaled_y)
    """
    # Calculate scaling factors
    scale_x = device_width / img_width
    scale_y = device_height / img_height
    
    # Apply scaling
    scaled_x = int(x * scale_x)
    scaled_y = int(y * scale_y)
    
    return scaled_x, scaled_y
