#!/usr/bin/env python
import os
import importlib
import sys
import inspect

def check_action_files():
    # Path to the actions directory
    actions_dir = os.path.join('app', 'actions')
    
    print(f"Checking action files in: {os.path.abspath(actions_dir)}")
    
    # Get all Python files in the actions directory
    action_files = [f for f in os.listdir(actions_dir) 
                   if f.endswith('.py') 
                   and not f.startswith('__') 
                   and f not in ['base_action.py', 'action_factory.py']]
    
    print(f"Found {len(action_files)} action files")
    
    # Add the parent directory to the path to be able to import
    sys.path.insert(0, os.path.abspath('.'))
    
    for action_file in action_files:
        try:
            # Extract expected action type and class name
            file_stem = action_file[:-3]  # Remove .py
            expected_action_type = file_stem.replace('_action', '')
            
            # Calculate expected camelCase action type
            if '_' in expected_action_type:
                parts = expected_action_type.split('_')
                expected_camel_type = parts[0] + ''.join(part.capitalize() for part in parts[1:])
            else:
                expected_camel_type = expected_action_type
                
            # Calculate expected class name (CamelCase of file_stem)
            expected_class_name = ''.join(word.capitalize() for word in file_stem.split('_'))
            
            # Import the module
            module_name = f"app.actions.{file_stem}"
            module = importlib.import_module(module_name)
            
            # Check if the expected class exists in the module
            if hasattr(module, expected_class_name):
                action_class = getattr(module, expected_class_name)
                if inspect.isclass(action_class):
                    print(f"✅ {action_file}: Found class {expected_class_name} → Action type: {expected_camel_type}")
                else:
                    print(f"❌ {action_file}: {expected_class_name} is not a class")
            else:
                print(f"❌ {action_file}: Missing expected class {expected_class_name}")
                # Show all classes in the module
                for name, obj in inspect.getmembers(module, inspect.isclass):
                    if obj.__module__ == module.__name__:
                        print(f"   Found class: {name}")
                        
        except Exception as e:
            print(f"❌ Error checking {action_file}: {str(e)}")
    
if __name__ == "__main__":
    check_action_files() 