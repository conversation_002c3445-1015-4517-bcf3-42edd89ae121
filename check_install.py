#!/usr/bin/env python
"""
Check installation script - verifies that all dependencies are correctly installed
"""
import sys
import pkg_resources

def check_dependencies():
    """Check if all required packages are installed"""
    print(f"Python version: {sys.version}")
    
    with open('requirements.txt', 'r') as f:
        requirements = [line.strip() for line in f if line.strip() and not line.startswith('#')]
    
    missing = []
    installed = []
    
    for requirement in requirements:
        package = requirement.split('==')[0]
        try:
            dist = pkg_resources.get_distribution(package)
            installed.append(f"{dist.key} {dist.version}")
        except pkg_resources.DistributionNotFound:
            missing.append(requirement)
    
    print("\nInstalled packages:")
    for pkg in installed:
        print(f"✓ {pkg}")
    
    if missing:
        print("\nMissing packages:")
        for pkg in missing:
            print(f"✗ {pkg}")
        print("\nTo install missing packages, run: pip install -r requirements.txt")
        return False
    
    print("\nAll required packages are installed! ✓")
    return True

def check_adb():
    """Check if ADB is installed and accessible"""
    import subprocess
    try:
        result = subprocess.run(['adb', 'version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"\nADB is installed:\n{result.stdout.splitlines()[0]}")
            return True
        else:
            print("\nADB is installed but returned an error:")
            print(result.stderr)
            return False
    except FileNotFoundError:
        print("\nADB is not installed or not in your PATH")
        print("Please install Android SDK Platform Tools and add it to your PATH")
        return False

def check_opencv():
    """Check if OpenCV is working properly"""
    try:
        import cv2
        import numpy as np
        
        # Create a simple test image
        test_img = np.zeros((100, 100, 3), dtype=np.uint8)
        gray = cv2.cvtColor(test_img, cv2.COLOR_BGR2GRAY)
        
        print(f"\nOpenCV is working properly (version {cv2.__version__}) ✓")
        return True
    except Exception as e:
        print(f"\nOpenCV test failed: {e}")
        return False

if __name__ == "__main__":
    print("Checking installation...\n")
    deps_ok = check_dependencies()
    adb_ok = check_adb()
    cv_ok = check_opencv()
    
    if deps_ok and adb_ok and cv_ok:
        print("\nAll checks passed! Your installation appears to be working correctly.")
        print("You can start the application with: python run.py")
    else:
        print("\nSome checks failed. Please resolve the issues above before running the application.")
        sys.exit(1) 