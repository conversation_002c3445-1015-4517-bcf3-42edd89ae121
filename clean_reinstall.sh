#!/bin/bash

# Exit on error
set -e

echo "===== Cleaning Python environment and reinstalling packages ====="

# Create a requirements file to save our current packages
echo "Creating requirements backup..."
pip freeze > requirements_backup.txt

# Filter out packages you want to keep
echo "Filtering out essential packages..."
grep -v -E "^(pip|setuptools|wheel|conda).*" requirements_backup.txt > to_uninstall.txt

# Uninstall all packages except essential ones
echo "Uninstalling packages..."
pip uninstall -y -r to_uninstall.txt || true

# Clear pip cache
echo "Clearing pip cache..."
pip cache purge

# Clear conda cache if using conda
echo "Clearing conda cache..."
conda clean -a -y || true

# Install Cython first (needed for gevent)
echo "Installing Cython..."
pip install Cython==3.0.9

# Install required packages for your project
echo "Installing required packages with some exclusions..."
grep -v "gevent" requirements.txt > requirements_filtered.txt
pip install -r requirements_filtered.txt

# Install compatible version of gevent for Python 3.12
echo "Installing compatible gevent version..."
pip install gevent==23.9.1 --no-build-isolation

# Install Appium-Python-Client with specific version for compatibility
echo "Installing Appium-Python-Client..."
pip install Appium-Python-Client==2.8.1

# Install additional packages needed for mobile automation
echo "Installing additional packages..."
pip install facebook-wda tidevice easyocr

# Install uiautomator2 for Android
echo "Installing uiautomator2..."
pip install uiautomator2

# Check the installed packages
echo "Checking installed packages..."
pip list | grep -E "Appium|gevent|facebook-wda|tidevice|easyocr|uiautomator2"

echo "===== Environment has been cleaned and packages reinstalled ====="
echo "You may need to restart your application or IDE for changes to take effect." 