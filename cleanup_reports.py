#!/usr/bin/env python3
"""
Script to clean up all test reports and Allure results
"""

import os
import shutil
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("CleanupReports")

def cleanup_reports():
    """Clean up all test reports and Allure results"""
    # Get the app root directory
    app_dir = os.path.dirname(os.path.abspath(__file__))
    
    # Directories to clean
    reports_dir = os.path.join(app_dir, 'reports')
    allure_results_dir = os.path.join(app_dir, 'reports', 'allure-results')
    allure_report_dir = os.path.join(app_dir, 'reports', 'allure-report')
    
    # Clean up reports directory
    if os.path.exists(reports_dir):
        logger.info(f"Cleaning up reports directory: {reports_dir}")
        for item in os.listdir(reports_dir):
            item_path = os.path.join(reports_dir, item)
            if os.path.isdir(item_path):
                try:
                    shutil.rmtree(item_path)
                    logger.info(f"Removed directory: {item_path}")
                except Exception as e:
                    logger.error(f"Error removing directory {item_path}: {e}")
            elif os.path.isfile(item_path) and item.endswith('.zip'):
                try:
                    os.remove(item_path)
                    logger.info(f"Removed file: {item_path}")
                except Exception as e:
                    logger.error(f"Error removing file {item_path}: {e}")
    
    # Create necessary directories
    os.makedirs(reports_dir, exist_ok=True)
    os.makedirs(allure_results_dir, exist_ok=True)
    os.makedirs(allure_report_dir, exist_ok=True)
    
    logger.info("Reports cleanup completed")

if __name__ == "__main__":
    cleanup_reports()
