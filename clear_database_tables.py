#!/usr/bin/env python3
import os
import sqlite3
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Database paths
DB_PATHS = [
    os.path.join(os.getcwd(), 'data', 'test_execution.db'),
    os.path.join(os.getcwd(), 'app', 'data', 'global_values.db'),
    os.path.join(os.getcwd(), 'app', 'data', 'settings.db'),
    os.path.join(os.getcwd(), 'app', 'database.db')
]

def clear_table(conn, table_name):
    """Delete all records from a table while preserving its structure."""
    try:
        cursor = conn.cursor()
        cursor.execute(f"DELETE FROM {table_name}")
        conn.commit()
        row_count = cursor.rowcount if cursor.rowcount >= 0 else "unknown"
        logger.info(f"Cleared {row_count} rows from table: {table_name}")
        return True
    except sqlite3.Error as e:
        logger.error(f"Error clearing table {table_name}: {e}")
        return False

def get_tables(conn):
    """Get all table names from a database."""
    cursor = conn.cursor()
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    return [row[0] for row in cursor.fetchall()]

def clear_database(db_path):
    """Clear all tables in a database."""
    if not os.path.exists(db_path):
        logger.warning(f"Database file not found: {db_path}")
        return False
    
    try:
        logger.info(f"Connecting to database: {db_path}")
        conn = sqlite3.connect(db_path)
        
        # Get all tables
        tables = get_tables(conn)
        logger.info(f"Found {len(tables)} tables in {db_path}: {', '.join(tables)}")
        
        # Clear each table
        for table in tables:
            clear_table(conn, table)
        
        # Vacuum the database to reclaim space
        conn.execute("VACUUM")
        logger.info(f"Vacuumed database: {db_path}")
        
        conn.close()
        logger.info(f"Successfully cleared all tables in: {db_path}")
        return True
    except sqlite3.Error as e:
        logger.error(f"Error clearing database {db_path}: {e}")
        return False

def main():
    """Main function to clear all database tables."""
    logger.info("Starting database clearing process")
    
    success_count = 0
    for db_path in DB_PATHS:
        if clear_database(db_path):
            success_count += 1
    
    logger.info(f"Database clearing completed. Successfully cleared {success_count}/{len(DB_PATHS)} databases.")

if __name__ == "__main__":
    main()
