"""
Android-specific configuration for Mobile App Automation Tool
"""
import os
from pathlib import Path

# Base configuration
BASE_DIR = Path(__file__).parent
APP_DIR = BASE_DIR / 'app_android'

# Directory configuration with defaults
DIRECTORIES = {
    'TEST_CASES': BASE_DIR / 'test_cases',
    'REPORTS': BASE_DIR / 'reports',
    'SCREENSHOTS': BASE_DIR / 'screenshots',
    'REFERENCE_IMAGES': BASE_DIR / '/Users/<USER>/Documents/automation-tool/reference_images',
    'TEST_SUITES': BASE_DIR / 'test_suites',
    'RESULTS': BASE_DIR / 'reports' / 'suites',
    'RECORDINGS': BASE_DIR / 'recordings',
    'TEMP_FILES': BASE_DIR / 'temp',
}

# Platform identification
PLATFORM = 'Android'
PLATFORM_NAME = 'Android'

# Server configuration
DEFAULT_FLASK_PORT = 8081  # Different from iOS to allow parallel running
DEFAULT_APPIUM_PORT = 4724  # Can be same as iOS since they're separate instances
DEFAULT_WDA_PORT = None  # Not used for Android

# Android-specific tool paths
ADB_PATH = 'adb'  # Assumes adb is in PATH
AAPT_PATH = 'aapt'  # Android Asset Packaging Tool
FASTBOOT_PATH = 'fastboot'

# Android SDK configuration
ANDROID_HOME = os.environ.get('ANDROID_HOME', '/usr/local/android-sdk')
ANDROID_SDK_ROOT = os.environ.get('ANDROID_SDK_ROOT', ANDROID_HOME)

# Platform tools
PLATFORM_TOOLS_PATH = os.path.join(ANDROID_SDK_ROOT, 'platform-tools')
BUILD_TOOLS_PATH = os.path.join(ANDROID_SDK_ROOT, 'build-tools')

# UiAutomator2 configuration
UIAUTOMATOR2_SERVER_LAUNCH_TIMEOUT = 60000
UIAUTOMATOR2_SERVER_INSTALL_TIMEOUT = 60000
ADB_EXEC_TIMEOUT = 60000

# Android Appium capabilities defaults
ANDROID_CAPABILITIES = {
    'platformName': 'Android',
    'automationName': 'UiAutomator2',
    'newCommandTimeout': 300,
    'uiautomator2ServerLaunchTimeout': UIAUTOMATOR2_SERVER_LAUNCH_TIMEOUT,
    'uiautomator2ServerInstallTimeout': UIAUTOMATOR2_SERVER_INSTALL_TIMEOUT,
    'adbExecTimeout': ADB_EXEC_TIMEOUT,
    'skipServerInstallation': False,
    'skipDeviceInitialization': False,
    'ignoreHiddenApiPolicyError': True,
    'disableWindowAnimation': True,
    'autoGrantPermissions': True,
    'dontStopAppOnReset': True,
    'noReset': True,
    'fullReset': False,
    'unicodeKeyboard': True,
    'resetKeyboard': True
}

# Android-specific action types
ANDROID_ACTION_TYPES = [
    'tap',
    'swipe',
    'type',
    'wait',
    'screenshot',
    'checkIfExists',
    'tapOnText',
    'tapOnImage',
    'swipeTillVisible',
    'androidFunctions',  # Android-specific functions
    'qrBarcodeScan'
]

# Android device discovery settings
DEVICE_DISCOVERY = {
    'command': ['adb', 'devices', '-l'],
    'timeout': 10,
    'retry_count': 3,
    'retry_delay': 2
}

# Android device properties to retrieve
DEVICE_PROPERTIES = {
    'model': 'ro.product.model',
    'manufacturer': 'ro.product.manufacturer',
    'brand': 'ro.product.brand',
    'device': 'ro.product.device',
    'android_version': 'ro.build.version.release',
    'api_level': 'ro.build.version.sdk',
    'build_id': 'ro.build.id',
    'serial': 'ro.serialno',
    'board': 'ro.product.board',
    'hardware': 'ro.hardware',
    'cpu_abi': 'ro.product.cpu.abi',
    'cpu_abi2': 'ro.product.cpu.abi2',
    'density': 'ro.sf.lcd_density',
    'resolution': 'wm size'
}

# Android system settings
SYSTEM_SETTINGS = {
    'disable_animations': [
        'settings put global window_animation_scale 0',
        'settings put global transition_animation_scale 0',
        'settings put global animator_duration_scale 0'
    ],
    'enable_animations': [
        'settings put global window_animation_scale 1',
        'settings put global transition_animation_scale 1',
        'settings put global animator_duration_scale 1'
    ],
    'stay_awake': 'settings put global stay_on_while_plugged_in 3',
    'disable_stay_awake': 'settings put global stay_on_while_plugged_in 0'
}

# Android key codes for common actions
ANDROID_KEY_CODES = {
    'home': 3,
    'back': 4,
    'call': 5,
    'endcall': 6,
    'volume_up': 24,
    'volume_down': 25,
    'power': 26,
    'camera': 27,
    'clear': 28,
    'menu': 82,
    'search': 84,
    'enter': 66,
    'delete': 67,
    'escape': 111,
    'app_switch': 187,
    'notification': 83,
    'settings': 176
}

# Android package management
PACKAGE_MANAGER = {
    'list_packages': 'pm list packages',
    'list_system_packages': 'pm list packages -s',
    'list_user_packages': 'pm list packages -3',
    'package_info': 'pm dump',
    'clear_data': 'pm clear',
    'install_apk': 'pm install',
    'uninstall_package': 'pm uninstall'
}

# Android activity manager
ACTIVITY_MANAGER = {
    'start_activity': 'am start',
    'start_service': 'am startservice',
    'broadcast': 'am broadcast',
    'force_stop': 'am force-stop',
    'kill': 'am kill',
    'get_current_activity': 'dumpsys activity activities | grep mResumedActivity'
}

# Android input methods
INPUT_METHODS = {
    'tap': 'input tap',
    'swipe': 'input swipe',
    'text': 'input text',
    'keyevent': 'input keyevent',
    'roll': 'input roll',
    'press': 'input press',
    'release': 'input release'
}

# Android screenshot settings
SCREENSHOT = {
    'command': 'screencap -p',
    'default_path': '/sdcard/screenshot.png',
    'format': 'png',
    'quality': 100
}

# Android logcat settings
LOGCAT = {
    'command': 'logcat',
    'clear': 'logcat -c',
    'dump': 'logcat -d',
    'filter_levels': ['V', 'D', 'I', 'W', 'E', 'F'],
    'default_filter': '*:W'  # Warning and above
}

# Android network settings
NETWORK = {
    'wifi_enable': 'svc wifi enable',
    'wifi_disable': 'svc wifi disable',
    'data_enable': 'svc data enable',
    'data_disable': 'svc data disable',
    'airplane_mode_on': 'settings put global airplane_mode_on 1',
    'airplane_mode_off': 'settings put global airplane_mode_on 0'
}

# Android file system paths
ANDROID_PATHS = {
    'sdcard': '/sdcard',
    'external_storage': '/storage/emulated/0',
    'system': '/system',
    'data': '/data',
    'cache': '/cache',
    'tmp': '/tmp',
    'downloads': '/sdcard/Download',
    'pictures': '/sdcard/Pictures',
    'dcim': '/sdcard/DCIM'
}

# Test execution settings
TEST_EXECUTION = {
    'default_timeout': 30,
    'element_wait_timeout': 10,
    'screenshot_on_failure': True,
    'screenshot_on_success': False,
    'video_recording': False,
    'performance_monitoring': False
}

# Logging configuration
LOGGING_CONFIG = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'standard': {
            'format': '%(asctime)s [%(levelname)s] %(name)s: %(message)s'
        },
        'detailed': {
            'format': '%(asctime)s [%(levelname)s] %(name)s:%(lineno)d: %(message)s'
        }
    },
    'handlers': {
        'default': {
            'level': 'INFO',
            'formatter': 'standard',
            'class': 'logging.StreamHandler',
        },
        'file': {
            'level': 'DEBUG',
            'formatter': 'detailed',
            'class': 'logging.FileHandler',
            'filename': 'android_automation.log',
            'mode': 'a',
        }
    },
    'loggers': {
        '': {
            'handlers': ['default', 'file'],
            'level': 'DEBUG',
            'propagate': False
        }
    }
}

# Environment variables
ENVIRONMENT_VARIABLES = {
    'ANDROID_HOME': ANDROID_HOME,
    'ANDROID_SDK_ROOT': ANDROID_SDK_ROOT,
    'PATH': f"{PLATFORM_TOOLS_PATH}:{os.environ.get('PATH', '')}"
}

# Validation functions
def validate_android_environment():
    """Validate that Android development environment is properly set up"""
    issues = []
    
    # Check if adb is available
    try:
        import subprocess
        subprocess.run([ADB_PATH, 'version'], capture_output=True, check=True)
    except (subprocess.CalledProcessError, FileNotFoundError):
        issues.append("ADB not found in PATH. Please install Android SDK Platform Tools.")
    
    # Check ANDROID_HOME
    if not os.path.exists(ANDROID_HOME):
        issues.append(f"ANDROID_HOME path does not exist: {ANDROID_HOME}")
    
    # Check platform-tools
    if not os.path.exists(PLATFORM_TOOLS_PATH):
        issues.append(f"Platform tools not found: {PLATFORM_TOOLS_PATH}")
    
    return issues

def get_android_config():
    """Get complete Android configuration"""
    return {
        'platform': PLATFORM,
        'capabilities': ANDROID_CAPABILITIES,
        'action_types': ANDROID_ACTION_TYPES,
        'device_discovery': DEVICE_DISCOVERY,
        'device_properties': DEVICE_PROPERTIES,
        'key_codes': ANDROID_KEY_CODES,
        'paths': ANDROID_PATHS,
        'test_execution': TEST_EXECUTION,
        'environment': ENVIRONMENT_VARIABLES
    }
