#!/usr/bin/env python3
"""
Create database tables for test cases and test suites metadata
"""
import sqlite3
import os
import json
from datetime import datetime

def get_db_path():
    """Get the database path"""
    base_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'data')
    return os.path.join(base_path, 'test_execution.db')

def create_metadata_tables():
    """Create test_cases and test_suites metadata tables"""
    db_path = get_db_path()
    
    if not os.path.exists(db_path):
        print(f"Database not found at: {db_path}")
        return False
    
    print(f"Creating metadata tables in: {db_path}")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Create test_case_metadata table (avoid conflict with existing test_cases)
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS test_case_metadata (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                description TEXT,
                file_path TEXT NOT NULL,
                created_date TEXT NOT NULL,
                updated_date TEXT NOT NULL,
                device_id TEXT,
                action_count INTEGER DEFAULT 0,
                labels TEXT,  -- JSON string for labels array
                UNIQUE(file_path)
            )
        ''')

        # Create test_suite_metadata table (avoid conflict with existing test_suites)
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS test_suite_metadata (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                description TEXT,
                file_path TEXT NOT NULL,
                created_date TEXT NOT NULL,
                updated_date TEXT NOT NULL,
                test_case_count INTEGER DEFAULT 0,
                test_case_files TEXT,  -- JSON string for test case files array
                UNIQUE(file_path)
            )
        ''')
        
        # Create indexes for better performance
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_test_case_metadata_name ON test_case_metadata(name)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_test_case_metadata_updated ON test_case_metadata(updated_date)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_test_suite_metadata_name ON test_suite_metadata(name)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_test_suite_metadata_updated ON test_suite_metadata(updated_date)')

        conn.commit()

        # Verify tables were created
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name IN ('test_case_metadata', 'test_suite_metadata')")
        tables = cursor.fetchall()

        print(f"Created tables: {[table[0] for table in tables]}")

        # Show table schemas
        for table_name in ['test_case_metadata', 'test_suite_metadata']:
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = cursor.fetchall()
            print(f"\n{table_name} table schema:")
            for col in columns:
                print(f"  {col[1]} {col[2]} {'PRIMARY KEY' if col[5] else ''} {'NOT NULL' if col[3] else ''}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"Error creating metadata tables: {str(e)}")
        if 'conn' in locals():
            conn.close()
        return False

def populate_test_cases_table():
    """Populate test_case_metadata table from existing JSON files"""
    test_cases_dir = "/Users/<USER>/Documents/automation-tool/test_cases"
    
    if not os.path.exists(test_cases_dir):
        print(f"Test cases directory not found: {test_cases_dir}")
        return False
    
    db_path = get_db_path()
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    json_files = [f for f in os.listdir(test_cases_dir) if f.endswith('.json')]
    print(f"Found {len(json_files)} test case files to process...")
    
    inserted_count = 0
    updated_count = 0
    error_count = 0
    
    for json_file in json_files:
        try:
            file_path = os.path.join(test_cases_dir, json_file)
            
            with open(file_path, 'r') as f:
                test_case_data = json.load(f)
            
            # Extract data from JSON
            test_case_id = test_case_data.get('test_case_id', json_file.replace('.json', ''))
            name = test_case_data.get('name', json_file.replace('.json', ''))
            description = test_case_data.get('description', '')
            created_date = test_case_data.get('created', datetime.now().isoformat())
            updated_date = test_case_data.get('updated', created_date)
            device_id = test_case_data.get('device_id', '')
            actions = test_case_data.get('actions', [])
            action_count = len(actions)
            labels = json.dumps(test_case_data.get('labels', []))
            
            # Check if record exists
            cursor.execute('SELECT id FROM test_case_metadata WHERE id = ? OR file_path = ?', (test_case_id, file_path))
            existing = cursor.fetchone()

            if existing:
                # Update existing record
                cursor.execute('''
                    UPDATE test_case_metadata
                    SET name = ?, description = ?, updated_date = ?, device_id = ?,
                        action_count = ?, labels = ?
                    WHERE id = ?
                ''', (name, description, updated_date, device_id, action_count, labels, test_case_id))
                updated_count += 1
            else:
                # Insert new record
                cursor.execute('''
                    INSERT INTO test_case_metadata
                    (id, name, description, file_path, created_date, updated_date, device_id, action_count, labels)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (test_case_id, name, description, file_path, created_date, updated_date, device_id, action_count, labels))
                inserted_count += 1
                
        except Exception as e:
            print(f"Error processing {json_file}: {e}")
            error_count += 1
    
    conn.commit()
    conn.close()
    
    print(f"Test cases processed: {inserted_count} inserted, {updated_count} updated, {error_count} errors")
    return True

def populate_test_suites_table():
    """Populate test_suite_metadata table from existing JSON files"""
    test_suites_dir = "/Users/<USER>/Documents/automation-tool/test_suites"
    
    if not os.path.exists(test_suites_dir):
        print(f"Test suites directory not found: {test_suites_dir}")
        return False
    
    db_path = get_db_path()
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    json_files = [f for f in os.listdir(test_suites_dir) if f.endswith('.json')]
    print(f"Found {len(json_files)} test suite files to process...")
    
    inserted_count = 0
    updated_count = 0
    error_count = 0
    
    for json_file in json_files:
        try:
            file_path = os.path.join(test_suites_dir, json_file)
            
            with open(file_path, 'r') as f:
                test_suite_data = json.load(f)
            
            # Extract data from JSON
            suite_id = test_suite_data.get('id', json_file.replace('.json', ''))
            name = test_suite_data.get('name', json_file.replace('.json', ''))
            description = test_suite_data.get('description', '')
            created_date = test_suite_data.get('created', datetime.now().isoformat())
            updated_date = test_suite_data.get('updated', created_date)
            test_cases = test_suite_data.get('test_cases', [])
            test_case_count = len(test_cases)
            test_case_files = json.dumps(test_cases)
            
            # Check if record exists
            cursor.execute('SELECT id FROM test_suite_metadata WHERE id = ? OR file_path = ?', (suite_id, file_path))
            existing = cursor.fetchone()

            if existing:
                # Update existing record
                cursor.execute('''
                    UPDATE test_suite_metadata
                    SET name = ?, description = ?, updated_date = ?, test_case_count = ?, test_case_files = ?
                    WHERE id = ?
                ''', (name, description, updated_date, test_case_count, test_case_files, suite_id))
                updated_count += 1
            else:
                # Insert new record
                cursor.execute('''
                    INSERT INTO test_suite_metadata
                    (id, name, description, file_path, created_date, updated_date, test_case_count, test_case_files)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (suite_id, name, description, file_path, created_date, updated_date, test_case_count, test_case_files))
                inserted_count += 1
                
        except Exception as e:
            print(f"Error processing {json_file}: {e}")
            error_count += 1
    
    conn.commit()
    conn.close()
    
    print(f"Test suites processed: {inserted_count} inserted, {updated_count} updated, {error_count} errors")
    return True

if __name__ == "__main__":
    print("🗄️  Creating Test Cases and Test Suites Metadata Tables")
    print("="*60)
    
    # Step 1: Create tables
    if create_metadata_tables():
        print("✅ Metadata tables created successfully")
        
        # Step 2: Populate test cases
        print("\n📁 Populating test_cases table...")
        if populate_test_cases_table():
            print("✅ Test cases table populated")
        
        # Step 3: Populate test suites
        print("\n📁 Populating test_suites table...")
        if populate_test_suites_table():
            print("✅ Test suites table populated")
        
        print("\n🎉 Database metadata tables setup completed!")
    else:
        print("❌ Failed to create metadata tables")
