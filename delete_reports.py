#!/usr/bin/env python3
import os
import sqlite3
import shutil
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Database file path
DB_PATH = os.path.join('data', 'test_execution.db')

# Reports to delete (from the screenshot)
REPORTS_TO_DELETE = [
    {
        'name': 'Screenshot Test',
        'date': '23/04/2025, 21:52:32'
    },
    {
        'name': 'Another Database Test',
        'date': '23/04/2025, 20:51:38'
    }
]

def delete_reports():
    """Delete the specified reports from the database and file system"""
    try:
        # Connect to the database
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        # Get the reports directory
        reports_dir = os.path.join(os.getcwd(), 'reports')
        logger.info(f"Reports directory: {reports_dir}")
        
        for report in REPORTS_TO_DELETE:
            logger.info(f"Deleting report: {report['name']} ({report['date']})")
            
            # Find the report in the database
            cursor.execute(
                "SELECT suite_id, report_dir FROM test_suites WHERE name = ?",
                (report['name'],)
            )
            
            result = cursor.fetchone()
            if result:
                suite_id, report_dir = result
                logger.info(f"Found report in database: suite_id={suite_id}, report_dir={report_dir}")
                
                # Delete from database
                logger.info(f"Deleting from database: suite_id={suite_id}")
                cursor.execute("DELETE FROM screenshots WHERE suite_id = ?", (suite_id,))
                cursor.execute("DELETE FROM test_steps WHERE suite_id = ?", (suite_id,))
                cursor.execute("DELETE FROM test_cases WHERE suite_id = ?", (suite_id,))
                cursor.execute("DELETE FROM test_suites WHERE suite_id = ?", (suite_id,))
                
                # Delete from file system if report_dir is not empty
                if report_dir:
                    if os.path.exists(report_dir):
                        logger.info(f"Deleting directory: {report_dir}")
                        shutil.rmtree(report_dir)
                    else:
                        logger.warning(f"Report directory not found: {report_dir}")
                    
                    # Check for ZIP file
                    report_id = os.path.basename(report_dir)
                    zip_path = os.path.join(reports_dir, f"{report_id}.zip")
                    if os.path.exists(zip_path):
                        logger.info(f"Deleting ZIP file: {zip_path}")
                        os.remove(zip_path)
            else:
                logger.warning(f"Report not found in database: {report['name']}")
        
        # Commit changes
        conn.commit()
        logger.info("Changes committed to database")
        
        # Close connection
        conn.close()
        logger.info("Database connection closed")
        
        logger.info("Reports deleted successfully")
        return True
    except Exception as e:
        logger.error(f"Error deleting reports: {str(e)}")
        if 'conn' in locals() and conn:
            conn.rollback()
            conn.close()
        return False

if __name__ == "__main__":
    delete_reports()
