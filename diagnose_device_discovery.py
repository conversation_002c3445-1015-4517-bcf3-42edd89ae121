#!/usr/bin/env python3
"""
Device Discovery Diagnostic Script
This script helps diagnose why devices are not being detected in the Mobile App AutoTest tool.
"""

import subprocess
import sys
import os
import json
from pathlib import Path

def check_ios_tools():
    """Check if iOS development tools are available"""
    print("🔍 Checking iOS Development Tools...")
    
    tools = {
        'idevice_id': 'List iOS devices',
        'idevicename': 'Get device names',
        'ideviceinfo': 'Get device information',
        'tidevice': 'Alternative iOS tool'
    }
    
    available_tools = []
    
    for tool, description in tools.items():
        try:
            result = subprocess.run([tool, '--help'], capture_output=True, text=True, timeout=5)
            if result.returncode == 0 or 'usage' in result.stderr.lower() or 'usage' in result.stdout.lower():
                print(f"✅ {tool} - {description} (Available)")
                available_tools.append(tool)
            else:
                print(f"❌ {tool} - {description} (Not working)")
        except FileNotFoundError:
            print(f"❌ {tool} - {description} (Not installed)")
        except subprocess.TimeoutExpired:
            print(f"⚠️  {tool} - {description} (Timeout)")
        except Exception as e:
            print(f"❌ {tool} - {description} (Error: {e})")
    
    return available_tools

def check_android_tools():
    """Check if Android development tools are available"""
    print("\n🔍 Checking Android Development Tools...")
    
    tools = {
        'adb': 'Android Debug Bridge'
    }
    
    available_tools = []
    
    for tool, description in tools.items():
        try:
            result = subprocess.run([tool, 'version'], capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                print(f"✅ {tool} - {description} (Available)")
                print(f"   Version: {result.stdout.strip().split()[4] if len(result.stdout.strip().split()) > 4 else 'Unknown'}")
                available_tools.append(tool)
            else:
                print(f"❌ {tool} - {description} (Not working)")
        except FileNotFoundError:
            print(f"❌ {tool} - {description} (Not installed)")
        except subprocess.TimeoutExpired:
            print(f"⚠️  {tool} - {description} (Timeout)")
        except Exception as e:
            print(f"❌ {tool} - {description} (Error: {e})")
    
    return available_tools

def test_ios_device_discovery():
    """Test iOS device discovery"""
    print("\n📱 Testing iOS Device Discovery...")
    
    try:
        # Test idevice_id
        print("Testing idevice_id -l...")
        result = subprocess.run(['idevice_id', '-l'], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            devices = [line.strip() for line in result.stdout.strip().split('\n') if line.strip()]
            if devices:
                print(f"✅ Found {len(devices)} iOS device(s):")
                for device in devices:
                    print(f"   📱 {device}")
                    
                    # Try to get device name
                    try:
                        name_result = subprocess.run(['idevicename', '-u', device], capture_output=True, text=True, timeout=5)
                        if name_result.returncode == 0:
                            print(f"      Name: {name_result.stdout.strip()}")
                    except Exception as e:
                        print(f"      Name: Error getting name ({e})")
                    
                    # Try to get iOS version
                    try:
                        version_result = subprocess.run(['ideviceinfo', '-u', device, '-k', 'ProductVersion'], capture_output=True, text=True, timeout=5)
                        if version_result.returncode == 0:
                            print(f"      iOS Version: {version_result.stdout.strip()}")
                    except Exception as e:
                        print(f"      iOS Version: Error getting version ({e})")
            else:
                print("❌ No iOS devices found")
        else:
            print(f"❌ idevice_id failed: {result.stderr}")
    except FileNotFoundError:
        print("❌ idevice_id not found - install libimobiledevice")
    except Exception as e:
        print(f"❌ Error testing iOS discovery: {e}")

def test_android_device_discovery():
    """Test Android device discovery"""
    print("\n🤖 Testing Android Device Discovery...")
    
    try:
        # Test adb devices
        print("Testing adb devices -l...")
        result = subprocess.run(['adb', 'devices', '-l'], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')[1:]  # Skip header
            devices = [line for line in lines if line.strip() and 'device' in line]
            
            if devices:
                print(f"✅ Found {len(devices)} Android device(s):")
                for device_line in devices:
                    parts = device_line.split()
                    device_id = parts[0]
                    status = parts[1]
                    print(f"   🤖 {device_id} (Status: {status})")
                    
                    if status == 'device':
                        # Try to get device properties
                        try:
                            model_result = subprocess.run(['adb', '-s', device_id, 'shell', 'getprop', 'ro.product.model'], capture_output=True, text=True, timeout=5)
                            if model_result.returncode == 0:
                                print(f"      Model: {model_result.stdout.strip()}")
                        except Exception as e:
                            print(f"      Model: Error getting model ({e})")
                        
                        try:
                            version_result = subprocess.run(['adb', '-s', device_id, 'shell', 'getprop', 'ro.build.version.release'], capture_output=True, text=True, timeout=5)
                            if version_result.returncode == 0:
                                print(f"      Android Version: {version_result.stdout.strip()}")
                        except Exception as e:
                            print(f"      Android Version: Error getting version ({e})")
            else:
                print("❌ No Android devices found")
        else:
            print(f"❌ adb devices failed: {result.stderr}")
    except FileNotFoundError:
        print("❌ adb not found - install Android SDK Platform Tools")
    except Exception as e:
        print(f"❌ Error testing Android discovery: {e}")

def test_api_endpoint():
    """Test the device API endpoint"""
    print("\n🌐 Testing Device API Endpoint...")
    
    try:
        import requests
        
        # Test iOS app endpoint
        try:
            response = requests.get('http://localhost:8080/api/devices', timeout=10)
            if response.status_code == 200:
                data = response.json()
                devices = data.get('devices', [])
                print(f"✅ iOS API endpoint working - Found {len(devices)} device(s)")
                for device in devices:
                    print(f"   📱 {device.get('name', 'Unknown')} ({device.get('id', 'Unknown ID')})")
            else:
                print(f"❌ iOS API endpoint failed: HTTP {response.status_code}")
        except requests.exceptions.ConnectionError:
            print("❌ iOS app not running on localhost:8080")
        except Exception as e:
            print(f"❌ Error testing iOS API: {e}")
        
        # Test Android app endpoint
        try:
            response = requests.get('http://localhost:8081/api/devices', timeout=10)
            if response.status_code == 200:
                data = response.json()
                devices = data.get('devices', [])
                print(f"✅ Android API endpoint working - Found {len(devices)} device(s)")
                for device in devices:
                    print(f"   🤖 {device.get('name', 'Unknown')} ({device.get('id', 'Unknown ID')})")
            else:
                print(f"❌ Android API endpoint failed: HTTP {response.status_code}")
        except requests.exceptions.ConnectionError:
            print("❌ Android app not running on localhost:8081")
        except Exception as e:
            print(f"❌ Error testing Android API: {e}")
            
    except ImportError:
        print("❌ requests library not available - install with: pip install requests")

def check_permissions():
    """Check device permissions and trust status"""
    print("\n🔐 Checking Device Permissions...")
    
    # Check iOS device trust
    print("iOS Device Trust:")
    try:
        result = subprocess.run(['idevice_id', '-l'], capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            devices = [line.strip() for line in result.stdout.strip().split('\n') if line.strip()]
            if devices:
                for device in devices:
                    try:
                        # Try to get device info - this will fail if device is not trusted
                        info_result = subprocess.run(['ideviceinfo', '-u', device, '-k', 'DeviceName'], capture_output=True, text=True, timeout=5)
                        if info_result.returncode == 0:
                            print(f"   ✅ {device} - Trusted")
                        else:
                            print(f"   ❌ {device} - Not trusted or locked")
                    except Exception:
                        print(f"   ❌ {device} - Cannot verify trust status")
            else:
                print("   ❌ No iOS devices to check")
        else:
            print("   ❌ Cannot check iOS device trust")
    except Exception as e:
        print(f"   ❌ Error checking iOS trust: {e}")
    
    # Check Android USB debugging
    print("\nAndroid USB Debugging:")
    try:
        result = subprocess.run(['adb', 'devices'], capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')[1:]  # Skip header
            devices = [line for line in lines if line.strip()]
            
            if devices:
                for device_line in devices:
                    parts = device_line.split()
                    device_id = parts[0]
                    status = parts[1] if len(parts) > 1 else 'unknown'
                    
                    if status == 'device':
                        print(f"   ✅ {device_id} - USB debugging enabled")
                    elif status == 'unauthorized':
                        print(f"   ❌ {device_id} - USB debugging not authorized")
                    else:
                        print(f"   ⚠️  {device_id} - Status: {status}")
            else:
                print("   ❌ No Android devices to check")
        else:
            print("   ❌ Cannot check Android USB debugging")
    except Exception as e:
        print(f"   ❌ Error checking Android debugging: {e}")

def main():
    """Main diagnostic function"""
    print("🔧 Mobile App AutoTest - Device Discovery Diagnostics")
    print("=" * 60)
    
    # Check tools availability
    ios_tools = check_ios_tools()
    android_tools = check_android_tools()
    
    # Test device discovery
    if ios_tools:
        test_ios_device_discovery()
    
    if android_tools:
        test_android_device_discovery()
    
    # Check permissions
    check_permissions()
    
    # Test API endpoints
    test_api_endpoint()
    
    print("\n" + "=" * 60)
    print("🎯 Diagnostic Summary:")
    print(f"   iOS Tools Available: {len(ios_tools)} / 4")
    print(f"   Android Tools Available: {len(android_tools)} / 1")
    
    print("\n💡 Common Solutions:")
    print("   1. For iOS: Install libimobiledevice: brew install libimobiledevice")
    print("   2. For iOS: Trust the device on your Mac and enter passcode")
    print("   3. For Android: Enable USB debugging in Developer Options")
    print("   4. For Android: Install Android SDK Platform Tools")
    print("   5. Check if devices are properly connected via USB")
    print("   6. Restart the app and refresh the device list")

if __name__ == "__main__":
    main()
