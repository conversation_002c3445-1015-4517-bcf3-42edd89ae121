#!/usr/bin/env python3
"""
iOS Device Detection Diagnostic Script
This script helps diagnose why iOS devices are not being detected.
"""

import subprocess
import sys
import os
import time

def run_command(cmd, timeout=10):
    """Run a command and return the result"""
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=timeout)
        return result.returncode, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        return -1, "", "Command timed out"
    except FileNotFoundError:
        return -1, "", f"Command not found: {cmd[0]}"
    except Exception as e:
        return -1, "", str(e)

def check_ios_tools_detailed():
    """Check iOS tools in detail"""
    print("🔍 Detailed iOS Tools Check...")
    
    tools = {
        'idevice_id': ['idevice_id', '--help'],
        'idevicename': ['idevicename', '--help'],
        'ideviceinfo': ['ideviceinfo', '--help'],
        'tidevice': ['tidevice', '--help'],
        'xcrun': ['xcrun', '--help'],
        'xcode-select': ['xcode-select', '--print-path']
    }
    
    for tool_name, cmd in tools.items():
        returncode, stdout, stderr = run_command(cmd)
        if returncode == 0:
            print(f"✅ {tool_name} - Available")
        else:
            print(f"❌ {tool_name} - Not available")
            if stderr:
                print(f"   Error: {stderr.strip()}")

def check_xcode_installation():
    """Check Xcode installation"""
    print("\n🛠️  Checking Xcode Installation...")
    
    # Check if Xcode is installed
    returncode, stdout, stderr = run_command(['xcode-select', '--print-path'])
    if returncode == 0:
        print(f"✅ Xcode path: {stdout.strip()}")
        
        # Check Xcode version
        returncode, stdout, stderr = run_command(['xcodebuild', '-version'])
        if returncode == 0:
            version_lines = stdout.strip().split('\n')
            print(f"✅ {version_lines[0] if version_lines else 'Xcode installed'}")
        else:
            print("⚠️  Xcode installed but xcodebuild not working")
    else:
        print("❌ Xcode not properly installed")
        print("   Install Xcode from App Store or run: xcode-select --install")

def check_libimobiledevice():
    """Check libimobiledevice installation"""
    print("\n📱 Checking libimobiledevice...")
    
    # Check if installed via Homebrew
    returncode, stdout, stderr = run_command(['brew', 'list', 'libimobiledevice'])
    if returncode == 0:
        print("✅ libimobiledevice installed via Homebrew")
        
        # Get version
        returncode, stdout, stderr = run_command(['brew', 'info', 'libimobiledevice'])
        if returncode == 0:
            lines = stdout.split('\n')
            for line in lines:
                if 'libimobiledevice:' in line:
                    print(f"   Version: {line}")
                    break
    else:
        print("❌ libimobiledevice not installed via Homebrew")
        print("   Install with: brew install libimobiledevice")

def test_ios_device_detection_detailed():
    """Test iOS device detection with detailed output"""
    print("\n📱 Testing iOS Device Detection...")
    
    # Test idevice_id with verbose output
    print("Testing 'idevice_id -l'...")
    returncode, stdout, stderr = run_command(['idevice_id', '-l'])
    
    print(f"Return code: {returncode}")
    print(f"Stdout: '{stdout.strip()}'")
    print(f"Stderr: '{stderr.strip()}'")
    
    if returncode == 0:
        if stdout.strip():
            devices = [line.strip() for line in stdout.strip().split('\n') if line.strip()]
            print(f"✅ Found {len(devices)} iOS device(s):")
            for device in devices:
                print(f"   📱 {device}")
                test_device_details(device)
        else:
            print("❌ No iOS devices found (empty output)")
    else:
        print(f"❌ idevice_id failed with return code {returncode}")
        if stderr:
            print(f"   Error: {stderr}")

def test_device_details(device_id):
    """Test getting details for a specific device"""
    print(f"\n🔍 Testing device details for {device_id}...")
    
    # Test device name
    returncode, stdout, stderr = run_command(['idevicename', '-u', device_id])
    if returncode == 0:
        print(f"   Name: {stdout.strip()}")
    else:
        print(f"   Name: Failed to get name (code: {returncode})")
        if stderr:
            print(f"   Name Error: {stderr.strip()}")
    
    # Test device info
    returncode, stdout, stderr = run_command(['ideviceinfo', '-u', device_id, '-k', 'ProductVersion'])
    if returncode == 0:
        print(f"   iOS Version: {stdout.strip()}")
    else:
        print(f"   iOS Version: Failed to get version (code: {returncode})")
        if stderr:
            print(f"   Version Error: {stderr.strip()}")
    
    # Test device model
    returncode, stdout, stderr = run_command(['ideviceinfo', '-u', device_id, '-k', 'ProductType'])
    if returncode == 0:
        print(f"   Model: {stdout.strip()}")
    else:
        print(f"   Model: Failed to get model (code: {returncode})")

def check_device_trust():
    """Check device trust status"""
    print("\n🔐 Checking Device Trust Status...")
    
    # First get list of devices
    returncode, stdout, stderr = run_command(['idevice_id', '-l'])
    if returncode == 0 and stdout.strip():
        devices = [line.strip() for line in stdout.strip().split('\n') if line.strip()]
        
        for device in devices:
            print(f"\nChecking trust for device: {device}")
            
            # Try to get device name (this requires trust)
            returncode, stdout, stderr = run_command(['idevicename', '-u', device])
            if returncode == 0:
                print(f"   ✅ Device is trusted (got name: {stdout.strip()})")
            else:
                print(f"   ❌ Device may not be trusted or is locked")
                print(f"   Error: {stderr.strip()}")
                
                # Check if it's a trust issue
                if "not trusted" in stderr.lower() or "lockdown" in stderr.lower():
                    print("   💡 Solution: Unlock device and tap 'Trust' when prompted")
                elif "passcode" in stderr.lower():
                    print("   💡 Solution: Unlock device with passcode")
    else:
        print("❌ No devices to check trust status")

def check_usbmuxd():
    """Check usbmuxd service"""
    print("\n🔌 Checking usbmuxd service...")
    
    # Check if usbmuxd is running
    returncode, stdout, stderr = run_command(['ps', 'aux'])
    if returncode == 0:
        if 'usbmuxd' in stdout:
            print("✅ usbmuxd service is running")
        else:
            print("❌ usbmuxd service not found")
            print("   Try: sudo launchctl load -w /System/Library/LaunchDaemons/com.apple.usbmuxd.plist")

def provide_solutions():
    """Provide common solutions"""
    print("\n💡 Common Solutions for iOS Device Detection Issues:")
    print()
    print("1. 📱 Device Setup:")
    print("   - Connect iOS device via USB cable")
    print("   - Unlock the device with passcode/Face ID/Touch ID")
    print("   - When prompted, tap 'Trust This Computer'")
    print("   - Keep device unlocked during initial setup")
    print()
    print("2. 🛠️  Install Required Tools:")
    print("   - Install Xcode: xcode-select --install")
    print("   - Install libimobiledevice: brew install libimobiledevice")
    print("   - Install usbmuxd: brew install usbmuxd")
    print()
    print("3. 🔄 Restart Services:")
    print("   - Restart usbmuxd: sudo pkill usbmuxd")
    print("   - Unplug and replug device")
    print("   - Restart your Mac if issues persist")
    print()
    print("4. 🔍 Verify Detection:")
    print("   - Run: idevice_id -l")
    print("   - Should show your device UDID")
    print("   - Run: idevicename -u <UDID>")
    print("   - Should show device name")

def main():
    """Main diagnostic function"""
    print("🔧 iOS Device Detection Diagnostics")
    print("=" * 50)
    
    check_ios_tools_detailed()
    check_xcode_installation()
    check_libimobiledevice()
    check_usbmuxd()
    test_ios_device_detection_detailed()
    check_device_trust()
    provide_solutions()
    
    print("\n" + "=" * 50)
    print("🎯 Diagnostic Complete!")

if __name__ == "__main__":
    main()
