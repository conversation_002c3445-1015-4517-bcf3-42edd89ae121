#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Direct WDA Clicker - No Appium required
Performs random clicks in targeted screen regions using WDA directly
"""

import os
import sys
import time
import random
import cv2
import numpy as np
from datetime import datetime
import json
import base64
from PIL import Image
import requests

# Create templates directory if it doesn't exist
TEMPLATE_DIR = "templates"
os.makedirs(TEMPLATE_DIR, exist_ok=True)

try:
    import wda
except ImportError:
    print("Error: WDA Python client is not installed")
    print("Please install it with: pip install wda")
    print("Or use: pip install --pre facebook-wda")
    sys.exit(1)

class DirectWDAClicker:
    """Direct WDA client to perform clicks without Appium dependency"""
    
    def __init__(self, wda_url="http://localhost:8100"):
        """
        Initialize DirectWDAClicker
        
        Args:
            wda_url: URL of WebDriverAgent running on device
        """
        self.wda_url = wda_url
        self.client = None
        self.width = 0
        self.height = 0
        self.template_dir = TEMPLATE_DIR
        self.connect()
        
    def connect(self):
        """Connect to iOS device using WDA"""
        print(f"Connecting to WebDriverAgent at {self.wda_url}...")
        
        try:
            # Connect to WDA
            self.client = wda.Client(self.wda_url)
            print("Connected to iOS device!")
            
            # Create new session
            try:
                print("Creating new WDA session...")
                response = requests.post(f"{self.wda_url}/session", json={
                    "capabilities": {
                        "firstMatch": [{}],
                        "alwaysMatch": {}
                    }
                })
                
                if response.status_code == 200 and 'sessionId' in response.json()['value']:
                    self.session_id = response.json()['value']['sessionId']
                    print(f"Session created: {self.session_id}")
                else:
                    print("Failed to create session, using direct endpoint")
                    self.session_id = None
            except Exception as e:
                print(f"Error creating session: {e}")
                self.session_id = None
            
            # Get screen dimensions
            try:
                # Try different methods to get screen size
                try:
                    # Method 1: Use HTTP request to get window size with session
                    if self.session_id:
                        response = requests.get(f"{self.wda_url}/session/{self.session_id}/window/size")
                    else:
                        response = requests.get(f"{self.wda_url}/window/size")
                        
                    if response.status_code == 200:
                        data = response.json()
                        if 'value' in data and 'width' in data['value'] and 'height' in data['value']:
                            self.width = data['value']['width']
                            self.height = data['value']['height']
                        else:
                            # Fallback to common iOS device resolutions
                            self.width = 390  # iPhone 13/14 width
                            self.height = 844 # iPhone 13/14 height
                except Exception:
                    # Fallback to common iOS device resolutions
                    self.width = 390  # iPhone 13/14 width
                    self.height = 844 # iPhone 13/14 height
            except Exception as e:
                print(f"Failed to get screen dimensions: {e}")
                # Fallback to common iOS device resolutions
                self.width = 390  # iPhone 13/14 width
                self.height = 844 # iPhone 13/14 height
            
            print(f"Screen dimensions: {self.width}x{self.height}")
            
        except Exception as e:
            print(f"Error connecting to WebDriverAgent: {e}")
            print("\nMake sure that:")
            print("1. WebDriverAgent is running on your device")
            print("2. Your device is connected and authorized")
            self.client = None
            raise
    
    def take_screenshot(self):
        """Take screenshot using direct HTTP requests to WDA"""
        if not self.client:
            print("Error: Not connected to a device")
            return None
        
        try:
            # Use direct HTTP request to get screenshot
            print("Taking screenshot via HTTP request...")
            response = requests.get(f"{self.wda_url}/screenshot")
            if response.status_code == 200:
                data = response.json()
                if 'value' in data:
                    img_data = base64.b64decode(data['value'])
                    nparr = np.frombuffer(img_data, np.uint8)
                    img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
                    return img
            
            print(f"HTTP request failed with code: {response.status_code}")
            return None
        except Exception as e:
            print(f"Error taking screenshot: {e}")
            return None
    
    def region_click(self, num_attempts=5, region="bottom"):
        """
        Try clicking at random coordinates within specified region
        
        Args:
            num_attempts: Number of random clicks to try
            region: Screen region to target ("all", "top", "middle", "bottom", "left", "right", "center")
            
        Returns:
            bool: True if any click reported success
        """
        if not self.client:
            print("Error: Not connected to a device")
            return False
            
        print(f"\n=== Testing Random Clicks - Screen Size: {self.width}x{self.height} ===")
        print(f"Region: {region}, Attempts: {num_attempts}")
        
        # Take before screenshot
        before_screen = self.take_screenshot()
        if before_screen is None:
            print("Failed to take screenshot")
            return False
            
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        before_path = os.path.join(self.template_dir, f"before_region_clicks_{timestamp}.png")
        cv2.imwrite(before_path, before_screen)
        
        # Define screen regions
        margin = 20
        regions = {
            "all": (margin, self.width - margin, margin, self.height - margin),
            "top": (margin, self.width - margin, margin, self.height // 3),
            "middle": (margin, self.width - margin, self.height // 3, self.height * 2 // 3),
            "bottom": (margin, self.width - margin, self.height * 2 // 3, self.height - margin),
            "left": (margin, self.width // 3, margin, self.height - margin),
            "right": (self.width * 2 // 3, self.width - margin, margin, self.height - margin),
            "center": (self.width // 3, self.width * 2 // 3, self.height // 3, self.height * 2 // 3)
        }
        
        # Get region boundaries
        if region not in regions:
            print(f"Unknown region: {region}. Using full screen.")
            region = "all"
            
        x_min, x_max, y_min, y_max = regions[region]
        print(f"Targeting region: {region} - X({x_min}-{x_max}), Y({y_min}-{y_max})")
        
        # Visualize the target region
        vis_img = before_screen.copy()
        cv2.rectangle(vis_img, (x_min, y_min), (x_max, y_max), (0, 255, 255), 3)
        cv2.putText(vis_img, f"Region: {region}", (x_min + 10, y_min + 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1.0, (0, 255, 255), 2)
        region_vis_path = os.path.join(self.template_dir, f"region_{region}_{timestamp}.png")
        cv2.imwrite(region_vis_path, vis_img)
        
        successful_clicks = []
        
        for i in range(num_attempts):
            # Generate random coordinates within specified region
            x = random.randint(x_min, x_max)
            y = random.randint(y_min, y_max)
            
            print(f"\nAttempt {i+1}/{num_attempts}: Clicking at ({x}, {y})")
            
            # Save click visualization
            vis_img = self.take_screenshot()
            if vis_img is None:
                print("Failed to take screenshot for visualization")
                continue
                
            cv2.circle(vis_img, (x, y), 20, (0, 0, 255), -1)
            cv2.circle(vis_img, (x, y), 22, (255, 255, 255), 2)
            vis_path = os.path.join(self.template_dir, f"click_{region}_{i+1}_{timestamp}.png")
            cv2.imwrite(vis_path, vis_img)
            
            # Perform the tap
            success = False
            try:
                print(f"Tapping at ({x}, {y}) using HTTP request")
                data = {
                    "actions": [
                        {
                            "type": "pointer",
                            "id": "finger1",
                            "parameters": {"pointerType": "touch"},
                            "actions": [
                                {"type": "pointerMove", "duration": 0, "x": x, "y": y},
                                {"type": "pointerDown", "button": 0},
                                {"type": "pause", "duration": 100},
                                {"type": "pointerUp", "button": 0}
                            ]
                        }
                    ]
                }
                
                # Use session endpoint if available
                if self.session_id:
                    endpoint = f"{self.wda_url}/session/{self.session_id}/actions"
                else:
                    endpoint = f"{self.wda_url}/wda/tap/0"
                    data = {"x": x, "y": y}
                
                response = requests.post(endpoint, json=data)
                
                if response.status_code == 200:
                    print("Tap succeeded")
                    success = True
                    successful_clicks.append((x, y))
                else:
                    print(f"Tap failed with code: {response.status_code}")
            except Exception as e:
                print(f"Tap failed: {e}")
                
                # Try alternative tap method
                try:
                    print("Trying alternative tap method...")
                    
                    # Find the main application element
                    find_data = {
                        "using": "class name",
                        "value": "XCUIElementTypeApplication"
                    }
                    
                    if self.session_id:
                        find_endpoint = f"{self.wda_url}/session/{self.session_id}/element"
                    else:
                        find_endpoint = f"{self.wda_url}/element"
                    
                    response = requests.post(find_endpoint, json=find_data)
                    
                    if response.status_code == 200 and 'value' in response.json():
                        element_data = response.json()['value']
                        element_id = None
                        
                        # Extract element ID from response (format varies)
                        if isinstance(element_data, dict):
                            if 'ELEMENT' in element_data:
                                element_id = element_data['ELEMENT']
                            elif 'element-6066-11e4-a52e-4f735466cecf' in element_data:
                                element_id = element_data['element-6066-11e4-a52e-4f735466cecf']
                        
                        if element_id:
                            # Tap on the element at coordinates
                            if self.session_id:
                                tap_endpoint = f"{self.wda_url}/session/{self.session_id}/element/{element_id}/click"
                            else:
                                tap_endpoint = f"{self.wda_url}/wda/element/{element_id}/click"
                                
                            tap_response = requests.post(tap_endpoint)
                            
                            if tap_response.status_code == 200:
                                print("Element tap succeeded")
                                success = True
                                successful_clicks.append((x, y))
                            else:
                                print(f"Element tap failed with code: {tap_response.status_code}")
                        else:
                            print("Failed to extract element ID")
                    else:
                        print("Failed to find application element")
                except Exception as e2:
                    print(f"Alternative tap failed: {e2}")
            
            time.sleep(1.5)  # Wait between clicks
            
            # Take mid-click screenshot to see if UI changed
            mid_screen = self.take_screenshot()
            if mid_screen is not None:
                mid_path = os.path.join(self.template_dir, f"mid_click_{region}_{i+1}_{timestamp}.png")
                cv2.imwrite(mid_path, mid_screen)
                
                # Check if UI changed
                try:
                    before_gray = cv2.cvtColor(before_screen, cv2.COLOR_BGR2GRAY)
                    mid_gray = cv2.cvtColor(mid_screen, cv2.COLOR_BGR2GRAY)
                    
                    diff = cv2.absdiff(before_gray, mid_gray)
                    _, diff_thresh = cv2.threshold(diff, 30, 255, cv2.THRESH_BINARY)
                    diff_pixels = cv2.countNonZero(diff_thresh)
                    
                    print(f"UI difference after click {i+1}: {diff_pixels} pixels changed")
                    
                    # Save diff visualization
                    diff_path = os.path.join(self.template_dir, f"diff_click_{region}_{i+1}_{timestamp}.png")
                    cv2.imwrite(diff_path, diff_thresh)
                    
                    # If significant change, mark as successful
                    if diff_pixels > 1000 and not success:
                        print("UI changed significantly - considering click successful")
                        success = True
                        successful_clicks.append((x, y))
                except Exception as e:
                    print(f"Error comparing screenshots: {e}")
        
        # Take after screenshot
        after_screen = self.take_screenshot()
        if after_screen is not None:
            after_path = os.path.join(self.template_dir, f"after_region_clicks_{timestamp}.png")
            cv2.imwrite(after_path, after_screen)
            
            # Check overall UI change
            try:
                before_gray = cv2.cvtColor(before_screen, cv2.COLOR_BGR2GRAY)
                after_gray = cv2.cvtColor(after_screen, cv2.COLOR_BGR2GRAY)
                
                diff = cv2.absdiff(before_gray, after_gray)
                _, diff_thresh = cv2.threshold(diff, 30, 255, cv2.THRESH_BINARY)
                diff_pixels = cv2.countNonZero(diff_thresh)
                
                print(f"\nOverall UI difference: {diff_pixels} pixels changed")
                
                # Save diff visualization
                diff_path = os.path.join(self.template_dir, f"diff_overall_{region}_{timestamp}.png")
                cv2.imwrite(diff_path, diff_thresh)
            except Exception as e:
                print(f"Error comparing screenshots: {e}")
        
        # Mark successful clicks on final visualization
        if successful_clicks and after_screen is not None:
            final_vis = after_screen.copy()
            for idx, (x, y) in enumerate(successful_clicks):
                cv2.circle(final_vis, (x, y), 25, (0, 255, 0), -1)
                cv2.circle(final_vis, (x, y), 27, (255, 255, 255), 2)
                cv2.putText(final_vis, str(idx+1), (x-7, y+7), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)
            
            success_vis_path = os.path.join(self.template_dir, f"successful_clicks_{region}_{timestamp}.png")
            cv2.imwrite(success_vis_path, final_vis)
            
            print(f"\nSuccessful clicks: {len(successful_clicks)}/{num_attempts}")
            print(f"Coordinates: {successful_clicks}")
            print(f"Visualization saved to: {success_vis_path}")
            
            return len(successful_clicks) > 0
        
        return False
    
    def close(self):
        """Close the connection"""
        self.client = None
        print("Connection closed")

    def capture_template_with_snipping(self, name=None):
        """
        Capture a template using the snipping tool interface
        
        Args:
            name: Optional template name
            
        Returns:
            dict: Template information or None if canceled
        """
        # Take screenshot
        screenshot = self.take_screenshot()
        if screenshot is None:
            print("Failed to take screenshot")
            return None
        
        # Use snipping tool to select region
        snipper = SnippingTool(screenshot, self.width, self.height)
        selection = snipper.get_selection()
        
        if not selection:
            return None
        
        # Ask for template name if not provided
        if not name:
            name = input("Enter template name (e.g., 'signin_button'): ") or "template"
        
        # Extract region from original screenshot using DEVICE coordinates
        x1, y1, x2, y2 = selection["device_rect"]
        
        # Verify coordinates are within device bounds
        x1 = max(0, min(x1, self.width-1))
        y1 = max(0, min(y1, self.height-1))
        x2 = max(0, min(x2, self.width-1))
        y2 = max(0, min(y2, self.height-1))
        
        # Get UI coordinates for cropping the screenshot
        ui_x1, ui_y1, ui_x2, ui_y2 = selection["ui_rect"]
        
        # Ensure width and height are at least 1 pixel
        if ui_x2 <= ui_x1 or ui_y2 <= ui_y1:
            print("Invalid selection: width or height is zero or negative")
            return None
        
        # Crop the template from screenshot using UI coordinates
        try:
            template = screenshot[ui_y1:ui_y2, ui_x1:ui_x2]
            if template.size == 0:
                print("Error: Cropped region is empty")
                return None
        except Exception as e:
            print(f"Error cropping template: {e}")
            return None
        
        # Save the template
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        template_filename = f"{name}_{timestamp}.png"
        template_path = os.path.join(self.template_dir, template_filename)
        
        try:
            cv2.imwrite(template_path, template)
            print(f"Template saved to: {template_path}")
            
            # Save visualization using device coordinates
            vis_img = screenshot.copy()
            
            # Draw UI coordinates in green
            cv2.rectangle(vis_img, (ui_x1, ui_y1), (ui_x2, ui_y2), (0, 255, 0), 2)
            ui_center_x, ui_center_y = selection["ui_center"]
            cv2.circle(vis_img, (ui_center_x, ui_center_y), 5, (0, 255, 0), -1)
            
            # Add text labels to show device coordinates
            device_center_x, device_center_y = selection["device_center"]
            info_text = f"Device center: ({device_center_x}, {device_center_y})"
            cv2.putText(vis_img, info_text, (10, 30), 
                      cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
            
            vis_path = os.path.join(self.template_dir, f"{name}_{timestamp}_vis.png")
            cv2.imwrite(vis_path, vis_img)
            print(f"Visualization saved to: {vis_path}")
            
            # Save metadata
            metadata = {
                "name": name,
                "timestamp": timestamp,
                "ui_rect": selection["ui_rect"],
                "device_rect": selection["device_rect"],
                "ui_center": selection["ui_center"],
                "device_center": selection["device_center"],
                "width": x2 - x1,
                "height": y2 - y1,
                "device_dimensions": {
                    "width": self.width,
                    "height": self.height
                }
            }
            
            metadata_path = os.path.join(self.template_dir, f"{name}_{timestamp}.json")
            with open(metadata_path, 'w') as f:
                json.dump(metadata, f, indent=2)
            
            return {
                "path": template_path,
                "center": selection["device_center"],
                "rect": selection["device_rect"],
                "metadata": metadata
            }
            
        except Exception as e:
            print(f"Error saving template: {e}")
            return None

    def preprocess_images(self, template, screen):
        """Preprocess images for better matching on physical devices"""
        # Convert to grayscale
        if len(template.shape) == 3:
            template_gray = cv2.cvtColor(template, cv2.COLOR_BGR2GRAY)
        else:
            template_gray = template
            
        if len(screen.shape) == 3:
            screen_gray = cv2.cvtColor(screen, cv2.COLOR_BGR2GRAY)
        else:
            screen_gray = screen
        
        # Apply adaptive histogram equalization
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
        template_enhanced = clahe.apply(template_gray)
        screen_enhanced = clahe.apply(screen_gray)
        
        return template_enhanced, screen_enhanced
    
    def find_template(self, template_path, threshold=0.7, debug=True):
        """
        Find template in the current screen
        
        Args:
            template_path: Path to the template image
            threshold: Matching threshold
            debug: Whether to save debug images
            
        Returns:
            dict: Match information or None if not found
        """
        print(f"Finding template: {template_path}")
        
        # Load template
        template = cv2.imread(template_path)
        if template is None:
            print(f"Error: Could not load template {template_path}")
            return None
        
        # Take a screenshot
        screen = self.take_screenshot()
        if screen is None:
            print("Failed to take screenshot for template matching")
            return None
        
        # Preprocess images
        template_proc, screen_proc = self.preprocess_images(template, screen)
        
        # Try different methods and scales
        methods = [
            (cv2.TM_CCOEFF_NORMED, "TM_CCOEFF_NORMED"),
            (cv2.TM_CCORR_NORMED, "TM_CCORR_NORMED")
        ]
        
        scales = [1.0, 0.9, 1.1, 0.8, 1.2]
        
        best_result = None
        best_confidence = 0
        best_method = None
        best_scale = 1.0
        
        for method, method_name in methods:
            for scale in scales:
                # Resize template
                h, w = template_proc.shape[:2]
                scaled_w = int(w * scale)
                scaled_h = int(h * scale)
                
                if scaled_w < 10 or scaled_h < 10:
                    continue
                
                scaled_template = cv2.resize(template_proc, (scaled_w, scaled_h))
                
                # Apply template matching
                try:
                    res = cv2.matchTemplate(screen_proc, scaled_template, method)
                    min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(res)
                    
                    # Use max_val for TM_CCOEFF_NORMED and TM_CCORR_NORMED
                    confidence = max_val
                    loc = max_loc
                    
                    if confidence > best_confidence and confidence >= threshold:
                        best_confidence = confidence
                        best_method = method_name
                        best_scale = scale
                        
                        # Calculate rectangle and center
                        x, y = loc
                        w, h = scaled_w, scaled_h
                        center_x = x + w // 2
                        center_y = y + h // 2
                        
                        best_result = {
                            "confidence": confidence,
                            "method": method_name,
                            "scale": scale,
                            "rect": (x, y, x + w, y + h),
                            "center": (center_x, center_y)
                        }
                
                except Exception as e:
                    print(f"Error matching with {method_name} at scale {scale}: {e}")
        
        if best_result:
            print(f"Template found!")
            print(f"Confidence: {best_result['confidence']:.4f}")
            print(f"Method: {best_result['method']}")
            print(f"Scale: {best_result['scale']:.2f}")
            print(f"Center: {best_result['center']}")
            
            # Create visualization
            if debug:
                vis_img = screen.copy()
                x1, y1, x2, y2 = best_result["rect"]
                
                # Draw rectangle
                cv2.rectangle(vis_img, (x1, y1), (x2, y2), (0, 255, 0), 2)
                
                # Draw center
                cx, cy = best_result["center"]
                cv2.circle(vis_img, (cx, cy), 5, (0, 0, 255), -1)
                
                # Add confidence text
                text = f"{best_result['confidence']:.2f}, {best_result['method']}"
                cv2.putText(vis_img, text, (x1, y1 - 10), 
                            cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
                
                # Save visualization
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                template_name = os.path.basename(template_path).split('.')[0]
                vis_path = os.path.join(self.template_dir, f"match_{template_name}_{timestamp}.png")
                cv2.imwrite(vis_path, vis_img)
                print(f"Match visualization saved to: {vis_path}")
            
            return best_result
        else:
            print(f"Template not found (threshold: {threshold})")
            return None
    
    def template_click(self, template_path, threshold=0.6, offset=(0, 0)):
        """
        Find and click template
        
        Args:
            template_path: Path to the template image
            threshold: Matching threshold
            offset: Click offset from template center (x, y)
            
        Returns:
            bool: Success or failure
        """
        print(f"\n=== Finding and clicking template: {template_path} ===")
        print(f"Threshold: {threshold}")
        
        # Take before screenshot for comparison
        before_screen = self.take_screenshot()
        if before_screen is None:
            print("Failed to take before screenshot")
            return False
            
        # Find the template
        match = self.find_template(template_path, threshold)
        if not match:
            print("Template not found")
            return False
        
        # Get click coordinates (center of template + offset)
        center_x, center_y = match["center"]
        click_x = center_x + offset[0]
        click_y = center_y + offset[1]
        
        print(f"Clicking at ({click_x}, {click_y})")
        
        # Perform the tap using the WDA session
        success = False
        try:
            # Use the W3C Actions API
            data = {
                "actions": [
                    {
                        "type": "pointer",
                        "id": "finger1",
                        "parameters": {"pointerType": "touch"},
                        "actions": [
                            {"type": "pointerMove", "duration": 0, "x": click_x, "y": click_y},
                            {"type": "pointerDown", "button": 0},
                            {"type": "pause", "duration": 100},
                            {"type": "pointerUp", "button": 0}
                        ]
                    }
                ]
            }
            
            # Use session endpoint if available
            if self.session_id:
                endpoint = f"{self.wda_url}/session/{self.session_id}/actions"
            else:
                endpoint = f"{self.wda_url}/wda/tap/0"
                data = {"x": click_x, "y": click_y}
                
            response = requests.post(endpoint, json=data)
            
            if response.status_code == 200:
                print("Template click succeeded!")
                success = True
            else:
                print(f"Template click failed with status code: {response.status_code}")
        except Exception as e:
            print(f"Error performing template click: {e}")
        
        # Wait for UI to update
        time.sleep(1.5)
        
        # Take after screenshot to verify the click had an effect
        after_screen = self.take_screenshot()
        if after_screen is None:
            print("Failed to take after screenshot")
            return success
        
        # Save before/after screenshots
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        template_name = os.path.basename(template_path).split('.')[0]
        
        before_path = os.path.join(self.template_dir, f"before_templ_click_{template_name}_{timestamp}.png")
        after_path = os.path.join(self.template_dir, f"after_templ_click_{template_name}_{timestamp}.png")
        
        cv2.imwrite(before_path, before_screen)
        cv2.imwrite(after_path, after_screen)
        
        # Check if UI changed
        try:
            before_gray = cv2.cvtColor(before_screen, cv2.COLOR_BGR2GRAY)
            after_gray = cv2.cvtColor(after_screen, cv2.COLOR_BGR2GRAY)
            
            diff = cv2.absdiff(before_gray, after_gray)
            _, diff_thresh = cv2.threshold(diff, 30, 255, cv2.THRESH_BINARY)
            diff_pixels = cv2.countNonZero(diff_thresh)
            
            print(f"UI difference: {diff_pixels} pixels changed")
            
            # Save diff visualization
            diff_path = os.path.join(self.template_dir, f"diff_templ_click_{template_name}_{timestamp}.png")
            cv2.imwrite(diff_path, diff_thresh)
            
            # If we see a significant change and click was reported as failed, consider it successful
            if diff_pixels > 1000 and not success:
                print("UI changed significantly - considering click successful")
                success = True
        except Exception as e:
            print(f"Error comparing screenshots: {e}")
        
        return success

class SnippingTool:
    """
    Tool for selecting a region from an image interactively
    Displays the device dimensions for reference
    """
    
    def __init__(self, img, device_width=None, device_height=None):
        self.img = img.copy()
        self.orig_img = img.copy()
        
        # Get image dimensions (as displayed in UI)
        self.ui_height, self.ui_width = self.img.shape[:2]
        
        # Store device dimensions for scaling calculations
        self.device_width = device_width or self.ui_width
        self.device_height = device_height or self.ui_height
        
        # Calculate scaling factors between UI and device
        self.scale_x = self.device_width / self.ui_width
        self.scale_y = self.device_height / self.ui_height
        
        print(f"UI dimensions: {self.ui_width}x{self.ui_height}")
        print(f"Device dimensions: {self.device_width}x{self.device_height}")
        print(f"Scaling factors: x={self.scale_x:.2f}, y={self.scale_y:.2f}")
        
        self.drawing = False
        self.start_x, self.start_y = -1, -1
        self.cur_x, self.cur_y = -1, -1
        self.rect = None
        self.selected = False
        self.canceled = False

    def mouse_callback(self, event, x, y, flags, param):
        """Handle mouse events for the snipping tool"""
        if self.selected or self.canceled:
            return
        
        # Display cursor position (both UI and device coordinates)
        copy_img = self.orig_img.copy()
        device_x = int(x * self.scale_x)
        device_y = int(y * self.scale_y)
        
        # Add device info text
        cv2.putText(copy_img, f"UI: {x},{y} | Device: {device_x},{device_y}", 
                    (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
        cv2.putText(copy_img, f"Device size: {self.device_width}x{self.device_height}", 
                    (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
        
        if event == cv2.EVENT_LBUTTONDOWN:
            self.drawing = True
            self.start_x, self.start_y = x, y
            
        elif event == cv2.EVENT_MOUSEMOVE:
            if self.drawing:
                self.cur_x, self.cur_y = x, y
                cv2.rectangle(copy_img, (self.start_x, self.start_y), (x, y), (0, 255, 0), 2)
                
        elif event == cv2.EVENT_LBUTTONUP:
            self.drawing = False
            self.selected = True
            
            # Sort coordinates to ensure x1 < x2 and y1 < y2
            x1, x2 = min(self.start_x, x), max(self.start_x, x)
            y1, y2 = min(self.start_y, y), max(self.start_y, y)
            
            # Calculate UI and device coordinates
            ui_rect = (x1, y1, x2, y2)
            
            # Convert to device coordinates
            device_x1 = int(x1 * self.scale_x)
            device_y1 = int(y1 * self.scale_y)
            device_x2 = int(x2 * self.scale_x)
            device_y2 = int(y2 * self.scale_y)
            
            device_rect = (device_x1, device_y1, device_x2, device_y2)
            
            # Calculate centers
            ui_center_x = (x1 + x2) // 2
            ui_center_y = (y1 + y2) // 2
            
            device_center_x = (device_x1 + device_x2) // 2
            device_center_y = (device_y1 + device_y2) // 2
            
            self.rect = {
                "ui_rect": ui_rect,
                "device_rect": device_rect,
                "ui_center": (ui_center_x, ui_center_y),
                "device_center": (device_center_x, device_center_y)
            }
            
            # Draw final rectangle and center
            cv2.rectangle(copy_img, (x1, y1), (x2, y2), (0, 255, 0), 2)
            cv2.circle(copy_img, (ui_center_x, ui_center_y), 5, (0, 0, 255), -1)
            
            # Show device coordinates of selection
            info_text = f"Device coords: ({device_x1},{device_y1}) to ({device_x2},{device_y2})"
            cv2.putText(copy_img, info_text, (10, 90), 
                        cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
        
        elif event == cv2.EVENT_RBUTTONDOWN:
            # Cancel on right click
            self.canceled = True
            
        self.img = copy_img

    def get_selection(self):
        """Show the snipping interface and return the selected region"""
        cv2.namedWindow("Select Template", cv2.WINDOW_NORMAL)
        cv2.setMouseCallback("Select Template", self.mouse_callback)
        
        print("\nInstructions:")
        print("- Left-click and drag to select a region")
        print("- Right-click to cancel")
        print("- Press 'Enter' to confirm selection")
        print("- Press 'Esc' to cancel\n")
        print("Current coordinates will be shown in the window")
        
        while True:
            cv2.imshow("Select Template", self.img)
            key = cv2.waitKey(1) & 0xFF
            
            if key == 27:  # ESC
                self.canceled = True
                break
            elif key == 13:  # Enter
                if self.rect:
                    self.selected = True
                    break
            
            if self.selected or self.canceled:
                break
        
        cv2.destroyAllWindows()
        
        if self.canceled or not self.rect:
            return None
            
        return self.rect

def run_cli():
    """Run the CLI interface"""
    print("\n=== Direct WDA Clicker - No Appium Required ===\n")
    
    # Get WDA URL
    wda_url = input("WebDriverAgent URL (default: http://localhost:8100): ") or "http://localhost:8100"
    
    try:
        clicker = DirectWDAClicker(wda_url=wda_url)
        
        # Choose mode
        print("\nAvailable modes:")
        print("1. Template matching and click")
        print("2. Random region clicks")
        mode = input("Select mode (default: 1): ") or "1"
        
        if mode == "1":
            # Template matching mode
            print("\nTemplate Matching Mode:")
            print("We'll use a snipping tool to select a UI element to click")
            print("Then we'll search for it and click it using direct WDA commands")
            
            # Capture or load template
            use_existing = input("Use existing template? (y/n, default: n): ").lower() == 'y'
            
            if use_existing:
                # List available templates
                templates = [f for f in os.listdir(clicker.template_dir) if f.endswith('.png') and not f.endswith('_vis.png')]
                
                if not templates:
                    print("No templates found. Let's capture a new one.")
                    template_info = clicker.capture_template_with_snipping()
                else:
                    print("\nAvailable templates:")
                    for i, template in enumerate(templates):
                        print(f"{i+1}. {template}")
                    
                    choice = input(f"Select template (1-{len(templates)}, default: 1): ") or "1"
                    try:
                        index = int(choice) - 1
                        if 0 <= index < len(templates):
                            template_path = os.path.join(clicker.template_dir, templates[index])
                            template_info = {"path": template_path}
                        else:
                            print("Invalid selection. Let's capture a new template.")
                            template_info = clicker.capture_template_with_snipping()
                    except ValueError:
                        print("Invalid input. Let's capture a new template.")
                        template_info = clicker.capture_template_with_snipping()
            else:
                # Capture new template
                template_info = clicker.capture_template_with_snipping()
            
            if not template_info:
                print("Template capture was canceled. Exiting...")
                clicker.close()
                return
            
            # Try with different thresholds
            print("\nAttempting to find and click the template using WDA...")
            for threshold in [0.7, 0.6, 0.5, 0.4]:
                print(f"\nAttempting with threshold {threshold}...")
                success = clicker.template_click(template_info["path"], threshold=threshold)
                
                if success:
                    print("\nSuccess! Template was found and clicked.")
                    break
                elif threshold == 0.4:  # Last attempt
                    print("\nFailed to find or click the template with all thresholds.")
            
        else:
            # Random click mode (default)
            region = input("Which region to click (all, top, middle, bottom, left, right, center) [bottom]: ") or "bottom"
            
            num_attempts = input("Number of random clicks to try (default: 10): ")
            num_attempts = int(num_attempts) if num_attempts.isdigit() else 10
            
            print(f"\nTrying {num_attempts} random clicks in the {region} region...")
            success = clicker.region_click(num_attempts=num_attempts, region=region)
            
            if success:
                print("\nAt least one random click was successful!")
                print("Check the templates directory for visualizations and screenshots")
            else:
                print("\nAll clicks failed to produce a detectable change.")
        
        print("\nAll template files and visualizations are saved in the 'templates' directory.")
        clicker.close()
        
    except Exception as e:
        print(f"Error: {e}")
        print("\nMake sure that:")
        print("1. WebDriverAgent is running on your device")
        print("2. Your device is connected and authorized")
        print("3. WDA Python client is installed correctly")

if __name__ == "__main__":
    run_cli() 