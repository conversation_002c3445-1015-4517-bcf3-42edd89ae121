# -*- coding: utf-8 -*-
"""
iOS Template Matcher with Appium XCUITest Driver
"""

import os
import sys
import time
import cv2
import numpy as np
from datetime import datetime
import json
import base64
from io import BytesIO
from PIL import Image

# Import Appium Python client
from appium import webdriver
from appium.options.ios import XCUITestOptions
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.common.actions import interaction
from selenium.webdriver.common.actions.action_builder import ActionBuilder
from selenium.webdriver.common.actions.pointer_input import PointerInput
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

class SnippingTool:
    """
    Tool for selecting a region from an image interactively
    Displays the device dimensions for reference
    """
    
    def __init__(self, img, device_width=None, device_height=None):
        self.img = img.copy()
        self.orig_img = img.copy()
        
        # Get image dimensions (as displayed in UI)
        self.ui_height, self.ui_width = self.img.shape[:2]
        
        # Store device dimensions for scaling calculations
        self.device_width = device_width or self.ui_width
        self.device_height = device_height or self.ui_height
        
        # Calculate scaling factors between UI and device
        self.scale_x = self.device_width / self.ui_width
        self.scale_y = self.device_height / self.ui_height
        
        print(f"UI dimensions: {self.ui_width}x{self.ui_height}")
        print(f"Device dimensions: {self.device_width}x{self.device_height}")
        print(f"Scaling factors: x={self.scale_x:.2f}, y={self.scale_y:.2f}")
        
        self.drawing = False
        self.start_x, self.start_y = -1, -1
        self.cur_x, self.cur_y = -1, -1
        self.rect = None
        self.selected = False
        self.canceled = False
    
    def mouse_callback(self, event, x, y, flags, param):
        """Handle mouse events for the snipping tool"""
        if self.selected or self.canceled:
            return
        
        # Display cursor position (both UI and device coordinates)
        copy_img = self.orig_img.copy()
        device_x = int(x * self.scale_x)
        device_y = int(y * self.scale_y)
        
        # Add device info text
        cv2.putText(copy_img, f"UI: {x},{y} | Device: {device_x},{device_y}", 
                    (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
        cv2.putText(copy_img, f"Device size: {self.device_width}x{self.device_height}", 
                    (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
        
        if event == cv2.EVENT_LBUTTONDOWN:
            self.drawing = True
            self.start_x, self.start_y = x, y
        
        elif event == cv2.EVENT_MOUSEMOVE:
            if self.drawing:
                self.cur_x, self.cur_y = x, y
                cv2.rectangle(copy_img, (self.start_x, self.start_y), (x, y), (0, 255, 0), 2)
        
        elif event == cv2.EVENT_LBUTTONUP:
            self.drawing = False
            self.selected = True
            
            # Sort coordinates to ensure x1 < x2 and y1 < y2
            x1, x2 = min(self.start_x, x), max(self.start_x, x)
            y1, y2 = min(self.start_y, y), max(self.start_y, y)
            
            # Calculate UI and device coordinates
            ui_rect = (x1, y1, x2, y2)
            
            # Convert to device coordinates
            device_x1 = int(x1 * self.scale_x)
            device_y1 = int(y1 * self.scale_y)
            device_x2 = int(x2 * self.scale_x)
            device_y2 = int(y2 * self.scale_y)
            
            device_rect = (device_x1, device_y1, device_x2, device_y2)
            
            # Calculate centers
            ui_center_x = (x1 + x2) // 2
            ui_center_y = (y1 + y2) // 2
            
            device_center_x = (device_x1 + device_x2) // 2
            device_center_y = (device_y1 + device_y2) // 2
            
            self.rect = {
                "ui_rect": ui_rect,
                "device_rect": device_rect,
                "ui_center": (ui_center_x, ui_center_y),
                "device_center": (device_center_x, device_center_y)
            }
            
            # Draw final rectangle and center
            cv2.rectangle(copy_img, (x1, y1), (x2, y2), (0, 255, 0), 2)
            cv2.circle(copy_img, (ui_center_x, ui_center_y), 5, (0, 0, 255), -1)
            
            # Show device coordinates of selection
            info_text = f"Device coords: ({device_x1},{device_y1}) to ({device_x2},{device_y2})"
            cv2.putText(copy_img, info_text, (10, 90), 
                        cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
        
        elif event == cv2.EVENT_RBUTTONDOWN:
            # Cancel on right click
            self.canceled = True
            
        self.img = copy_img
    
    def get_selection(self):
        """Show the snipping interface and return the selected region"""
        cv2.namedWindow("Select Template", cv2.WINDOW_NORMAL)
        cv2.setMouseCallback("Select Template", self.mouse_callback)
        
        print("\nInstructions:")
        print("- Left-click and drag to select a region")
        print("- Right-click to cancel")
        print("- Press 'Enter' to confirm selection")
        print("- Press 'Esc' to cancel\n")
        print("Current coordinates will be shown in the window")
        
        while True:
            cv2.imshow("Select Template", self.img)
            key = cv2.waitKey(1) & 0xFF
            
            if key == 27:  # ESC
                self.canceled = True
                break
            elif key == 13:  # Enter
                if self.rect:
                    self.selected = True
                    break
            
            if self.selected or self.canceled:
                break
        
        cv2.destroyAllWindows()
        
        if self.canceled or not self.rect:
        return None
            
        return self.rect

class AppiumIOSTemplateMatcher:
    """Template matcher for iOS devices using Appium XCUITest driver"""
    
    def __init__(self, appium_url="http://localhost:4723", template_dir="templates", bundle_id=None):
        """
        Initialize Appium iOS Template Matcher
        
        Args:
            appium_url: URL of the Appium server
            template_dir: Directory to store templates
            bundle_id: Bundle ID of the app to test (optional)
        """
        self.appium_url = appium_url
        self.template_dir = template_dir
        self.bundle_id = bundle_id
        self.driver = None
        self.last_screenshot = None
        
        # Create template directory
        os.makedirs(template_dir, exist_ok=True)
        
        # Connect to Appium server
        self._connect_to_device()
    
    def _connect_to_device(self):
        """Connect to iOS device using Appium"""
        print(f"Connecting to Appium server at {self.appium_url}...")
        
        # Set up desired capabilities using XCUITestOptions
        options = XCUITestOptions()
        options.device_name = "iPhone"  # This can be any string for a real device
        options.udid = "auto"           # Auto-detect connected device
        options.no_reset = True         # Don't reset the app state between sessions
        options.full_reset = False      # Don't uninstall the app before or after testing
        options.use_new_wda = False     # Don't force a new WDA session
        options.webdriver_agent_url = "http://localhost:8100"  # Use existing WDA
        
        # Add bundle ID if provided
        if self.bundle_id:
            options.bundle_id = self.bundle_id
        
        try:
            self.driver = webdriver.Remote(command_executor=self.appium_url + '/wd/hub', options=options)
            print("Connected to iOS device!")
            
            # Get screen dimensions
            window_size = self.driver.get_window_size()
            self.width = window_size['width']
            self.height = window_size['height']
            print(f"Screen dimensions: {self.width}x{self.height}")
            
        except Exception as e:
            print(f"Error connecting to Appium: {e}")
            print("\nMake sure that:")
            print("1. Appium server is running (appium --base-path=/wd/hub)")
            print("2. WebDriverAgent is running on your device")
            print("3. Your device is connected and authorized")
            self.driver = None
            raise
    
    def take_screenshot(self):
        """Take screenshot using Appium"""
        if not self.driver:
            print("Error: Not connected to a device")
            return None
        
        try:
            # Get screenshot as base64 string
            screenshot_base64 = self.driver.get_screenshot_as_base64()
            
            # Convert to OpenCV image
            img_data = base64.b64decode(screenshot_base64)
            nparr = np.frombuffer(img_data, np.uint8)
            img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            
            self.last_screenshot = img
            return img
            
        except Exception as e:
            print(f"Error taking screenshot: {e}")
            return None
    
    def capture_template_with_snipping(self, name=None):
        """
        Capture a template using the snipping tool interface
        
        Args:
            name: Optional template name
            
        Returns:
            dict: Template information or None if canceled
        """
        # Take screenshot
        screenshot = self.take_screenshot()
        if screenshot is None:
            print("Failed to take screenshot")
            return None
        
        # Use snipping tool to select region
        snipper = SnippingTool(screenshot, self.width, self.height)
        selection = snipper.get_selection()
        
        if not selection:
            return None
        
        # Ask for template name if not provided
        if not name:
            name = input("Enter template name (e.g., 'signin_button'): ") or "template"
        
        # Extract region from original screenshot using DEVICE coordinates
        x1, y1, x2, y2 = selection["device_rect"]
        
        # Verify coordinates are within device bounds
        x1 = max(0, min(x1, self.width-1))
        y1 = max(0, min(y1, self.height-1))
        x2 = max(0, min(x2, self.width-1))
        y2 = max(0, min(y2, self.height-1))
        
        # Get UI coordinates for cropping the screenshot
        ui_x1, ui_y1, ui_x2, ui_y2 = selection["ui_rect"]
        
        # Ensure width and height are at least 1 pixel
        if ui_x2 <= ui_x1 or ui_y2 <= ui_y1:
            print("Invalid selection: width or height is zero or negative")
            return None
        
        # Crop the template from screenshot using UI coordinates
        try:
            template = screenshot[ui_y1:ui_y2, ui_x1:ui_x2]
            if template.size == 0:
                print("Error: Cropped region is empty")
                return None
        except Exception as e:
            print(f"Error cropping template: {e}")
            return None
        
        # Save the template
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        template_filename = f"{name}_{timestamp}.png"
        template_path = os.path.join(self.template_dir, template_filename)
        
        try:
            cv2.imwrite(template_path, template)
            print(f"Template saved to: {template_path}")
            
            # Save visualization using device coordinates
            vis_img = screenshot.copy()
            
            # Draw UI coordinates in green
            ui_x1, ui_y1, ui_x2, ui_y2 = selection["ui_rect"]
            cv2.rectangle(vis_img, (ui_x1, ui_y1), (ui_x2, ui_y2), (0, 255, 0), 2)
            ui_center_x, ui_center_y = selection["ui_center"]
            cv2.circle(vis_img, (ui_center_x, ui_center_y), 5, (0, 255, 0), -1)
            
            # Add text labels to show device coordinates
            device_center_x, device_center_y = selection["device_center"]
            info_text = f"Device center: ({device_center_x}, {device_center_y})"
            cv2.putText(vis_img, info_text, (10, 30), 
                      cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
            
            vis_path = os.path.join(self.template_dir, f"{name}_{timestamp}_vis.png")
            cv2.imwrite(vis_path, vis_img)
            print(f"Visualization saved to: {vis_path}")
            
            # Save metadata
            metadata = {
                "name": name,
                "timestamp": timestamp,
                "ui_rect": selection["ui_rect"],
                "device_rect": selection["device_rect"],
                "ui_center": selection["ui_center"],
                "device_center": selection["device_center"],
                "width": x2 - x1,
                "height": y2 - y1,
                "device_dimensions": {
                    "width": self.width,
                    "height": self.height
                }
            }
            
            metadata_path = os.path.join(self.template_dir, f"{name}_{timestamp}.json")
            with open(metadata_path, 'w') as f:
                json.dump(metadata, f, indent=2)
            
            return {
                "path": template_path,
                "center": selection["device_center"],
                "rect": selection["device_rect"],
                "metadata": metadata
            }
            
        except Exception as e:
            print(f"Error saving template: {e}")
            return None
    
    def preprocess_images(self, template, screen):
        """Preprocess images for better matching on physical devices"""
        # Convert to grayscale
        if len(template.shape) == 3:
            template_gray = cv2.cvtColor(template, cv2.COLOR_BGR2GRAY)
        else:
            template_gray = template
            
        if len(screen.shape) == 3:
            screen_gray = cv2.cvtColor(screen, cv2.COLOR_BGR2GRAY)
        else:
            screen_gray = screen
        
        # Apply adaptive histogram equalization
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
        template_enhanced = clahe.apply(template_gray)
        screen_enhanced = clahe.apply(screen_gray)
        
        return template_enhanced, screen_enhanced
    
    def find_template(self, template_path, threshold=0.7, debug=True):
        """
        Find template in the current screen
        
        Args:
            template_path: Path to the template image
            threshold: Matching threshold
            debug: Whether to save debug images
            
        Returns:
            dict: Match information or None if not found
        """
        print(f"Finding template: {template_path}")
        
        # Load template
        template = cv2.imread(template_path)
        if template is None:
            print(f"Error: Could not load template {template_path}")
            return None
        
        # Take a screenshot
        screen = self.take_screenshot()
        
        # Preprocess images
        template_proc, screen_proc = self.preprocess_images(template, screen)
        
        # Try different methods and scales
        methods = [
            (cv2.TM_CCOEFF_NORMED, "TM_CCOEFF_NORMED"),
            (cv2.TM_CCORR_NORMED, "TM_CCORR_NORMED")
        ]
        
        scales = [1.0, 0.9, 1.1, 0.8, 1.2]
        
        best_result = None
        best_confidence = 0
        best_method = None
        best_scale = 1.0
        
        for method, method_name in methods:
            for scale in scales:
                # Resize template
                h, w = template_proc.shape[:2]
                scaled_w = int(w * scale)
                scaled_h = int(h * scale)
                
                if scaled_w < 10 or scaled_h < 10:
                    continue
                
                scaled_template = cv2.resize(template_proc, (scaled_w, scaled_h))
                
                # Apply template matching
                try:
                    res = cv2.matchTemplate(screen_proc, scaled_template, method)
                    min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(res)
                    
                    # Use max_val for TM_CCOEFF_NORMED and TM_CCORR_NORMED
                    confidence = max_val
                    loc = max_loc
                    
                    if confidence > best_confidence and confidence >= threshold:
                        best_confidence = confidence
                        best_method = method_name
                        best_scale = scale
                        
                        # Calculate rectangle and center
                        x, y = loc
                        w, h = scaled_w, scaled_h
                        center_x = x + w // 2
                        center_y = y + h // 2
                        
                        best_result = {
                            "confidence": confidence,
                            "method": method_name,
                            "scale": scale,
                            "rect": (x, y, x + w, y + h),
                            "center": (center_x, center_y)
                        }
                
                except Exception as e:
                    print(f"Error matching with {method_name} at scale {scale}: {e}")
        
        if best_result:
            print(f"Template found!")
            print(f"Confidence: {best_result['confidence']:.4f}")
            print(f"Method: {best_result['method']}")
            print(f"Scale: {best_result['scale']:.2f}")
            print(f"Center: {best_result['center']}")
            
            # Create visualization
            if debug:
                vis_img = screen.copy()
                x1, y1, x2, y2 = best_result["rect"]
                
                # Draw rectangle
                cv2.rectangle(vis_img, (x1, y1), (x2, y2), (0, 255, 0), 2)
                
                # Draw center
                cx, cy = best_result["center"]
                cv2.circle(vis_img, (cx, cy), 5, (0, 0, 255), -1)
                
                # Add confidence text
                text = f"{best_result['confidence']:.2f}, {best_result['method']}"
                cv2.putText(vis_img, text, (x1, y1 - 10), 
                            cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
                
                # Save visualization
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                template_name = os.path.basename(template_path).split('.')[0]
                vis_path = os.path.join(self.template_dir, f"match_{template_name}_{timestamp}.png")
                cv2.imwrite(vis_path, vis_img)
                print(f"Match visualization saved to: {vis_path}")
            
            return best_result
        else:
            print(f"Template not found (threshold: {threshold})")
            return None
    
    def w3c_tap(self, x, y):
        """
        Tap at coordinates using W3C Actions API
        
        Args:
            x, y: Coordinates to tap
            
        Returns:
            bool: Success or failure
        """
        if not self.driver:
            print("Error: Not connected to a device")
            return False
        
        try:
            print(f"W3C tap at ({x}, {y})")
            
            actions = {
                "actions": [
                    {
                        "type": "pointer",
                        "id": "finger1",
                        "parameters": {"pointerType": "touch"},
                        "actions": [
                            {"type": "pointerMove", "duration": 0, "x": x, "y": y},
                            {"type": "pointerDown", "button": 0},
                            {"type": "pause", "duration": 100},
                            {"type": "pointerUp", "button": 0}
                        ]
                    }
                ]
            }
            
            self.driver.execute("actions", actions)
            print("W3C tap successful")
            return True
            
        except Exception as e:
            print(f"Error performing W3C tap: {e}")
            return False
    
    def direct_wda_tap(self, x, y):
        """
        Tap at coordinates using direct WDA endpoint call
        """
        if not self.driver:
            print("Error: Not connected to a device")
            return False
        
        try:
            print(f"Direct WDA tap at ({x}, {y})")
            
            # Use the direct tap endpoint supported by WDA
            self.driver.execute_script('mobile: tap', {'x': x, 'y': y})
            print("Direct tap successful")
            return True
            
        except Exception as e:
            print(f"Error performing direct tap: {e}")
            return False
    
    def appium_swipe(self, start_x, start_y, end_x, end_y, duration=None):
        """
        Swipe from one point to another using W3C Actions API
        
        Args:
            start_x, start_y: Start coordinates
            end_x, end_y: End coordinates
            duration: Duration of swipe in ms
            
        Returns:
            bool: Success or failure
        """
        if not self.driver:
            print("Error: Not connected to a device")
            return False
        
        try:
            print(f"Swiping from ({start_x}, {start_y}) to ({end_x}, {end_y})")
            
            # Convert duration from ms to seconds
            duration_seconds = duration / 1000 if duration else 0.5
            
            # Create pointer action with touch input
            finger = PointerInput(interaction.POINTER_TOUCH, "finger")
            
            # Create actions sequence
            actions = ActionChains(self.driver)
            actions.w3c_actions = ActionBuilder(self.driver, mouse=finger)
            
            # Build the action sequence
            actions.w3c_actions.pointer_action.move_to_location(start_x, start_y)
            actions.w3c_actions.pointer_action.pointer_down()
            actions.w3c_actions.pointer_action.pause(duration_seconds)
            actions.w3c_actions.pointer_action.move_to_location(end_x, end_y)
            actions.w3c_actions.pointer_action.release()
            
            # Perform the action
            actions.perform()
            print("Swipe performed successfully")
            return True
            
        except Exception as e:
            print(f"Error performing swipe: {e}")
            return False
    
    def fix_click_template(self, template_path, threshold=0.7, offset=(0, 0)):
        """
        Fixed method to find and click on a template using direct Appium methods
        
        Args:
            template_path: Path to template image
            threshold: Matching threshold
            offset: (x, y) offset to apply to click position
            
        Returns:
            bool: Success or failure
        """
        result = self.find_template(template_path, threshold)
        
        if result:
            # Calculate click position
            center_x, center_y = result["center"]
            click_x = center_x + offset[0]
            click_y = center_y + offset[1]
            
            print(f"\nAttempting to click at ({click_x}, {click_y})...")
            
            # Take before screenshot for comparison
            before_screen = self.take_screenshot()
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            template_name = os.path.basename(template_path).split('.')[0]
            before_path = os.path.join(self.template_dir, 
                f"before_click_{template_name}_{timestamp}.png")
            cv2.imwrite(before_path, before_screen)
            
            # METHOD 1: Direct JavaScript tap
            try:
                print("Using 'mobile: tap' JavaScript command...")
                self.driver.execute_script('mobile: tap', {'x': click_x, 'y': click_y})
                print("JavaScript tap executed")
                success = True
            except Exception as e:
                print(f"JavaScript tap failed: {e}")
                
                # METHOD 2: Simple driver tap
                try:
                    print("Using driver tap_coordinates method...")
                    self.driver.tap([(click_x, click_y)])
                    print("Driver tap executed")
                    success = True
                except Exception as e:
                    print(f"Driver tap failed: {e}")
                    
                    # METHOD 3: Simple click
                    try:
                        print("Using standard click method...")
                        # Get the element at that position if possible
                        self.driver.tap([(click_x, click_y)], 500)  # Longer tap duration
                        print("Long tap executed")
                        success = True
                    except Exception as e:
                        print(f"Standard click failed: {e}")
                        success = False
            
            # Wait for UI to update
            time.sleep(1.5)
            
            # Take after screenshot
            after_screen = self.take_screenshot()
            after_path = os.path.join(self.template_dir, 
                f"after_click_{template_name}_{timestamp}.png")
            cv2.imwrite(after_path, after_screen)
            print(f"After-click screenshot saved to: {after_path}")
            
            # Check if UI changed
            if success:
                try:
                    before_gray = cv2.cvtColor(before_screen, cv2.COLOR_BGR2GRAY)
                    after_gray = cv2.cvtColor(after_screen, cv2.COLOR_BGR2GRAY)
                    
                    diff = cv2.absdiff(before_gray, after_gray)
                    _, diff_thresh = cv2.threshold(diff, 30, 255, cv2.THRESH_BINARY)
                    diff_pixels = cv2.countNonZero(diff_thresh)
                    
                    print(f"UI difference: {diff_pixels} pixels changed")
                    
                    # Save diff visualization
                    diff_path = os.path.join(self.template_dir, 
                        f"diff_{template_name}_{timestamp}.png")
                    cv2.imwrite(diff_path, diff_thresh)
                except Exception as e:
                    print(f"Error comparing screenshots: {e}")
            
            return success
        
        return False
    
    def double_click_template(self, template_path, threshold=0.7, offset=(0, 0)):
        """
        Find template and perform a double-click using multiple methods
        
        Args:
            template_path: Path to template image
            threshold: Matching threshold
            offset: (x, y) offset to apply to click position
            
        Returns:
            bool: Success or failure
        """
        result = self.find_template(template_path, threshold)
        
        if result:
            # Calculate click position
            center_x, center_y = result["center"]
            click_x = center_x + offset[0]
            click_y = center_y + offset[1]
            
            print(f"\nAttempting to double-click at ({click_x}, {click_y})...")
            
            # Take before screenshot for comparison
                before_screen = self.take_screenshot()
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                template_name = os.path.basename(template_path).split('.')[0]
                before_path = os.path.join(self.template_dir, 
                f"before_dblclick_{template_name}_{timestamp}.png")
                cv2.imwrite(before_path, before_screen)
                
            success = False
            
            # METHOD 1: Mobile doubleTap command
            try:
                print("Using 'mobile: doubleTap' JavaScript command...")
                self.driver.execute_script('mobile: doubleTap', {'x': click_x, 'y': click_y})
                print("JavaScript double tap executed")
                success = True
            except Exception as e:
                print(f"JavaScript double tap failed: {e}")
                
                # METHOD 2: Two quick taps via JavaScript
                try:
                    print("Using two consecutive taps via JavaScript...")
                    self.driver.execute_script('mobile: tap', {'x': click_x, 'y': click_y})
                    time.sleep(0.1)  # Very brief delay between taps
                    self.driver.execute_script('mobile: tap', {'x': click_x, 'y': click_y})
                    print("Double tap via consecutive taps executed")
                    success = True
                except Exception as e:
                    print(f"Consecutive taps failed: {e}")
                    
                    # METHOD 3: Two taps with driver tap method
                    try:
                        print("Using driver tap method twice...")
                        self.driver.tap([(click_x, click_y)])
                        time.sleep(0.1)
                        self.driver.tap([(click_x, click_y)])
                        print("Double tap with driver.tap executed")
                        success = True
                    except Exception as e:
                        print(f"Driver tap double-click failed: {e}")
                        success = False
                
                # Wait for UI to update
                time.sleep(1.5)
                
                # Take after screenshot
                after_screen = self.take_screenshot()
                after_path = os.path.join(self.template_dir, 
                    f"after_dblclick_{template_name}_{timestamp}.png")
                cv2.imwrite(after_path, after_screen)
                print(f"After-click screenshot saved to: {after_path}")
                
            # Check if UI changed
                if success:
                try:
                    before_gray = cv2.cvtColor(before_screen, cv2.COLOR_BGR2GRAY)
                    after_gray = cv2.cvtColor(after_screen, cv2.COLOR_BGR2GRAY)
                    
                    diff = cv2.absdiff(before_gray, after_gray)
                    _, diff_thresh = cv2.threshold(diff, 30, 255, cv2.THRESH_BINARY)
                    diff_pixels = cv2.countNonZero(diff_thresh)
                    
                    print(f"UI difference: {diff_pixels} pixels changed")
                        
                        # Save diff visualization
                        diff_path = os.path.join(self.template_dir, 
                        f"diff_dblclick_{template_name}_{timestamp}.png")
                        cv2.imwrite(diff_path, diff_thresh)
                except Exception as e:
                    print(f"Error comparing screenshots: {e}")
            
            return success
        
        return False
    
    def airtest_click_template(self, template_path, threshold=0.7, offset=(0, 0), attempt_direct=True):
        """
        Find and click template using Airtest's click method with improved reliability
        
        Args:
            template_path: Path to template image
            threshold: Matching threshold
            offset: (x, y) offset to apply to click position
            attempt_direct: Whether to attempt direct Airtest click if coordinates fail
            
        Returns:
            bool: Success or failure
        """
        # Import Airtest only when needed
        try:
            from airtest.core.api import connect_device, touch, click, G
            import airtest.core.api as airtest_api
        except ImportError:
            print("Error: Airtest is not installed. Install it with 'pip install airtest'")
            return False
        
        result = self.find_template(template_path, threshold)
        
        if result:
            # Calculate click position
            center_x, center_y = result["center"]
            click_x = center_x + offset[0]
            click_y = center_y + offset[1]
            
            print(f"\nAttempting to click at ({click_x}, {click_y}) using Airtest...")
            
            # Take before screenshot for comparison
            before_screen = self.take_screenshot()
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            template_name = os.path.basename(template_path).split('.')[0]
            before_path = os.path.join(self.template_dir, 
                f"before_airtest_click_{template_name}_{timestamp}.png")
            cv2.imwrite(before_path, before_screen)
            
            success = False
            
            try:
                # METHOD 1: Connect to iOS device using Airtest with direct WDA URL
                print("Method 1: Connecting to iOS device with direct WDA URL...")
                wda_url = self.driver.capabilities.get('webDriverAgentUrl', 'http://localhost:8100')
                uri = f"iOS:///{wda_url}"
                
                # Configure Airtest's log level to reduce noise
                import logging
                airtest_logger = logging.getLogger("airtest")
                original_level = airtest_logger.level
                airtest_logger.setLevel(logging.ERROR)
                
                try:
                    # Try to connect to the device
                    ios_dev = connect_device(uri)
                    G.add_device(ios_dev)
                    
                    # Use Airtest's touch function
                    print(f"Touching at coordinates ({click_x}, {click_y}) using Airtest touch...")
                    touch((click_x, click_y))
                    print("Airtest touch completed")
                    success = True
                except Exception as e:
                    print(f"Airtest direct touch failed: {e}")
                    
                    if attempt_direct:
                        # METHOD 2: Try with auto_setup and touch without specifying the device
                        try:
                            print("Method 2: Using auto_setup and direct touch...")
                            from airtest.core.api import auto_setup
                            auto_setup(__file__, devices=[uri])
                            touch((click_x, click_y))
                            print("Airtest auto_setup touch completed")
                            success = True
                        except Exception as e2:
                            print(f"Airtest auto_setup touch failed: {e2}")
                            
                            # METHOD 3: Try with Airtest's click function on a template image
                            try:
                                print("Method 3: Using Airtest click on template image...")
                                # Create a new template from our cropped region
                                from airtest.core.cv import Template
                                
                                # Save result image to a temporary file
                                x1, y1, x2, y2 = result["rect"]
                                temp_template = self.take_screenshot()[y1:y2, x1:x2]
                                temp_file = os.path.join(self.template_dir, f"temp_{template_name}_{timestamp}.png")
                                cv2.imwrite(temp_file, temp_template)
                                
                                # Create Template object with appropriate threshold
                                tpl = Template(temp_file, threshold=threshold)
                                
                                # Use click function (which handles finding and clicking)
                                click(tpl)
                                print("Airtest template click completed")
                                success = True
                            except Exception as e3:
                                print(f"Airtest template click failed: {e3}")
                finally:
                    # Restore original logging level
                    airtest_logger.setLevel(original_level)
                    
                    # Wait for UI to update
                    time.sleep(1.5)
                    
                    # Take after screenshot
                    after_screen = self.take_screenshot()
                    after_path = os.path.join(self.template_dir, 
                        f"after_airtest_click_{template_name}_{timestamp}.png")
                    cv2.imwrite(after_path, after_screen)
                    print(f"After-click screenshot saved to: {after_path}")
                    
                    # Check if UI changed
                    try:
                        before_gray = cv2.cvtColor(before_screen, cv2.COLOR_BGR2GRAY)
                        after_gray = cv2.cvtColor(after_screen, cv2.COLOR_BGR2GRAY)
                        
                        diff = cv2.absdiff(before_gray, after_gray)
                        _, diff_thresh = cv2.threshold(diff, 30, 255, cv2.THRESH_BINARY)
                        diff_pixels = cv2.countNonZero(diff_thresh)
                        
                        print(f"UI difference: {diff_pixels} pixels changed")
                        
                        # Save diff visualization
                        diff_path = os.path.join(self.template_dir, 
                            f"diff_airtest_{template_name}_{timestamp}.png")
                        cv2.imwrite(diff_path, diff_thresh)
                        
                        # If we see a significant change in the UI, consider it successful
                        if diff_pixels > 1000 and not success:
                            print("UI changed significantly - considering click successful")
                            success = True
                    except Exception as e:
                        print(f"Error comparing screenshots: {e}")
            
            except Exception as e:
                print(f"Overall Airtest click process failed: {e}")
                success = False
            
            return success
        
        return False
    
    def direct_wda_click_template(self, template_path, threshold=0.7, offset=(0, 0)):
        """
        Find and click template using direct WDA tap for maximum reliability
        
        Args:
            template_path: Path to template image
            threshold: Matching threshold
            offset: (x, y) offset to apply to click position
            
        Returns:
            bool: Success or failure
        """
        result = self.find_template(template_path, threshold)
        
        if result:
            # Calculate click position
            center_x, center_y = result["center"]
            click_x = center_x + offset[0]
            click_y = center_y + offset[1]
            
            print(f"\nAttempting direct WDA tap at ({click_x}, {click_y})...")
            
            # Take before screenshot for comparison
            before_screen = self.take_screenshot()
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            template_name = os.path.basename(template_path).split('.')[0]
            before_path = os.path.join(self.template_dir, 
                f"before_wda_click_{template_name}_{timestamp}.png")
            cv2.imwrite(before_path, before_screen)
            
            success = False
            
            try:
                # Get direct access to WDA client through the Appium driver
                import wda
                
                # Method 1: Use direct WDA client from driver
                try:
                    print("Using direct WDA client from Appium session...")
                    wda_url = self.driver.capabilities.get('webDriverAgentUrl', 'http://localhost:8100')
                    print(f"WDA URL: {wda_url}")
                    
                    # Create a new WDA client connection
                    wda_client = wda.Client(wda_url)
                    
                    # Use the tap method with a very short duration
                    print(f"Performing direct WDA tap at ({click_x}, {click_y})...")
                    wda_client.tap(click_x, click_y)
                    print("Direct WDA tap completed")
                    success = True
                except Exception as e:
                    print(f"Direct WDA tap failed: {e}")
                    
                    # Method 2: Alternative tap through WebDriverAgent.js
                    try:
                        print("Trying alternative tap with WebDriverAgent.js...")
                        # Use Appium's execute_script method to directly call WebDriverAgent
                        self.driver.execute_script('mobile: tap', {'x': click_x, 'y': click_y, 'duration': 0.05})
                        print("WebDriverAgent JS tap completed")
                        success = True
                    except Exception as e:
                        print(f"WebDriverAgent JS tap failed: {e}")
                
                # Wait for UI to update
                time.sleep(1.5)
                
                # Take after screenshot
                after_screen = self.take_screenshot()
                after_path = os.path.join(self.template_dir, 
                    f"after_wda_click_{template_name}_{timestamp}.png")
                cv2.imwrite(after_path, after_screen)
                print(f"After-click screenshot saved to: {after_path}")
                
                # Check if UI changed
                try:
                    before_gray = cv2.cvtColor(before_screen, cv2.COLOR_BGR2GRAY)
                    after_gray = cv2.cvtColor(after_screen, cv2.COLOR_BGR2GRAY)
                    
                    diff = cv2.absdiff(before_gray, after_gray)
                    _, diff_thresh = cv2.threshold(diff, 30, 255, cv2.THRESH_BINARY)
                    diff_pixels = cv2.countNonZero(diff_thresh)
                    
                    print(f"UI difference: {diff_pixels} pixels changed")
                    
                    # Save diff visualization
                    diff_path = os.path.join(self.template_dir, 
                        f"diff_wda_{template_name}_{timestamp}.png")
                    cv2.imwrite(diff_path, diff_thresh)
                    
                    # If we see a significant change in the UI, consider it successful
                    if diff_pixels > 1000 and not success:
                        print("UI changed significantly - considering click successful")
                        success = True
                except Exception as e:
                    print(f"Error comparing screenshots: {e}")
                
            except Exception as e:
                print(f"Overall WDA click process failed: {e}")
                success = False
            
            return success
        
        return False
    
    def random_click(self, num_attempts=5, region="all"):
        """
        Try clicking at random coordinates within screen boundaries or specified region
        
        Args:
            num_attempts: Number of random clicks to try
            region: Screen region to target ("all", "top", "middle", "bottom", "left", "right", "center")
            
        Returns:
            bool: True if any click reported success
        """
        if not self.driver:
            print("Error: Not connected to a device")
            return False
            
        import random
        
        print(f"\n=== Testing Random Clicks - Screen Size: {self.width}x{self.height} ===")
        print(f"Region: {region}, Attempts: {num_attempts}")
        
        # Take before screenshot
        before_screen = self.take_screenshot()
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        before_path = os.path.join(self.template_dir, f"before_random_clicks_{timestamp}.png")
        cv2.imwrite(before_path, before_screen)
        
        # Define screen regions
        margin = 20
        regions = {
            "all": (margin, self.width - margin, margin, self.height - margin),
            "top": (margin, self.width - margin, margin, self.height // 3),
            "middle": (margin, self.width - margin, self.height // 3, self.height * 2 // 3),
            "bottom": (margin, self.width - margin, self.height * 2 // 3, self.height - margin),
            "left": (margin, self.width // 3, margin, self.height - margin),
            "right": (self.width * 2 // 3, self.width - margin, margin, self.height - margin),
            "center": (self.width // 3, self.width * 2 // 3, self.height // 3, self.height * 2 // 3)
        }
        
        # Get region boundaries
        if region not in regions:
            print(f"Unknown region: {region}. Using full screen.")
            region = "all"
            
        x_min, x_max, y_min, y_max = regions[region]
        print(f"Targeting region: {region} - X({x_min}-{x_max}), Y({y_min}-{y_max})")
        
        # Visualize the target region
        vis_img = before_screen.copy()
        cv2.rectangle(vis_img, (x_min, y_min), (x_max, y_max), (0, 255, 255), 3)
        cv2.putText(vis_img, f"Region: {region}", (x_min + 10, y_min + 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1.0, (0, 255, 255), 2)
        region_vis_path = os.path.join(self.template_dir, f"region_{region}_{timestamp}.png")
        cv2.imwrite(region_vis_path, vis_img)
        
        any_success = False
        
        for i in range(num_attempts):
            # Generate random coordinates within specified region
            x = random.randint(x_min, x_max)
            y = random.randint(y_min, y_max)
            
            print(f"\nAttempt {i+1}/{num_attempts}: Clicking at ({x}, {y})")
            
            # Save click visualization
            vis_img = self.take_screenshot()
            cv2.circle(vis_img, (x, y), 20, (0, 0, 255), -1)
            cv2.circle(vis_img, (x, y), 22, (255, 255, 255), 2)
            vis_path = os.path.join(self.template_dir, f"random_click_{region}_{i+1}_{timestamp}.png")
            cv2.imwrite(vis_path, vis_img)
            
            # METHOD 1: Direct WDA JS tap
            success = False
            try:
                print(f"Method 1: Using 'mobile: tap' at ({x}, {y})")
                self.driver.execute_script('mobile: tap', {'x': x, 'y': y})
                print("JavaScript tap succeeded")
                success = True
            except Exception as e:
                print(f"JavaScript tap failed: {e}")
                
                # METHOD 2: WDA Python client
                try:
                    import wda
                    print(f"Method 2: Using WDA Python client at ({x}, {y})")
                    wda_url = self.driver.capabilities.get('webDriverAgentUrl', 'http://localhost:8100')
                    client = wda.Client(wda_url)
                    client.tap(x, y)
                    print("WDA client tap succeeded")
                    success = True
                except Exception as e:
                    print(f"WDA client tap failed: {e}")
                    
                    # METHOD 3: Appium driver tap
                    try:
                        print(f"Method 3: Using driver.tap at ({x}, {y})")
                        self.driver.tap([(x, y)])
                        print("Driver tap succeeded")
                        success = True
                    except Exception as e:
                        print(f"Driver tap failed: {e}")
            
            time.sleep(1.5)  # Wait between clicks
            
            if success:
                any_success = True
                print(f"Click at ({x}, {y}) successful!")
            else:
                print(f"All click methods failed at ({x}, {y})")
        
        # Take after screenshot
        after_screen = self.take_screenshot()
        after_path = os.path.join(self.template_dir, f"after_random_clicks_{region}_{timestamp}.png")
        cv2.imwrite(after_path, after_screen)
        
        # Check if UI changed
        try:
            before_gray = cv2.cvtColor(before_screen, cv2.COLOR_BGR2GRAY)
            after_gray = cv2.cvtColor(after_screen, cv2.COLOR_BGR2GRAY)
            
            diff = cv2.absdiff(before_gray, after_gray)
            _, diff_thresh = cv2.threshold(diff, 30, 255, cv2.THRESH_BINARY)
            diff_pixels = cv2.countNonZero(diff_thresh)
            
            print(f"\nOverall UI difference: {diff_pixels} pixels changed")
            
            # Save diff visualization
            diff_path = os.path.join(self.template_dir, f"diff_random_clicks_{region}_{timestamp}.png")
            cv2.imwrite(diff_path, diff_thresh)
            
            # If we see a significant change in the UI, consider it successful
            if diff_pixels > 1000 and not any_success:
                print("UI changed significantly - at least one click was effective")
                any_success = True
        except Exception as e:
            print(f"Error comparing screenshots: {e}")
        
        return any_success
    
    def appium_image_plugin_click(self, template_path, threshold=0.4, timeout=10):
        """
        Find and click UI element using Appium's native image matching plugin.
        
        Args:
            template_path: Path to the template image
            threshold: Matching threshold (0-1)
            timeout: Timeout in seconds
            
        Returns:
            bool: Success or failure
        """
        if not self.driver:
            print("Error: Not connected to a device")
            return False
        
        print(f"\n=== Using Appium Image Plugin to find and click: {template_path} ===")
        print(f"Threshold: {threshold}, Timeout: {timeout}s")
        
        # Take before screenshot for comparison
        before_screen = self.take_screenshot()
        
        try:
            # Convert template to base64
            with open(template_path, "rb") as template_file:
                template_base64 = base64.b64encode(template_file.read()).decode('utf-8')
            
            print("Template converted to base64")
            
            # Find element using image
            print("Searching for element using findByImage...")
            start_time = time.time()
            
            # Set desired capabilities for image matching
            self.driver.update_settings({
                "imageMatchThreshold": threshold,
                "fixImageTemplateSize": True,
                "fixImageTemplateScale": True,
                "autoAcceptAlerts": True
            })
            
            # Find element by image using Appium 5.0 syntax
            try:
                from appium.webdriver.common.appiumby import AppiumBy
                
                print("Using AppiumBy.IMAGE locator...")
                element = self.driver.find_element(by=AppiumBy.IMAGE, value=template_base64)
                
                # Click on the element
                print("Element found! Clicking...")
                element.click()
                
                # Calculate time taken
                elapsed = time.time() - start_time
                print(f"Element clicked successfully in {elapsed:.2f} seconds")
                
                # Take after screenshot for comparison
                time.sleep(1.5)
                after_screen = self.take_screenshot()
                
                # Save images for comparison
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                template_name = os.path.basename(template_path).split('.')[0]
                
                before_path = os.path.join(self.template_dir, f"before_imgplugin_{template_name}_{timestamp}.png")
                after_path = os.path.join(self.template_dir, f"after_imgplugin_{template_name}_{timestamp}.png")
                
                cv2.imwrite(before_path, before_screen)
                cv2.imwrite(after_path, after_screen)
                
                # Check if UI changed
                try:
                    before_gray = cv2.cvtColor(before_screen, cv2.COLOR_BGR2GRAY)
                    after_gray = cv2.cvtColor(after_screen, cv2.COLOR_BGR2GRAY)
                    
                    diff = cv2.absdiff(before_gray, after_gray)
                    _, diff_thresh = cv2.threshold(diff, 30, 255, cv2.THRESH_BINARY)
                    diff_pixels = cv2.countNonZero(diff_thresh)
                    
                    print(f"UI difference: {diff_pixels} pixels changed")
                    
                    # Save diff visualization
                    diff_path = os.path.join(self.template_dir, f"diff_imgplugin_{template_name}_{timestamp}.png")
                    cv2.imwrite(diff_path, diff_thresh)
                except Exception as e:
                    print(f"Error comparing screenshots: {e}")
                
                return True
                
            except Exception as e:
                print(f"Error finding element: {e}")
                return False
                
        except Exception as e:
            print(f"Error with Appium Image Plugin: {e}")
        return False
    
    def close(self):
        """Close the Appium session"""
        if self.driver:
            self.driver.quit()
            print("Appium session closed")

def run_interactive_demo():
    """Run the interactive template matching demo with Appium and direct WDA"""
    print("\n=== iOS Template Matching with Direct WDA Clicks ===\n")
    
    # Ask for Appium server URL
    appium_url = input("Appium server URL (default: http://localhost:4723): ") or "http://localhost:4723"
    
    # Ask for bundle ID (optional)
    bundle_id = input("App Bundle ID (optional, e.g. com.example.app): ") or None
    
    try:
        matcher = AppiumIOSTemplateMatcher(appium_url=appium_url, bundle_id=bundle_id)
        
        # Ask user what they want to do
        action = input("\nChoose an action (1 = Template Match & Click, 2 = Random Clicks, 3 = Appium Image Plugin): ")
        
        if action == "3":
            # Image Plugin mode
            print("\nWe'll use Appium Image Plugin to find and click a UI element")
            print("This requires the Appium Image Plugin installed (appium-image-plugin)")
        input("\nPress Enter to continue...")
        
        # Capture template using snipping tool
        template_info = matcher.capture_template_with_snipping()
        
        if not template_info:
            print("Template capture was canceled. Exiting...")
            matcher.close()
            return
        
            # Try with different thresholds
            print("\nAttempting to click using Appium Image Plugin...")
            for threshold in [0.4, 0.3, 0.2]:
                print(f"\nAttempting with threshold {threshold}...")
                success = matcher.appium_image_plugin_click(template_info["path"], threshold=threshold)
                
                if success:
                    print("\nSuccess! Element was found and clicked using Appium Image Plugin.")
                    break
                elif threshold == 0.2:  # Last attempt
                    print("\nFailed to find or click the element with all thresholds.")
        
        elif action == "2":
            # Random click mode
            num_clicks = input("Number of random clicks to try (default: 5): ")
            num_clicks = int(num_clicks) if num_clicks.isdigit() else 5
            
            region = input("Which region to click (all, top, middle, bottom, left, right, center): ") or "all"
            
            print(f"\nTrying {num_clicks} random clicks in the {region} region...")
            success = matcher.random_click(num_clicks, region=region)
            
            if success:
                print("\nAt least one random click was successful!")
            else:
                print("\nAll random clicks failed to produce a detectable change.")
                
        else:
            # Template match mode (default)
            print("\nWe'll use a snipping tool to select a UI element (like a button)")
            print("Then we'll try to find and click it using direct WDA calls")
            input("\nPress Enter to continue...")
            
            # Capture template using snipping tool
            template_info = matcher.capture_template_with_snipping()
            
            if not template_info:
                print("Template capture was canceled. Exiting...")
                matcher.close()
                return
            
            # Try with different thresholds
            print("\nAttempting to click the template using direct WDA...")
            for threshold in [0.7, 0.65, 0.6]:
                print(f"\nAttempting with threshold {threshold}...")
                success = matcher.direct_wda_click_template(template_info["path"], threshold=threshold)
                
                if success:
                    print("\nSuccess! Template was found and clicked using direct WDA.")
                    break
                elif threshold == 0.6:  # Last attempt
                    print("\nFailed to find or click the template with all thresholds.")
        
        print("\nAll template files and visualizations are saved in the 'templates' directory.")
        matcher.close()
        
    except Exception as e:
        print(f"Error in interactive demo: {e}")
        print("\nMake sure that:")
        print("1. Appium server is running (appium --base-path=/wd/hub)")
        print("2. iOS device is connected and authorized")
        print("3. WebDriverAgent is installed on the device")
        print("4. WDA Python client is installed ('pip install wda')")
        print("5. To use Image Plugin, install it with: appium driver install image")

if __name__ == "__main__":
    run_interactive_demo()