#!/usr/bin/env python3
import sys
import json
import requests

def get_session_capabilities(session_id):
    """Get the capabilities of a session from the Appium server."""
    try:
        # Try with /wd/hub base path first
        url = f"http://localhost:4723/wd/hub/session/{session_id}"
        response = requests.get(url)
        
        if response.status_code == 200:
            data = response.json()
            if 'value' in data and 'capabilities' in data['value']:
                return data['value']['capabilities']
        
        # Try without base path
        url = f"http://localhost:4723/session/{session_id}"
        response = requests.get(url)
        
        if response.status_code == 200:
            data = response.json()
            if 'value' in data and 'capabilities' in data['value']:
                return data['value']['capabilities']
            
        print(f"Error: Could not get capabilities for session {session_id}")
        print(f"Response: {response.text}")
        return None
    except Exception as e:
        print(f"Error: {e}")
        return None

def main():
    if len(sys.argv) < 2:
        print("Usage: python get_session_capabilities.py SESSION_ID")
        sys.exit(1)
    
    session_id = sys.argv[1]
    capabilities = get_session_capabilities(session_id)
    
    if capabilities:
        print(json.dumps(capabilities, indent=2))
        
        # Save capabilities to a file
        with open('session_capabilities.json', 'w') as f:
            json.dump(capabilities, f, indent=2)
        print(f"Capabilities saved to session_capabilities.json")
        
        # Create a simplified version for the Inspector
        inspector_caps = {
            "platformName": capabilities.get("platformName"),
            "deviceName": capabilities.get("deviceName") or "iPhone",
            "udid": capabilities.get("udid"),
            "automationName": capabilities.get("automationName") or "XCUITest" if capabilities.get("platformName") == "iOS" else "UiAutomator2"
        }
        
        # Add additional capabilities based on platform
        if capabilities.get("platformName") == "iOS":
            inspector_caps["xcodeOrgId"] = capabilities.get("xcodeOrgId", "")
            inspector_caps["xcodeSigningId"] = capabilities.get("xcodeSigningId", "iPhone Developer")
            inspector_caps["bundleId"] = capabilities.get("bundleId", "")
        elif capabilities.get("platformName") == "Android":
            inspector_caps["appPackage"] = capabilities.get("appPackage", "")
            inspector_caps["appActivity"] = capabilities.get("appActivity", "")
        
        # Remove any None values
        inspector_caps = {k: v for k, v in inspector_caps.items() if v is not None}
        
        with open('inspector_capabilities.json', 'w') as f:
            json.dump(inspector_caps, f, indent=2)
        print(f"Simplified capabilities for Inspector saved to inspector_capabilities.json")
    
if __name__ == "__main__":
    main()
