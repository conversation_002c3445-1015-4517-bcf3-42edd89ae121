from PIL import Image, ImageChops, ImageDraw, ImageFont
import numpy as np
import cv2
import os

def compare_images(image1_path, image2_path, threshold=0.01, min_area_percent=0.002, diff_threshold=20):
    """
    Compare two images and highlight only completely different objects
    
    Args:
        image1_path: Path to first image
        image2_path: Path to second image
        threshold: Percentage threshold for considering images different (default: 1%)
        min_area_percent: Minimum area percentage for an object to be considered significant
        diff_threshold: Minimum difference percentage within a region to be considered a different object
    
    Returns:
        dict: Contains similarity score, comparison result and diff image path
    """
    try:
        # Open images using PIL
        base = Image.open(image1_path).convert('RGB')
        other = Image.open(image2_path).convert('RGB')

        # Resize images to match dimensions
        width = min(base.width, other.width)
        height = min(base.height, other.height)
        base = base.resize((width, height))
        other = other.resize((width, height))

        # Compute the difference
        diff = ImageChops.difference(base, other).convert('L')
        
        # Calculate difference statistics
        diff_array = np.array(diff)
        diff_pixels = np.count_nonzero(diff_array > 30)
        total_pixels = diff_array.size
        diff_percentage = (diff_pixels / total_pixels) * 100
        similarity = 100 - diff_percentage
        
        # Determine if images are similar based on threshold
        is_similar = diff_percentage <= threshold

        # Convert PIL images to OpenCV format for advanced processing
        base_cv = cv2.cvtColor(np.array(base), cv2.COLOR_RGB2BGR)
        other_cv = cv2.cvtColor(np.array(other), cv2.COLOR_RGB2BGR)
        
        # Convert to grayscale for better comparison
        base_gray = cv2.cvtColor(base_cv, cv2.COLOR_BGR2GRAY)
        other_gray = cv2.cvtColor(other_cv, cv2.COLOR_BGR2GRAY)
        
        # Also analyze color differences (helps with detecting different colored objects)
        base_hsv = cv2.cvtColor(base_cv, cv2.COLOR_BGR2HSV)
        other_hsv = cv2.cvtColor(other_cv, cv2.COLOR_BGR2HSV)
        
        # Calculate color difference in HSV space (more perceptually relevant)
        h_diff = cv2.absdiff(base_hsv[:,:,0], other_hsv[:,:,0])
        s_diff = cv2.absdiff(base_hsv[:,:,1], other_hsv[:,:,1])
        v_diff = cv2.absdiff(base_hsv[:,:,2], other_hsv[:,:,2])
        
        # Combine color differences with higher weight on hue (color) differences
        color_diff = cv2.addWeighted(h_diff, 2.0, s_diff, 1.0, 0)
        color_diff = cv2.addWeighted(color_diff, 1.0, v_diff, 0.5, 0)
        
        # Calculate absolute difference between images (structural)
        diff_cv = cv2.absdiff(base_gray, other_gray)
        
        # Combine structural and color differences
        combined_diff = cv2.addWeighted(diff_cv, 0.7, color_diff, 0.3, 0)
        
        # Apply Gaussian blur to reduce noise
        blurred = cv2.GaussianBlur(combined_diff, (5, 5), 0)
        
        # Apply adaptive thresholding to better identify significant differences
        # This works better for detecting object-level changes rather than pixel-level noise
        thresh = cv2.adaptiveThreshold(blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                                      cv2.THRESH_BINARY, 21, 5)
        
        # Apply morphological operations to connect nearby differences
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (11, 11))
        dilated = cv2.dilate(thresh, kernel, iterations=2)
        eroded = cv2.erode(dilated, kernel, iterations=1)
        
        # Additional closing operation to better identify object boundaries
        closed = cv2.morphologyEx(eroded, cv2.MORPH_CLOSE, kernel)
        
        # Find contours of significant differences
        contours, _ = cv2.findContours(closed, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        # Calculate minimum area threshold based on image size
        min_area = total_pixels * min_area_percent
        
        # Draw bounding boxes only around significant object differences
        result = other.copy()
        draw = ImageDraw.Draw(result)
        significant_changes = 0
        
        for cnt in contours:
            area = cv2.contourArea(cnt)
            if area > min_area:  # Only mark significant object differences
                x, y, w, h = cv2.boundingRect(cnt)
                
                # Calculate how different this region is
                roi_base = base_gray[y:y+h, x:x+w]
                roi_other = other_gray[y:y+h, x:x+w]
                
                # Only proceed if ROIs have valid dimensions
                if roi_base.size > 0 and roi_other.size > 0 and roi_base.shape == roi_other.shape:
                    # Calculate local difference percentage
                    local_diff = cv2.absdiff(roi_base, roi_other)
                    local_diff_percent = np.count_nonzero(local_diff > 30) / local_diff.size * 100
                    
                    # Only highlight regions with significant differences
                    if local_diff_percent > diff_threshold:  # Region is completely different
                        # Use a more visible red color with thicker outline
                        draw.rectangle([x, y, x+w, y+h], outline='#FF0000', width=6)
                        # Add a semi-transparent red overlay to make the difference more visible
                        overlay = Image.new('RGBA', result.size, (0, 0, 0, 0))
                        overlay_draw = ImageDraw.Draw(overlay)
                        overlay_draw.rectangle([x, y, x+w, y+h], fill=(255, 0, 0, 80))
                        result = Image.alpha_composite(result.convert('RGBA'), overlay).convert('RGB')
                        draw = ImageDraw.Draw(result)
                        significant_changes += 1
        
        final = result
        
        # Create a side-by-side comparison with the original and marked images
        # This makes it easier to see what's different
        width, height = base.size
        comparison = Image.new('RGB', (width * 2, height))
        comparison.paste(base, (0, 0))
        comparison.paste(result, (width, 0))
        
        # Draw a dividing line
        draw_comp = ImageDraw.Draw(comparison)
        draw_comp.line([(width, 0), (width, height)], fill='white', width=2)
        
        # Add labels
        try:
            font = ImageFont.truetype("Arial.ttf", 20)
        except IOError:
            font = ImageFont.load_default()
            
        draw_comp.text((10, 10), "Original", fill='white', font=font)
        draw_comp.text((width + 10, 10), "Differences Highlighted", fill='red', font=font)
        
        # Save the comparison image
        output_dir = os.path.dirname(image1_path)
        output_path = os.path.join(output_dir, 'diff_marked.png')
        comparison.save(output_path)
        
        # Create a debug image showing the processing steps (helpful for tuning)
        debug_img = np.hstack([base_gray, other_gray, diff_cv, thresh, eroded])
        debug_path = os.path.join(output_dir, 'debug_steps.png')
        cv2.imwrite(debug_path, debug_img)
        
        # Add information about significant changes to the result
        print(f"Found {significant_changes} completely different objects")

        # Return more detailed information
        return {
            'similarity': similarity,
            'is_similar': is_similar,
            'diff_percentage': diff_percentage,
            'diff_image': output_path
        }

    except Exception as e:
        # Return more detailed information
        return {'error': str(e)}

if __name__ == '__main__':
    import sys
    
    if len(sys.argv) != 3:
        print("Usage: python compare_images.py <image1_path> <image2_path>")
        sys.exit(1)
        
    result = compare_images(sys.argv[1], sys.argv[2])
    print(result)