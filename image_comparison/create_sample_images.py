#!/usr/bin/env python3
"""
<PERSON>ript to create sample images for testing the image comparison tool.
"""

import os
import numpy as np
from PIL import Image, ImageDraw, ImageFont

def create_sample_image(filename, text, size=(800, 600), bg_color=(255, 255, 255), text_color=(0, 0, 0)):
    """Create a sample image with text."""
    # Create a blank image with the specified background color
    img = Image.new('RGB', size, color=bg_color)
    draw = ImageDraw.Draw(img)
    
    # Try to use a default font
    try:
        font = ImageFont.truetype("Arial", 40)
    except IOError:
        # If Arial is not available, use default font
        font = ImageFont.load_default()
    
    # Calculate text position to center it
    text_width, text_height = draw.textsize(text, font=font) if hasattr(draw, 'textsize') else (200, 40)
    position = ((size[0] - text_width) // 2, (size[1] - text_height) // 2)
    
    # Draw the text
    draw.text(position, text, fill=text_color, font=font)
    
    # Save the image
    img.save(filename)
    print(f"Created sample image: {filename}")

def main():
    """Create sample images for baseline and new folders."""
    # Create baseline directory if it doesn't exist
    os.makedirs("baseline", exist_ok=True)
    
    # Create new directory if it doesn't exist
    os.makedirs("new", exist_ok=True)
    
    # Create sample images with action IDs for baseline
    action_ids = [
        "al_Kn2JjueZaU",
        "al_9xPqRs7mLt",
        "al_3bVcNdKfGh",
        "al_5zXyWvUoIp"
    ]
    
    # Create baseline images
    for i, action_id in enumerate(action_ids):
        create_sample_image(
            f"baseline/{action_id}.png",
            f"Baseline Image {i+1}\nAction ID: {action_id}",
            bg_color=(240, 240, 255)
        )
    
    # Create slightly different images for the new folder
    for i, action_id in enumerate(action_ids):
        # For the first image, make it identical
        if i == 0:
            create_sample_image(
                f"new/{action_id}.png",
                f"Baseline Image {i+1}\nAction ID: {action_id}",
                bg_color=(240, 240, 255)
            )
        # For the second image, change text slightly
        elif i == 1:
            create_sample_image(
                f"new/{action_id}.png",
                f"New Image {i+1}\nAction ID: {action_id}",
                bg_color=(240, 240, 255)
            )
        # For the third image, change background color
        elif i == 2:
            create_sample_image(
                f"new/{action_id}.png",
                f"Baseline Image {i+1}\nAction ID: {action_id}",
                bg_color=(255, 240, 240)
            )
        # For the fourth image, change both text and color
        else:
            create_sample_image(
                f"new/{action_id}.png",
                f"New Image {i+1}\nAction ID: {action_id}",
                bg_color=(240, 255, 240)
            )

if __name__ == "__main__":
    main()
