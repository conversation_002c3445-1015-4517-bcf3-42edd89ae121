import os
import argparse
import base64
import requests
import concurrent.futures
from PIL import Image
import webbrowser
import glob
import time
import json
import random
import logging
import threading
from datetime import datetime
from queue import Queue

# Import cv2 (OpenCV) for fallback comparison
try:
    import cv2
    import numpy as np
    HAS_OPENCV = True
except ImportError:
    HAS_OPENCV = False

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('image_comparison.log')
    ]
)

def encode_image_to_base64(image_path):
    try:
        with open(image_path, "rb") as img_file:
            return base64.b64encode(img_file.read()).decode('utf-8')
    except Exception as e:
        logging.error(f"Error encoding image {image_path}: {e}")
        raise

def extract_response_content(response_json, model):
    """
    Extract content from different API response formats based on the model
    """
    # Log the response structure for debugging
    logging.debug(f"Response structure for {model}: {json.dumps(response_json, indent=2)}")
    
    # First, handle the standard OpenAI format (which has 'choices' array)
    if 'choices' in response_json and response_json['choices']:
        try:
            if 'message' in response_json['choices'][0]:
                if 'content' in response_json['choices'][0]['message']:
                    return response_json['choices'][0]['message']['content']
        except (KeyError, IndexError):
            pass
    
    # Handle Kimi format (moonshotai/kimi-vl-a3b-thinking:free)
    if model.startswith('moonshotai/kimi'):
        # Direct response field
        if 'response' in response_json:
            return response_json['response']
        # Nested in object
        if 'object' in response_json and response_json['object'] == 'chat.completion':
            if 'choices' in response_json and len(response_json['choices']) > 0:
                if 'message' in response_json['choices'][0]:
                    if 'content' in response_json['choices'][0]['message']:
                        return response_json['choices'][0]['message']['content']
    
    # Handle Llama format (meta-llama/llama-4-maverick:free)
    if model.startswith('meta-llama'):
        # Check for content directly
        if 'content' in response_json:
            return response_json['content']
        # Check for content in choices
        if 'choices' in response_json and len(response_json['choices']) > 0:
            if 'message' in response_json['choices'][0]:
                if 'content' in response_json['choices'][0]['message']:
                    return response_json['choices'][0]['message']['content']
            # Alternative format
            if 'content' in response_json['choices'][0]:
                return response_json['choices'][0]['content']
    
    # Handle Mistral format (mistralai/mistral-small-3.1-24b-instruct:free)
    if model.startswith('mistralai'):
        # Direct content field
        if 'content' in response_json:
            return response_json['content']
        # Check standard format
        if 'choices' in response_json and len(response_json['choices']) > 0:
            if 'message' in response_json['choices'][0]:
                if 'content' in response_json['choices'][0]['message']:
                    return response_json['choices'][0]['message']['content']
        # Alternative format
        if 'response' in response_json:
            return response_json['response']
    
    # Handle Qwen format (qwen/qwen2.5-vl-72b-instruct:free)
    if model.startswith('qwen'):
        # Common Qwen format
        if 'choices' in response_json and len(response_json['choices']) > 0:
            if 'message' in response_json['choices'][0]:
                if 'content' in response_json['choices'][0]['message']:
                    return response_json['choices'][0]['message']['content']
        # Alternative Qwen format with output field
        if 'output' in response_json:
            if isinstance(response_json['output'], str):
                return response_json['output']
            elif isinstance(response_json['output'], dict) and 'content' in response_json['output']:
                return response_json['output']['content']
        # Another alternative format
        if 'outputs' in response_json and len(response_json['outputs']) > 0:
            if 'text' in response_json['outputs'][0]:
                return response_json['outputs'][0]['text']
    
    # Generic fallbacks for any model
    
    # Check for direct message field
    if 'message' in response_json:
        if 'content' in response_json['message']:
            return response_json['message']['content']
    
    # Check for direct response field
    if 'response' in response_json:
        return response_json['response']
    
    # Check for Anthropic/Claude format
    if 'content' in response_json and isinstance(response_json['content'], list):
        for content_item in response_json['content']:
            if isinstance(content_item, dict) and content_item.get('type') == 'text':
                return content_item.get('text', '')
    
    # If we can't find the content in any of the known formats, log the structure and raise an error
    structure = json.dumps(response_json, indent=2)
    logging.error(f"Unknown response format for {model}: {structure}")
    
    # As a last resort, try to find any field that might contain the response text
    for key in ['text', 'content', 'message', 'output', 'response', 'result']:
        if key in response_json:
            value = response_json[key]
            if isinstance(value, str) and len(value) > 10:  # Heuristic: it's probably the content if it's a substantial string
                logging.warning(f"Using fallback field '{key}' for content from {model}")
                return value
    
    raise KeyError(f"Could not extract content from response for model {model}")

def fallback_image_comparison(baseline_path, new_path):
    """
    Perform a simple pixel-based comparison as a fallback when the API is rate-limited
    Returns a description of the differences and whether differences were found
    """
    if not HAS_OPENCV:
        return "Error: OpenCV is not installed. Cannot perform fallback comparison.", True
    
    try:
        # Read images
        baseline_img = cv2.imread(baseline_path)
        new_img = cv2.imread(new_path)
        
        if baseline_img is None or new_img is None:
            return "Error reading one or both images for comparison.", True
            
        # Make sure images are the same size
        if baseline_img.shape != new_img.shape:
            return f"Images have different dimensions. Baseline: {baseline_img.shape}, New: {new_img.shape}", True
            
        # Calculate absolute difference between images
        diff = cv2.absdiff(baseline_img, new_img)
        
        # Check if there are any differences
        if np.sum(diff) == 0:
            return "No visual differences detected", False
            
        # Count the number of different pixels (where diff is not black)
        non_black_pixels = np.sum(diff > 0)
        total_pixels = baseline_img.shape[0] * baseline_img.shape[1]
        diff_percentage = (non_black_pixels / (total_pixels * 3)) * 100  # 3 channels
        
        # Determine areas of difference
        diff_gray = cv2.cvtColor(diff, cv2.COLOR_BGR2GRAY)
        _, thresh = cv2.threshold(diff_gray, 30, 255, cv2.THRESH_BINARY)
        contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        # Generate a description based on the differences
        if len(contours) == 0:
            return "Minor pixel-level differences detected but no significant changes.", True
            
        # Get some statistics about the differences
        regions = len(contours)
        largest_area = max([cv2.contourArea(c) for c in contours])
        
        if regions == 1 and largest_area < 100:
            description = f"Small change detected in a single area. Approximately {diff_percentage:.2f}% of pixels differ."
        elif regions < 5:
            description = f"Changes detected in {regions} distinct regions. Approximately {diff_percentage:.2f}% of pixels differ."
        else:
            description = f"Multiple changes detected across {regions} regions. Approximately {diff_percentage:.2f}% of pixels differ."
            
        return description, True
            
    except Exception as e:
        return f"Error performing fallback comparison: {str(e)}", True

def compare_image_pair(baseline_path, new_path, api_key, output_dir='results', model="opengvlab/internvl3-14b:free", error_delay=30, use_fallback=False):
    """
    Compare a single pair of images using OpenRouter API
    Returns a dictionary with comparison results
    """
    image_name = os.path.basename(baseline_path)
    logging.info(f"Processing: {image_name} with model {model}")
    
    try:
        # Create output directory and save baseline and new images
        os.makedirs(output_dir, exist_ok=True)
        baseline_save = os.path.join(output_dir, f'baseline_{image_name}')
        new_save = os.path.join(output_dir, f'new_{image_name}')
        
        try:
            # Save images first, so we have them even if API calls fail
            Image.open(baseline_path).save(baseline_save)
            Image.open(new_path).save(new_save)
        except Exception as e:
            logging.error(f"Error saving images for {image_name}: {e}")
            # Continue to try API call even if saving fails
        
        # Encode images
        try:
            baseline_b64 = encode_image_to_base64(baseline_path)
            new_b64 = encode_image_to_base64(new_path)
        except Exception as e:
            logging.error(f"Could not encode images for {image_name}: {e}")
            return {
                'image_name': image_name,
                'baseline_path': baseline_save,
                'new_path': new_save,
                'llm_response': f"Error: Could not encode images - {str(e)}",
                'has_differences': False,
                'error': True,
                'model': model
            }

        # Prepare request for OpenRouter
        api_url = "https://openrouter.ai/api/v1/chat/completions"
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json",
            "HTTP-Referer": "https://image-comparison-tool.com", 
            "X-Title": "Image Comparison Tool"
        }
        
        # API request data
        data = {
            "model": model,
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": "Describe the visual differences between the first and second image. If they appear identical, just respond with 'No visual differences detected'."},
                        {"type": "image_url", "image_url": {"url": f"data:image/png;base64,{baseline_b64}"}},
                        {"type": "image_url", "image_url": {"url": f"data:image/png;base64,{new_b64}"}}
                    ]
                }
            ],
            "max_tokens": 512,
            "temperature": 0.2
        }

        # Send request with retry mechanism
        max_retries = 5
        retry_delay = 2  # seconds
        
        for attempt in range(max_retries):
            try:
                # Add a delay before each request to avoid rate limits
                time.sleep(random.uniform(1.0, 2.0))
                
                logging.info(f"Sending API request for {image_name} with {model} (attempt {attempt+1}/{max_retries})")
                response = requests.post(api_url, headers=headers, json=data, timeout=90)
                
                # Log the response status
                logging.info(f"Received status code {response.status_code} for {image_name} with {model}")
                
                # Try to parse the response, even for error status codes
                try:
                    response_json = response.json()
                except json.JSONDecodeError:
                    response_json = {}
                
                # Check if we're being rate limited (daily quota)
                if response.status_code == 429:
                    # Check if this is a daily quota limit which won't be resolved by retrying
                    if response_json.get('error', {}).get('message', '').startswith('Rate limit exceeded: free-models-per-day'):
                        logging.error(f"Daily quota exceeded for {model}. Please add credits to your OpenRouter account or try again tomorrow.")
                        
                        # If fallback is enabled, use OpenCV comparison instead
                        if use_fallback and HAS_OPENCV:
                            logging.info(f"Using fallback comparison for {image_name}")
                            fallback_result, has_differences = fallback_image_comparison(baseline_path, new_path)
                            return {
                                'image_name': image_name,
                                'baseline_path': baseline_save,
                                'new_path': new_save,
                                'llm_response': f"[FALLBACK COMPARISON] {fallback_result}",
                                'has_differences': has_differences,
                                'model': "fallback_opencv",
                                'fallback': True
                            }
                        
                        # No fallback or OpenCV missing
                        return {
                            'image_name': image_name,
                            'baseline_path': baseline_save,
                            'new_path': new_save,
                            'llm_response': "Error: Daily OpenRouter quota exceeded. Please add credits to your account or try again tomorrow.",
                            'has_differences': False,
                            'error': True,
                            'model': model
                        }
                    else:
                        # This is a standard rate limit, wait and retry
                        wait_time = 20 + attempt * 10  # Longer waits for rate limits
                        logging.warning(f"Rate limit hit for {image_name} with {model}, waiting {wait_time} seconds")
                        time.sleep(wait_time)
                        continue

                # Check for other error status codes
                if response.status_code != 200:
                    if attempt < max_retries - 1:
                        wait_time = retry_delay * (2 ** attempt)  # Exponential backoff
                        logging.warning(f"Received status code {response.status_code} for {image_name} with {model}, retrying in {wait_time} seconds")
                        time.sleep(wait_time)
                        continue
                    else:
                        logging.error(f"Failed with status code {response.status_code} for {image_name} with {model} after {max_retries} attempts")
                        return {
                            'image_name': image_name,
                            'baseline_path': baseline_save,
                            'new_path': new_save,
                            'llm_response': f"Error: API returned status code {response.status_code}",
                            'has_differences': False,
                            'error': True,
                            'model': model
                        }
                
                # Handle malformed JSON responses
                if not response_json and response.status_code == 200:
                    if attempt < max_retries - 1:
                        logging.warning(f"Received empty JSON for {image_name} with {model}, retrying")
                        time.sleep(retry_delay * (attempt + 1))
                        continue
                    else:
                        logging.error(f"Empty JSON response for {image_name} with {model} after {max_retries} attempts")
                        return {
                            'image_name': image_name,
                            'baseline_path': baseline_save,
                            'new_path': new_save,
                            'llm_response': "Error: Empty response from API",
                            'has_differences': False,
                            'error': True,
                            'model': model
                        }
                
                # Try to extract content from the response using our multi-model extractor
                try:
                    llm_response = extract_response_content(response_json, model)
                    logging.info(f"Successfully processed {image_name} with {model}")
                    
                    # Check if images are identical based on LLM response
                    has_differences = "No visual differences detected" not in llm_response
                    
                    return {
                        'image_name': image_name,
                        'baseline_path': baseline_save,
                        'new_path': new_save,
                        'llm_response': llm_response,
                        'has_differences': has_differences,
                        'model': model
                    }
                except KeyError as e:
                    if attempt < max_retries - 1:
                        wait_time = retry_delay * (2 ** attempt) + random.uniform(1, 5)
                        logging.warning(f"Could not extract content from response for {image_name} with {model}, retrying in {wait_time:.1f} seconds... ({e})")
                        time.sleep(wait_time)
                        continue
                    else:
                        logging.error(f"Failed to extract content from response for {image_name} with {model} after {max_retries} attempts")
                        
                        # Save the raw response for debugging
                        try:
                            debug_file = os.path.join(output_dir, f"debug_{image_name}_{model.split('/')[-1]}.json")
                            with open(debug_file, 'w') as f:
                                json.dump(response_json, f, indent=2)
                            error_msg = f"Error: Could not extract content from API response. Debug file saved to {debug_file}"
                        except Exception as debug_error:
                            error_msg = f"Error: Could not extract content from API response. Additionally, failed to save debug file: {debug_error}"
                            
                        # Wait longer before returning to prevent rapid failures
                        time.sleep(error_delay)
                        return {
                            'image_name': image_name,
                            'baseline_path': baseline_save,
                            'new_path': new_save,
                            'llm_response': error_msg,
                            'has_differences': False,
                            'error': True,
                            'model': model
                        }
            
            except requests.exceptions.RequestException as e:
                if attempt < max_retries - 1:
                    wait_time = retry_delay * (2 ** attempt) + random.uniform(1, 5)
                    logging.warning(f"Request error for {image_name} with {model}, retrying in {wait_time:.1f} seconds: {e}")
                    time.sleep(wait_time)
                else:
                    logging.error(f"Failed to process {image_name} with {model} after {max_retries} attempts: {e}")
                    return {
                        'image_name': image_name,
                        'baseline_path': baseline_save,
                        'new_path': new_save,
                        'llm_response': f"Error: {str(e)}",
                        'has_differences': False,
                        'error': True,
                        'model': model
                    }
    
    except Exception as e:
        logging.error(f"Unexpected error processing {image_name} with {model}: {e}")
        return {
            'image_name': image_name,
            'baseline_path': baseline_path if 'baseline_save' not in locals() else baseline_save,
            'new_path': new_path if 'new_save' not in locals() else new_save,
            'llm_response': f"Error: {str(e)}",
            'has_differences': False,
            'error': True,
            'model': model
        }

def generate_html_report(results, output_dir):
    """
    Generate an HTML report with all comparison results
    """
    # Count statistics
    total_images = len(results)
    images_with_differences = sum(1 for r in results if r.get('has_differences', False))
    error_images = sum(1 for r in results if r.get('error', False))
    
    # Count by model
    model_stats = {}
    for result in results:
        model = result.get('model', 'unknown')
        if model not in model_stats:
            model_stats[model] = {
                'total': 0,
                'success': 0,
                'error': 0,
                'with_differences': 0
            }
        
        model_stats[model]['total'] += 1
        if not result.get('error', False):
            model_stats[model]['success'] += 1
        else:
            model_stats[model]['error'] += 1
        
        if result.get('has_differences', False):
            model_stats[model]['with_differences'] += 1
    
    # Create timestamp 
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    # Generate HTML
    html = f"""<!DOCTYPE html>
<html>
<head>
    <title>Image Comparison Report</title>
    <style>
        body {{
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #F0F0F0;
        }}
        .header {{
            background-color: #333;
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }}
        .summary {{
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }}
        .stat-box {{
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            flex: 1;
            min-width: 150px;
            margin: 0 10px 10px 0;
            text-align: center;
        }}
        .stat-box h3 {{
            margin-top: 0;
        }}
        .controls {{
            margin-bottom: 20px;
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            align-items: center;
        }}
        .filter-btn {{
            padding: 8px 15px;
            background: #3498db;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }}
        .filter-btn.active {{
            background: #2980b9;
        }}
        .comparison-row {{
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin-bottom: 30px;
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }}
        .image-container {{
            text-align: center;
        }}
        .image-container img {{
            max-width: 100%;
            border: 1px solid #ddd;
            border-radius: 4px;
        }}
        .llm-response {{
            padding: 15px;
            background: #FFFBE6;
            border-left: 5px solid #F7C948;
            font-family: Arial, sans-serif;
            border-radius: 4px;
            height: 100%;
            overflow-y: auto;
        }}
        .error {{
            background: #FFECEC;
            border-left-color: #FF5252;
        }}
        .no-difference {{
            background: #E8F5E9;
            border-left-color: #4CAF50;
        }}
        .image-name {{
            font-weight: bold;
            margin-bottom: 10px;
            padding-bottom: 5px;
            border-bottom: 1px solid #eee;
        }}
        .search-container {{
            flex-grow: 1;
            margin-left: 10px;
        }}
        #searchInput {{
            padding: 8px;
            width: 100%;
            border: 1px solid #ddd;
            border-radius: 4px;
        }}
        .model-stats {{
            margin-top: 20px;
            margin-bottom: 30px;
        }}
        .model-stats table {{
            width: 100%;
            border-collapse: collapse;
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border-radius: 8px;
            overflow: hidden;
        }}
        .model-stats th, .model-stats td {{
            border: 1px solid #ddd;
            padding: 12px;
            text-align: center;
        }}
        .model-stats th {{
            background-color: #f2f2f2;
            font-weight: bold;
        }}
        .model-stats tr:nth-child(even) {{
            background-color: #f9f9f9;
        }}
        .model-badge {{
            display: inline-block;
            margin-left: 5px;
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 0.8em;
            background-color: #e0e0e0;
        }}
    </style>
</head>
<body>
    <div class="header">
        <h1>Image Comparison Report</h1>
        <p>Generated on: ${timestamp}</p>
    </div>

    <div class="summary">
        <div class="stat-box">
            <h3>Total Images</h3>
            <p>${total_images}</p>
        </div>
        <div class="stat-box">
            <h3>With Differences</h3>
            <p>${images_with_differences}</p>
        </div>
        <div class="stat-box">
            <h3>Errors</h3>
            <p>${error_images}</p>
        </div>
    </div>
    
    <div class="model-stats">
        <h2>Model Performance</h2>
        <table>
            <tr>
                <th>Model</th>
                <th>Total Images</th>
                <th>Success Rate</th>
                <th>Error Rate</th>
                <th>Differences Detected</th>
            </tr>
"""

    # Add model statistics
    for model_name, stats in model_stats.items():
        success_rate = (stats['success'] / stats['total'] * 100) if stats['total'] > 0 else 0
        error_rate = (stats['error'] / stats['total'] * 100) if stats['total'] > 0 else 0
        html += f"""
            <tr>
                <td>{model_name}</td>
                <td>{stats['total']}</td>
                <td>{success_rate:.1f}%</td>
                <td>{error_rate:.1f}%</td>
                <td>{stats['with_differences']}</td>
            </tr>
        """

    html += """
        </table>
    </div>

    <div class="controls">
        <button class="filter-btn active" onclick="filterImages('all')">All Images (${total_images})</button>
        <button class="filter-btn" onclick="filterImages('differences')">Only Differences (${images_with_differences})</button>
        <button class="filter-btn" onclick="filterImages('errors')">Only Errors (${error_images})</button>
"""

    # Add model filter buttons
    for model_name in model_stats.keys():
        html += f"""
        <button class="filter-btn" onclick="filterByModel('{model_name}')">Only {model_name} ({model_stats[model_name]['total']})</button>
"""

    html += """
        <div class="search-container">
            <input type="text" id="searchInput" onkeyup="searchImages()" placeholder="Search by image name...">
        </div>
    </div>

    <div id="comparison-container">
"""

    # Add comparison rows
    for result in results:
        image_name = result.get('image_name', 'Unknown')
        baseline_path = os.path.abspath(result.get('baseline_path', ''))
        new_path = os.path.abspath(result.get('new_path', ''))
        llm_response = result.get('llm_response', 'No response')
        has_differences = result.get('has_differences', False)
        has_error = result.get('error', False)
        model = result.get('model', 'unknown')
        
        response_class = "error" if has_error else ("no-difference" if not has_differences else "")

        html += f"""
        <div class="comparison-row" data-has-differences="{str(has_differences).lower()}" data-has-error="{str(has_error).lower()}" data-image-name="{image_name}" data-model="{model}">
            <div class="image-container">
                <div class="image-name">Baseline</div>
                <img src="file://{baseline_path}" alt="Baseline Image">
            </div>
            <div class="image-container">
                <div class="image-name">New</div>
                <img src="file://{new_path}" alt="New Image">
            </div>
            <div class="llm-response {response_class}">
                <div class="image-name">{image_name} <span class="model-badge">{model}</span></div>
                {llm_response}
            </div>
        </div>
"""

    # Add JavaScript for filtering
    html += """
    </div>

    <script>
        function filterImages(filter) {
            const rows = document.querySelectorAll('.comparison-row');
            const buttons = document.querySelectorAll('.filter-btn');
            
            // Update active button
            buttons.forEach(btn => {
                btn.classList.remove('active');
                if (btn.textContent.toLowerCase().includes(filter)) {
                    btn.classList.add('active');
                }
            });
            
            // Apply filter
            rows.forEach(row => {
                if (filter === 'all') {
                    row.style.display = '';
                } else if (filter === 'differences') {
                    row.style.display = row.dataset.hasDifferences === 'true' ? '' : 'none';
                } else if (filter === 'errors') {
                    row.style.display = row.dataset.hasError === 'true' ? '' : 'none';
                }
            });
        }
        
        function filterByModel(modelName) {
            const rows = document.querySelectorAll('.comparison-row');
            const buttons = document.querySelectorAll('.filter-btn');
            
            // Update active button
            buttons.forEach(btn => {
                btn.classList.remove('active');
                if (btn.textContent.toLowerCase().includes(modelName.toLowerCase())) {
                    btn.classList.add('active');
                }
            });
            
            // Apply filter
            rows.forEach(row => {
                if (row.dataset.model === modelName) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }
        
        function searchImages() {
            const searchText = document.getElementById('searchInput').value.toLowerCase();
            const rows = document.querySelectorAll('.comparison-row');
            
            rows.forEach(row => {
                const imageName = row.dataset.imageName.toLowerCase();
                if (imageName.includes(searchText)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }
    </script>
</body>
</html>
    """
    
    # Save HTML file
    html_path = os.path.join(output_dir, 'image_comparison_report.html')
    with open(html_path, 'w') as f:
        f.write(html)
    
    return html_path

def find_image_pairs(baseline_dir, new_dir):
    """
    Find matching image pairs between baseline and new directories
    """
    baseline_images = glob.glob(os.path.join(baseline_dir, "*.png"))
    new_images = glob.glob(os.path.join(new_dir, "*.png"))
    
    # Extract image names
    baseline_dict = {os.path.basename(img): img for img in baseline_images}
    new_dict = {os.path.basename(img): img for img in new_images}
    
    # Find common image names
    common_images = set(baseline_dict.keys()) & set(new_dict.keys())
    
    # Create pairs
    pairs = [(baseline_dict[img], new_dict[img]) for img in common_images]
    
    # Report missing images
    missing_in_new = set(baseline_dict.keys()) - set(new_dict.keys())
    missing_in_baseline = set(new_dict.keys()) - set(baseline_dict.keys())
    
    if missing_in_new:
        logging.warning(f"{len(missing_in_new)} images in baseline not found in new folder")
    
    if missing_in_baseline:
        logging.warning(f"{len(missing_in_baseline)} images in new folder not found in baseline")
    
    logging.info(f"Found {len(pairs)} matching image pairs")
    return pairs

def process_with_multiple_models(pairs, api_key, output_dir='results', error_delay=30, continue_from=0, use_fallback=False):
    """
    Process image pairs using multiple models in parallel - one model per thread
    """
    all_results = []
    total_pairs = len(pairs)
    models = get_available_models()
    
    # Load any existing results if continuing from a previous run
    results_json = os.path.join(output_dir, 'comparison_results.json')
    if continue_from > 0 and os.path.exists(results_json):
        try:
            with open(results_json, 'r') as f:
                all_results = json.load(f)
            logging.info(f"Loaded {len(all_results)} existing results from previous run")
        except Exception as e:
            logging.error(f"Error loading existing results: {e}")
            all_results = []
    
    # Process images in batches with multiple models in parallel
    index = max(0, continue_from)
    
    while index < total_pairs:
        batch_futures = []
        batch_indices = []
        
        # Create a batch of up to 4 images (one for each model)
        with concurrent.futures.ThreadPoolExecutor(max_workers=4) as executor:
            for model_key, model_value in models.items():
                if index < total_pairs:
                    baseline, new = pairs[index]
                    logging.info(f"Submitting image {index + 1}/{total_pairs} to model {model_key}")
                    
                    # Submit task to executor
                    future = executor.submit(
                        compare_image_pair, 
                        baseline, new, 
                        api_key, 
                        output_dir, 
                        model_value, 
                        error_delay,
                        use_fallback
                    )
                    batch_futures.append(future)
                    batch_indices.append(index)
                    index += 1
            
            # Wait for all submitted tasks to complete
            for i, future in enumerate(batch_futures):
                try:
                    result = future.result()
                    all_results.append(result)
                    logging.info(f"Completed image {batch_indices[i] + 1}/{total_pairs}")
                except Exception as e:
                    logging.error(f"Error processing image {batch_indices[i] + 1}: {e}")
                    
                    # Create error result
                    baseline, new = pairs[batch_indices[i]]
                    error_result = {
                        'image_name': os.path.basename(baseline),
                        'baseline_path': baseline,
                        'new_path': new,
                        'llm_response': f"Error: {str(e)}",
                        'has_differences': False,
                        'error': True,
                        'model': "unknown"  # We don't know which model failed
                    }
                    all_results.append(error_result)
        
        # Save results after each batch for resumability
        save_results_to_json(all_results, output_dir)
        
        # Add a delay between batches to avoid rate limiting
        if index < total_pairs:
            delay = random.uniform(8, 15)  # Random delay between 8-15 seconds between batches
            logging.info(f"Waiting {delay:.1f} seconds before next batch...")
            time.sleep(delay)
    
    return all_results

def process_sequentially(pairs, api_key, output_dir='results', model="opengvlab/internvl3-14b:free", error_delay=30, continue_from=0, use_fallback=False):
    """
    Process image pairs one by one sequentially
    """
    all_results = []
    total_pairs = len(pairs)
    
    # Load any existing results if continuing from a previous run
    results_json = os.path.join(output_dir, 'comparison_results.json')
    if continue_from > 0 and os.path.exists(results_json):
        try:
            with open(results_json, 'r') as f:
                all_results = json.load(f)
            logging.info(f"Loaded {len(all_results)} existing results from previous run")
        except Exception as e:
            logging.error(f"Error loading existing results: {e}")
            all_results = []
    
    # Process images starting from the continue_from index
    for index in range(max(0, continue_from), total_pairs):
        baseline, new = pairs[index]
        logging.info(f"Processing image {index + 1}/{total_pairs}")
        
        # Process one image at a time
        result = compare_image_pair(baseline, new, api_key, output_dir, model, error_delay, use_fallback)
        all_results.append(result)
        
        # Save results after each image for resumability
        save_results_to_json(all_results, output_dir)
        
        # Add a delay between image processing to avoid rate limiting
        if index < total_pairs - 1:  # If not the last image
            delay = random.uniform(5, 10)  # Random delay between 5-10 seconds
            logging.info(f"Waiting {delay:.1f} seconds before next image...")
            time.sleep(delay)
    
    return all_results

def save_results_to_json(results, output_dir='results'):
    """
    Save results to JSON file for potential future use or analysis
    """
    os.makedirs(output_dir, exist_ok=True)
    json_path = os.path.join(output_dir, 'comparison_results.json')
    
    # Convert absolute paths to relative for better portability
    portable_results = []
    for result in results:
        portable_result = result.copy()
        if 'baseline_path' in portable_result and portable_result['baseline_path']:
            portable_result['baseline_path'] = os.path.basename(portable_result['baseline_path'])
        if 'new_path' in portable_result and portable_result['new_path']:
            portable_result['new_path'] = os.path.basename(portable_result['new_path'])
        portable_results.append(portable_result)
    
    with open(json_path, 'w') as f:
        json.dump(portable_results, f, indent=2)
    
    return json_path

def get_available_models():
    """Returns a dictionary of available models"""
    return {
        "kimi": "moonshotai/kimi-vl-a3b-thinking:free",
        "llama": "meta-llama/llama-4-maverick:free",
        "mistral": "mistralai/mistral-small-3.1-24b-instruct:free",
        "qwen": "qwen/qwen2.5-vl-72b-instruct:free"
    }

# Debugging tool: inspect a model's response format
def debug_model_response(api_key, model, image_path):
    """
    Debug a model's response format by sending a test request
    """
    logging.info(f"Testing response format for model: {model}")
    
    try:
        # Encode image
        image_b64 = encode_image_to_base64(image_path)
        
        # Prepare request
        api_url = "https://openrouter.ai/api/v1/chat/completions"
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json",
            "HTTP-Referer": "https://image-comparison-tool.com", 
            "X-Title": "Image Comparison Tool"
        }
        
        data = {
            "model": model,
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": "Briefly describe this image"},
                        {"type": "image_url", "image_url": {"url": f"data:image/png;base64,{image_b64}"}}
                    ]
                }
            ],
            "max_tokens": 100,
            "temperature": 0.2
        }
        
        # Send request
        response = requests.post(api_url, headers=headers, json=data, timeout=90)
        
        if response.status_code == 200:
            response_json = response.json()
            logging.info(f"Response format for {model}:")
            logging.info(json.dumps(response_json, indent=2))
            
            # Try to extract content
            try:
                content = extract_response_content(response_json, model)
                logging.info(f"Successfully extracted content: {content}")
                return True
            except Exception as e:
                logging.error(f"Failed to extract content: {e}")
                return False
        else:
            logging.error(f"Request failed with status {response.status_code}")
            return False
            
    except Exception as e:
        logging.error(f"Error debugging model response: {e}")
        return False

# Add a debugging function to inspect model responses
def debug_models(api_key, test_image, output_dir='debug'):
    """
    Run test requests against all models to debug their response formats
    """
    os.makedirs(output_dir, exist_ok=True)
    models = get_available_models()
    
    results = {}
    for model_name, model_id in models.items():
        logging.info(f"Testing {model_name} ({model_id})...")
        
        try:
            # Encode image
            image_b64 = encode_image_to_base64(test_image)
            
            # Prepare request
            api_url = "https://openrouter.ai/api/v1/chat/completions"
            headers = {
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json",
                "HTTP-Referer": "https://image-comparison-tool.com",
                "X-Title": "Image Comparison Tool - Debug"
            }
            
            data = {
                "model": model_id,
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {"type": "text", "text": "Briefly describe this image."},
                            {"type": "image_url", "image_url": {"url": f"data:image/png;base64,{image_b64}"}}
                        ]
                    }
                ],
                "max_tokens": 100,
                "temperature": 0.2
            }
            
            # Send request
            response = requests.post(api_url, headers=headers, json=data, timeout=90)
            
            # Handle response
            if response.status_code == 200:
                response_json = response.json()
                
                # Save the response for analysis
                debug_file = os.path.join(output_dir, f"{model_name}_response.json")
                with open(debug_file, 'w') as f:
                    json.dump(response_json, f, indent=2)
                
                # Try to extract content
                try:
                    content = extract_response_content(response_json, model_id)
                    results[model_name] = {
                        "status": "success",
                        "content": content[:100] + "..." if len(content) > 100 else content
                    }
                    logging.info(f"✅ {model_name}: Successfully extracted content")
                except Exception as e:
                    results[model_name] = {
                        "status": "extract_error",
                        "error": str(e)
                    }
                    logging.error(f"❌ {model_name}: Failed to extract content: {e}")
            else:
                results[model_name] = {
                    "status": "api_error",
                    "status_code": response.status_code
                }
                logging.error(f"❌ {model_name}: API error {response.status_code}")
                
        except Exception as e:
            results[model_name] = {
                "status": "exception",
                "error": str(e)
            }
            logging.error(f"❌ {model_name}: Exception: {e}")
    
    # Save summary
    summary_file = os.path.join(output_dir, "model_debug_summary.json")
    with open(summary_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    logging.info(f"Debug summary saved to {summary_file}")
    return results

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Compare images using OpenRouter AI and generate an HTML report')
    parser.add_argument('--baseline_dir', default='baseline', help='Directory with baseline images')
    parser.add_argument('--new_dir', default='new', help='Directory with new images')
    parser.add_argument('--api_key', default='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', help='Your OpenRouter API key')
    parser.add_argument('--output_dir', default='report', help='Output directory for report')
    parser.add_argument('--model', default='kimi', choices=get_available_models().keys(), 
                        help='Model to use for comparison when not using multi-model mode (default: kimi)')
    parser.add_argument('--error_delay', type=int, default=30, 
                        help='Delay in seconds after error before continuing (default: 30)')
    parser.add_argument('--continue_from', type=int, default=0, 
                        help='Continue from this image index (0-based, useful for resuming interrupted runs)')
    parser.add_argument('--multi_model', action='store_true', default=False,
                        help='Use multiple models in parallel (process 4 images at a time, one with each model)')
    parser.add_argument('--use_fallback', action='store_true', default=False,
                        help='Use OpenCV for basic image comparison as fallback when API is rate limited')
    args = parser.parse_args()
    
    # Show warning about API key if using the default key
    default_key = '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'
    if args.api_key == default_key:
        logging.warning("Using the default OpenRouter API key, which has a limit of 50 requests per day.")
        logging.warning("If you hit rate limits, consider getting your own API key at https://openrouter.ai")
        logging.warning("Then run the script with --api_key YOUR_API_KEY")
        
    # Check if OpenCV is available for fallback functionality
    if args.use_fallback and not HAS_OPENCV:
        logging.warning("OpenCV (cv2) is not available. Fallback comparison will not work.")
        logging.warning("Install OpenCV with: pip install opencv-python")
        
    # Set up model
    models = get_available_models()
    selected_model = models[args.model]
    
    if args.multi_model:
        logging.info("Using multi-model parallel processing mode")
        for model_name, model_id in models.items():
            logging.info(f"- {model_name}: {model_id}")
    else:
        logging.info(f"Using single model: {args.model} ({selected_model})")
    
    # Find all image pairs
    pairs = find_image_pairs(args.baseline_dir, args.new_dir)
    
    if not pairs:
        logging.error("No matching image pairs found. Please check the baseline and new directories.")
        exit(1)
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Process all image pairs
    logging.info(f"Starting comparison of {len(pairs)} images...")
    if args.continue_from > 0:
        logging.info(f"Continuing from image {args.continue_from + 1}")
    
    if args.multi_model:
        logging.info("Processing with multiple models in parallel (4 images at a time)")
        results = process_with_multiple_models(
            pairs, 
            args.api_key, 
            args.output_dir,
            error_delay=args.error_delay,
            continue_from=args.continue_from,
            use_fallback=args.use_fallback
        )
    else:
        logging.info("Processing sequentially with a single model")
        results = process_sequentially(
            pairs, 
            args.api_key, 
            args.output_dir, 
            model=selected_model,
            error_delay=args.error_delay,
            continue_from=args.continue_from,
            use_fallback=args.use_fallback
        )
    
    # Generate HTML report
    logging.info("Generating HTML report...")
    report_path = generate_html_report(results, args.output_dir)
    
    # Open report in browser
    logging.info(f"Report generated at: {os.path.abspath(report_path)}")
    webbrowser.open(f'file://{os.path.abspath(report_path)}')