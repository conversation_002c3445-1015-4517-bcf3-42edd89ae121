#!/usr/bin/env python3
"""
Script to compare images from baseline and new folders and generate an HTML report.
"""

import os
import sys
import glob
import base64
import argparse
from io import BytesIO
from datetime import datetime
import numpy as np
from PIL import Image
import cv2

class ImageComparator:
    """Class to compare images and generate HTML report."""

    def __init__(self, baseline_dir, new_dir, output_dir=None, threshold=0.95):
        """Initialize with directories and threshold."""
        self.baseline_dir = baseline_dir
        self.new_dir = new_dir
        self.output_dir = output_dir or os.path.join(os.path.dirname(os.path.abspath(__file__)), "report")
        self.threshold = threshold
        self.comparison_results = []

        # Create output directory if it doesn't exist
        os.makedirs(self.output_dir, exist_ok=True)

    def find_image_pairs(self):
        """Find matching image pairs based on action IDs in filenames."""
        baseline_images = glob.glob(os.path.join(self.baseline_dir, "*.png"))
        new_images = glob.glob(os.path.join(self.new_dir, "*.png"))

        # Extract action IDs from filenames
        baseline_action_ids = {os.path.splitext(os.path.basename(img))[0]: img for img in baseline_images}
        new_action_ids = {os.path.splitext(os.path.basename(img))[0]: img for img in new_images}

        # Find common action IDs
        common_action_ids = set(baseline_action_ids.keys()) & set(new_action_ids.keys())

        # Create pairs
        pairs = [(baseline_action_ids[action_id], new_action_ids[action_id], action_id)
                 for action_id in common_action_ids]

        # Log missing images
        missing_in_new = set(baseline_action_ids.keys()) - set(new_action_ids.keys())
        missing_in_baseline = set(new_action_ids.keys()) - set(baseline_action_ids.keys())

        if missing_in_new:
            print(f"Warning: {len(missing_in_new)} images in baseline not found in new folder:")
            for action_id in missing_in_new:
                print(f"  - {action_id}")

        if missing_in_baseline:
            print(f"Warning: {len(missing_in_baseline)} images in new folder not found in baseline:")
            for action_id in missing_in_baseline:
                print(f"  - {action_id}")

        print(f"Found {len(pairs)} matching image pairs")
        return pairs

    def compare_images(self, baseline_path, new_path):
        """Compare two images and return similarity score."""
        # Read images
        baseline_img = cv2.imread(baseline_path)
        new_img = cv2.imread(new_path)

        # Check if images were loaded successfully
        if baseline_img is None or new_img is None:
            print(f"Error: Could not read images {baseline_path} or {new_path}")
            return 0.0

        # Convert to grayscale
        baseline_gray = cv2.cvtColor(baseline_img, cv2.COLOR_BGR2GRAY)
        new_gray = cv2.cvtColor(new_img, cv2.COLOR_BGR2GRAY)

        # Resize if dimensions don't match
        if baseline_gray.shape != new_gray.shape:
            print(f"Warning: Images have different dimensions. Resizing for comparison.")
            new_gray = cv2.resize(new_gray, (baseline_gray.shape[1], baseline_gray.shape[0]))

        # Calculate SSIM using OpenCV
        # C1 and C2 are constants to stabilize division with weak denominator
        C1 = 0.01 ** 2
        C2 = 0.03 ** 2

        # Calculate mean, variance, and covariance
        mu1 = cv2.GaussianBlur(baseline_gray, (11, 11), 1.5)
        mu2 = cv2.GaussianBlur(new_gray, (11, 11), 1.5)

        mu1_sq = mu1 * mu1
        mu2_sq = mu2 * mu2
        mu1_mu2 = mu1 * mu2

        sigma1_sq = cv2.GaussianBlur(baseline_gray * baseline_gray, (11, 11), 1.5) - mu1_sq
        sigma2_sq = cv2.GaussianBlur(new_gray * new_gray, (11, 11), 1.5) - mu2_sq
        sigma12 = cv2.GaussianBlur(baseline_gray * new_gray, (11, 11), 1.5) - mu1_mu2

        # Calculate SSIM
        ssim_map = ((2 * mu1_mu2 + C1) * (2 * sigma12 + C2)) / ((mu1_sq + mu2_sq + C1) * (sigma1_sq + sigma2_sq + C2))

        # Calculate mean SSIM
        score = np.mean(ssim_map)

        return score

    def image_to_base64(self, image_path):
        """Convert image to base64 for embedding in HTML."""
        with open(image_path, "rb") as img_file:
            return base64.b64encode(img_file.read()).decode('utf-8')

    def run_comparison(self):
        """Run comparison on all image pairs."""
        pairs = self.find_image_pairs()

        for baseline_path, new_path, action_id in pairs:
            similarity = self.compare_images(baseline_path, new_path)
            status = "Pass" if similarity >= self.threshold else "Fail"

            self.comparison_results.append({
                "action_id": action_id,
                "baseline_path": baseline_path,
                "new_path": new_path,
                "similarity": similarity,
                "status": status,
                "baseline_base64": self.image_to_base64(baseline_path),
                "new_base64": self.image_to_base64(new_path)
            })

        # Sort results by status (failures first) then by similarity
        self.comparison_results.sort(key=lambda x: (0 if x["status"] == "Fail" else 1, x["similarity"]))

        return self.comparison_results

    def generate_html_report(self):
        """Generate HTML report with comparison results."""
        if not self.comparison_results:
            self.run_comparison()

        # Count passes and failures
        passes = sum(1 for result in self.comparison_results if result["status"] == "Pass")
        failures = len(self.comparison_results) - passes

        # Generate HTML
        html = f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Image Comparison Report</title>
    <style>
        body {{
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }}
        .header {{
            background-color: #333;
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }}
        .summary {{
            background-color: #fff;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }}
        .comparison-item {{
            background-color: #fff;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }}
        .comparison-header {{
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }}
        .status {{
            padding: 5px 10px;
            border-radius: 3px;
            font-weight: bold;
        }}
        .pass {{
            background-color: #d4edda;
            color: #155724;
        }}
        .fail {{
            background-color: #f8d7da;
            color: #721c24;
        }}
        .image-container {{
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
        }}
        .image-box {{
            flex: 1;
            min-width: 300px;
            max-width: 400px;
            text-align: center;
        }}
        .image-box img {{
            max-width: 350px;
            max-height: 500px;
            width: auto;
            height: auto;
            border: 1px solid #ddd;
            border-radius: 4px;
            cursor: pointer;
            transition: transform 0.2s;
        }}
        .image-box img:hover {{
            transform: scale(1.02);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }}
        /* Modal styles for full-size image viewing */
        .modal {{
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.9);
            cursor: pointer;
        }}
        .modal-content {{
            margin: auto;
            display: block;
            max-width: 95%;
            max-height: 95%;
            margin-top: 2.5%;
        }}
        .close {{
            position: absolute;
            top: 15px;
            right: 35px;
            color: #f1f1f1;
            font-size: 40px;
            font-weight: bold;
            cursor: pointer;
        }}
        .close:hover {{
            color: #bbb;
        }}
        .similarity-meter {{
            height: 10px;
            background-color: #e9ecef;
            border-radius: 5px;
            margin-top: 10px;
            overflow: hidden;
        }}
        .similarity-value {{
            height: 100%;
            background-color: #4caf50;
        }}
        .filters {{
            margin-bottom: 20px;
            display: flex;
            gap: 10px;
        }}
        .filter-btn {{
            padding: 8px 15px;
            border: none;
            border-radius: 4px;
            background-color: #333;
            color: white;
            cursor: pointer;
        }}
        .filter-btn.active {{
            background-color: #4caf50;
        }}
        .filter-btn:hover {{
            opacity: 0.9;
        }}
    </style>
</head>
<body>
    <div class="header">
        <h1>Image Comparison Report</h1>
        <div>Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</div>
    </div>

    <div class="summary">
        <h2>Summary</h2>
        <p>Total comparisons: {len(self.comparison_results)}</p>
        <p>Passed: {passes} ({passes/len(self.comparison_results)*100:.1f}%)</p>
        <p>Failed: {failures} ({failures/len(self.comparison_results)*100:.1f}%)</p>
        <p>Similarity threshold: {self.threshold*100:.1f}%</p>
    </div>

    <div class="filters">
        <button class="filter-btn active" onclick="filterResults('all')">All ({len(self.comparison_results)})</button>
        <button class="filter-btn" onclick="filterResults('pass')">Pass ({passes})</button>
        <button class="filter-btn" onclick="filterResults('fail')">Fail ({failures})</button>
    </div>

    <div id="results">
"""

        # Add comparison items
        for result in self.comparison_results:
            status_class = "pass" if result["status"] == "Pass" else "fail"
            similarity_percent = result["similarity"] * 100

            html += f"""
        <div class="comparison-item" data-status="{result["status"].lower()}">
            <div class="comparison-header">
                <h3>Action ID: {result["action_id"]}</h3>
                <div class="status {status_class}">{result["status"]}</div>
            </div>
            <p>Similarity: {similarity_percent:.2f}%</p>
            <div class="similarity-meter">
                <div class="similarity-value" style="width: {similarity_percent}%;"></div>
            </div>
            <div class="image-container">
                <div class="image-box">
                    <h4>Baseline</h4>
                    <img src="data:image/png;base64,{result["baseline_base64"]}" alt="Baseline" onclick="openModal(this)">
                    <p>Path: {result["baseline_path"]}</p>
                </div>
                <div class="image-box">
                    <h4>New</h4>
                    <img src="data:image/png;base64,{result["new_base64"]}" alt="New" onclick="openModal(this)">
                    <p>Path: {result["new_path"]}</p>
                </div>
            </div>
        </div>
"""

        # Close HTML
        html += """
    </div>

    <!-- Modal for full-size image viewing -->
    <div id="imageModal" class="modal" onclick="closeModal()">
        <span class="close" onclick="closeModal()">&times;</span>
        <img class="modal-content" id="modalImage">
    </div>

    <script>
        function filterResults(status) {
            const items = document.querySelectorAll('.comparison-item');
            const buttons = document.querySelectorAll('.filter-btn');

            // Update active button
            buttons.forEach(btn => {
                btn.classList.remove('active');
                if (btn.textContent.toLowerCase().includes(status)) {
                    btn.classList.add('active');
                }
            });

            // Filter items
            items.forEach(item => {
                if (status === 'all' || item.dataset.status === status) {
                    item.style.display = 'block';
                } else {
                    item.style.display = 'none';
                }
            });
        }

        function openModal(img) {
            const modal = document.getElementById('imageModal');
            const modalImg = document.getElementById('modalImage');
            modal.style.display = 'block';
            modalImg.src = img.src;
        }

        function closeModal() {
            const modal = document.getElementById('imageModal');
            modal.style.display = 'none';
        }

        // Close modal when pressing Escape key
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                closeModal();
            }
        });
    </script>
</body>
</html>
"""

        # Write HTML to file
        report_path = os.path.join(self.output_dir, "report.html")
        with open(report_path, "w") as f:
            f.write(html)

        print(f"HTML report generated: {report_path}")
        return report_path

def main():
    """Main function to parse arguments and run comparison."""
    parser = argparse.ArgumentParser(description="Compare images and generate HTML report")
    parser.add_argument("--baseline", default="baseline", help="Directory containing baseline images")
    parser.add_argument("--new", default="new", help="Directory containing new images")
    parser.add_argument("--output", default="report", help="Directory to save the HTML report")
    parser.add_argument("--threshold", type=float, default=0.95, help="Similarity threshold (0.0-1.0)")

    args = parser.parse_args()

    comparator = ImageComparator(
        baseline_dir=args.baseline,
        new_dir=args.new,
        output_dir=args.output,
        threshold=args.threshold
    )

    report_path = comparator.generate_html_report()
    print(f"Report generated: {report_path}")
    print(f"Open this file in a web browser to view the report.")

if __name__ == "__main__":
    main()
