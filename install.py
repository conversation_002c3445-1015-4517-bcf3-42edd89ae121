#!/usr/bin/env python
"""
Installation script for Mobile App Automation Tool
This script installs dependencies in the correct order with appropriate error handling
"""
import os
import sys
import subprocess
import platform

def print_step(message):
    """Print a formatted step message"""
    print(f"\n\033[1;34m==>\033[0m \033[1m{message}\033[0m")

def print_success(message):
    """Print a formatted success message"""
    print(f"\033[1;32m✓\033[0m {message}")

def print_error(message):
    """Print a formatted error message"""
    print(f"\033[1;31m✗\033[0m {message}")

def print_warning(message):
    """Print a formatted warning message"""
    print(f"\033[1;33m!\033[0m {message}")

def run_command(command, error_message=None):
    """Run a shell command and handle errors"""
    try:
        result = subprocess.run(command, shell=True, check=True, text=True, 
                               stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        return True, result.stdout
    except subprocess.CalledProcessError as e:
        if error_message:
            print_error(f"{error_message}: {e.stderr}")
        return False, e.stderr

def install_package(package, force_upgrade=False):
    """Install a Python package using pip"""
    upgrade = "--upgrade" if force_upgrade else ""
    command = f"{sys.executable} -m pip install {upgrade} {package}"
    
    print(f"Installing {package}...")
    success, output = run_command(command)
    
    if success:
        print_success(f"Installed {package}")
    else:
        print_error(f"Failed to install {package}")
    
    return success

def check_python_version():
    """Check if Python version is compatible"""
    print_step("Checking Python version")
    
    major, minor, _ = platform.python_version_tuple()
    version = f"{major}.{minor}"
    
    if int(major) < 3 or (int(major) == 3 and int(minor) < 8):
        print_error(f"Python {version} is not supported. Please use Python 3.8+")
        return False
    
    # Check for Python 3.12 which needs special handling
    if int(major) == 3 and int(minor) >= 12:
        print_warning(f"Python {version} detected - this version requires special handling")
        print_warning("Installing setuptools with distutils support first...")
        # Install setuptools first to provide distutils
        run_command(f"{sys.executable} -m pip install setuptools")
    
    print_success(f"Python {version} is compatible")
    return True

def check_pip():
    """Check if pip is installed and up to date"""
    print_step("Checking pip installation")
    
    success, output = run_command(f"{sys.executable} -m pip --version")
    if not success:
        print_error("pip is not installed or not working properly")
        return False
    
    # Update pip itself
    success, _ = run_command(f"{sys.executable} -m pip install --upgrade pip")
    if success:
        print_success("pip is up to date")
    else:
        print_warning("Could not update pip, but continuing anyway")
    
    return True

def install_system_dependencies():
    """Install system-level dependencies based on OS"""
    print_step("Checking system dependencies")
    
    system = platform.system()
    
    if system == "Linux":
        print("Installing system dependencies for Linux...")
        
        # Use a more modern way to detect Linux distribution
        distro = ""
        try:
            # Try to read from /etc/os-release
            with open("/etc/os-release") as f:
                for line in f:
                    if line.startswith("ID="):
                        distro = line.split("=")[1].strip().strip('"')
                        break
        except:
            pass
        
        if not distro:
            # Fall back to checking common directories
            if os.path.exists("/etc/debian_version"):
                distro = "debian"
            elif os.path.exists("/etc/fedora-release"):
                distro = "fedora"
            elif os.path.exists("/etc/redhat-release"):
                distro = "rhel"
        
        if "ubuntu" in distro or "debian" in distro:
            run_command("sudo apt-get update")
            run_command("sudo apt-get install -y python3-dev libffi-dev libssl-dev cmake build-essential")
        elif "fedora" in distro or "centos" in distro or "rhel" in distro:
            run_command("sudo dnf install -y python3-devel libffi-devel openssl-devel cmake")
        else:
            print_warning("Unknown Linux distribution. You may need to install development tools manually.")
        
    elif system == "Darwin":  # macOS
        print("Checking for Homebrew on macOS...")
        brew_installed, _ = run_command("which brew")
        
        if brew_installed:
            run_command("brew install cmake pkg-config")
        else:
            print_warning("Homebrew not found. You may need to install cmake and pkg-config manually.")
            
    elif system == "Windows":
        print_warning("On Windows, you may need to install Microsoft Visual C++ Build Tools.")
        print_warning("See: https://visualstudio.microsoft.com/visual-cpp-build-tools/")
    
    print_success("System dependency check completed")
    return True

def install_basic_dependencies():
    """Install basic Python dependencies"""
    print_step("Installing basic dependencies")
    
    # These packages have few dependencies and are unlikely to cause problems
    basic_deps = [
        "wheel",
        "setuptools",
    ]
    
    # Install basic dependencies first
    success = True
    for dep in basic_deps:
        if not install_package(dep):
            success = False
    
    # Determine numpy version based on Python version
    major, minor, _ = platform.python_version_tuple()
    if int(major) == 3 and int(minor) >= 12:
        # Use newer numpy for Python 3.12+
        numpy_version = "numpy==1.26.0"
    else:
        numpy_version = "numpy==1.24.2"
    
    # Install core dependencies
    core_deps = [
        "Werkzeug==2.2.3",
        "flask==2.2.3",
        "Pillow==9.4.0",
        numpy_version
    ]
    
    for dep in core_deps:
        if not install_package(dep):
            success = False
    
    return success

def install_adb():
    """Install ADB Python interface"""
    print_step("Installing ADB interface")
    
    try:
        # Try the regular package first
        if install_package("pure-python-adb==0.3.0.dev0"):
            return True
        
        # If regular install fails, try installing from GitHub
        print_warning("Regular install failed, trying alternative installation...")
        return install_package("-e git+https://github.com/Swind/pure-python-adb.git#egg=pure-python-adb")
    except Exception as e:
        print_error(f"Failed to install ADB interface: {e}")
        return False

def install_opencv():
    """Install OpenCV"""
    print_step("Installing OpenCV")
    
    try:
        # Try headless version first which has fewer dependencies
        if install_package("opencv-python-headless==4.7.0.72"):
            return True
        
        # If headless fails, try regular OpenCV
        print_warning("Headless OpenCV install failed, trying full OpenCV...")
        return install_package("opencv-python")
    except Exception as e:
        print_error(f"Failed to install OpenCV: {e}")
        return False

def install_socketio():
    """Install SocketIO dependencies"""
    print_step("Installing SocketIO dependencies")
    
    socketio_deps = [
        "python-engineio==4.4.1",
        "python-socketio==5.8.0",
        "flask-socketio==5.3.3"
    ]
    
    success = True
    for dep in socketio_deps:
        if not install_package(dep):
            success = False
    
    return success

def install_scikit_image():
    """Install scikit-image"""
    print_step("Installing scikit-image")
    
    try:
        # Make sure numpy is installed first
        major, minor, _ = platform.python_version_tuple()
        if int(major) == 3 and int(minor) >= 12:
            # Use newer numpy for Python 3.12+
            numpy_version = "numpy==1.26.0"
            scikit_version = "scikit-image==0.22.0"
        else:
            numpy_version = "numpy==1.24.2"
            scikit_version = "scikit-image==0.20.0"
            
        if not install_package(numpy_version, force_upgrade=True):
            print_warning(f"Could not install {numpy_version}, but continuing anyway")
        
        return install_package(scikit_version)
    except Exception as e:
        print_error(f"Failed to install scikit-image: {e}")
        return False

def create_virtual_env():
    """Create a virtual environment if not already in one"""
    print_step("Checking virtual environment")
    
    # Check if already in a virtual environment
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print_success("Already in a virtual environment")
        return True
    
    # Ask user if they want to create a virtual environment
    choice = input("Do you want to create a virtual environment? (y/n): ").strip().lower()
    if choice != 'y':
        print_warning("Continuing without a virtual environment")
        return True
    
    # Check if venv module is available
    try:
        import venv
        venv_dir = os.path.join(os.getcwd(), 'venv')
        
        # Create the virtual environment
        print(f"Creating virtual environment in {venv_dir}...")
        venv.create(venv_dir, with_pip=True)
        
        # Provide activation instructions
        print_success("Virtual environment created successfully!")
        print("\nTo activate the virtual environment:")
        
        if platform.system() == "Windows":
            print("    venv\\Scripts\\activate")
        else:
            print("    source venv/bin/activate")
        
        print("\nAfter activation, run this script again to install dependencies.")
        sys.exit(0)
    except ImportError:
        print_error("The venv module is not available. Please install Python 3.8+ with venv support.")
        return False
    except Exception as e:
        print_error(f"Error creating virtual environment: {e}")
        return False

def main():
    print("Mobile App Automation Tool - Installation")
    print("=========================================")
    
    # Check Python version
    if not check_python_version():
        return False
    
    # Check for virtual environment
    if not create_virtual_env():
        return False
    
    # Check pip
    if not check_pip():
        return False
    
    # Install system dependencies
    install_system_dependencies()
    
    # Install Python dependencies
    steps = [
        ("Basic dependencies", install_basic_dependencies),
        ("ADB interface", install_adb),
        ("OpenCV", install_opencv),
        ("SocketIO", install_socketio),
        ("Scikit-image", install_scikit_image)
    ]
    
    failed_steps = []
    for step_name, step_func in steps:
        if not step_func():
            failed_steps.append(step_name)
    
    # Final report
    print("\n" + "="*50)
    if failed_steps:
        print_error("Installation completed with errors")
        print("The following steps had issues:")
        for step in failed_steps:
            print(f"  - {step}")
        print("\nPlease try to resolve these issues and run the script again.")
        print("You can also try installing the problematic packages manually.")
        return False
    else:
        print_success("All dependencies installed successfully!")
        print("\nYou can now run the application with:")
        print("    python run.py")
        return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 