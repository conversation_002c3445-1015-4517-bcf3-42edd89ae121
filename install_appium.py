#!/usr/bin/env python3
"""
Appium Installation Script for Mobile Automation Tool

This script installs Appium server and required dependencies.
It will check for prerequisites like Node.js and npm, and install them if missing.
"""

import os
import sys
import platform
import subprocess
import shutil
import time

# Define colors for console output
class Colors:
    HEADER = '\033[95m'
    BLUE = '\033[94m'
    GREEN = '\033[92m'
    WARNING = '\033[93m'
    ERROR = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'

def print_header(text):
    """Print a formatted header"""
    print(f"\n{Colors.HEADER}{Colors.BOLD}=== {text} ==={Colors.ENDC}")

def print_step(text):
    """Print a step with blue color"""
    print(f"{Colors.BLUE}→ {text}{Colors.ENDC}")

def print_success(text):
    """Print a success message with green color"""
    print(f"{Colors.GREEN}✓ {text}{Colors.ENDC}")

def print_warning(text):
    """Print a warning message with yellow color"""
    print(f"{Colors.WARNING}⚠ {text}{Colors.ENDC}")

def print_error(text):
    """Print an error message with red color"""
    print(f"{Colors.ERROR}✗ {text}{Colors.ENDC}")

def run_command(command, shell=False):
    """Run a command and return the output"""
    try:
        result = subprocess.run(command, check=True, shell=shell, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        return result.stdout.strip()
    except subprocess.CalledProcessError as e:
        return None

def check_prerequisites():
    """Check for required prerequisites"""
    print_header("Checking Prerequisites")
    
    # Check for Node.js
    print_step("Checking for Node.js...")
    node_version = run_command(["node", "--version"])
    
    if node_version:
        print_success(f"Node.js is installed: {node_version}")
    else:
        print_warning("Node.js is not installed.")
        install_nodejs()
    
    # Check for npm after Node.js is installed
    print_step("Checking for npm...")
    npm_version = run_command(["npm", "--version"])
    
    if npm_version:
        print_success(f"npm is installed: {npm_version}")
    else:
        print_error("npm is not installed. Please install npm manually.")
        sys.exit(1)
    
    # Check Python packages
    print_step("Installing required Python packages...")
    install_python_packages()

def install_nodejs():
    """Install Node.js based on the operating system"""
    print_header("Installing Node.js")
    
    system = platform.system()
    
    if system == "Windows":
        print_step("Please install Node.js from https://nodejs.org/")
        print_step("After installation, re-run this script.")
        sys.exit(1)
    
    elif system == "Darwin":  # macOS
        # Check if Homebrew is installed
        brew_path = run_command(["which", "brew"])
        
        if brew_path:
            print_step("Installing Node.js using Homebrew...")
            run_command(["brew", "install", "node"])
        else:
            print_warning("Homebrew is not installed. Installing it first...")
            install_command = '/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"'
            run_command(install_command, shell=True)
            
            print_step("Installing Node.js using Homebrew...")
            run_command(["brew", "install", "node"])
    
    elif system == "Linux":
        # Using nvm for Linux
        print_step("Installing nvm (Node Version Manager)...")
        nvm_install = 'curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash'
        run_command(nvm_install, shell=True)
        
        # Source nvm
        source_command = 'export NVM_DIR="$HOME/.nvm" && [ -s "$NVM_DIR/nvm.sh" ] && . "$NVM_DIR/nvm.sh"'
        run_command(source_command, shell=True)
        
        print_step("Installing Node.js LTS version...")
        run_command(f'{source_command} && nvm install --lts', shell=True)
    
    # Verify installation
    node_version = run_command(["node", "--version"])
    if node_version:
        print_success(f"Node.js has been installed: {node_version}")
    else:
        print_error("Failed to install Node.js. Please install it manually.")
        sys.exit(1)

def install_python_packages():
    """Install required Python packages"""
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "pip"], check=True)
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], check=True)
        print_success("Python packages installed successfully")
    except subprocess.CalledProcessError as e:
        print_error(f"Failed to install Python packages: {str(e)}")
        sys.exit(1)

def install_appium():
    """Install Appium globally using npm"""
    print_header("Installing Appium")
    
    print_step("Installing Appium globally...")
    try:
        subprocess.run(["npm", "install", "-g", "appium"], check=True)
        print_success("Appium installed successfully")
    except subprocess.CalledProcessError as e:
        print_error(f"Failed to install Appium: {str(e)}")
        sys.exit(1)
    
    # Install Appium drivers
    print_step("Installing UiAutomator2 driver for Android...")
    try:
        subprocess.run(["appium", "driver", "install", "uiautomator2"], check=True)
        print_success("UiAutomator2 driver installed successfully")
    except subprocess.CalledProcessError as e:
        print_warning(f"Failed to install UiAutomator2 driver: {str(e)}")
    
    # Verify Appium installation
    print_step("Verifying Appium installation...")
    try:
        appium_version = run_command(["appium", "-v"])
        print_success(f"Appium is installed: {appium_version}")
    except Exception as e:
        print_error(f"Failed to verify Appium installation: {str(e)}")

def main():
    """Main installation function"""
    print_header("Appium Installation for Mobile Automation Tool")
    
    # Check and install prerequisites
    check_prerequisites()
    
    # Install Appium
    install_appium()
    
    print_header("Installation Complete")
    print_success("Appium has been successfully installed.")
    print_success("You can now run the Mobile Automation Tool with Appium support!")
    print("\nTo run the application:")
    print_step("python run.py")

if __name__ == "__main__":
    main() 