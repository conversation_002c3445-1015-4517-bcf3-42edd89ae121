# -*- coding: utf-8 -*-
"""
Minimal iOS Device Implementation for Airtest
This implementation uses direct requests to WebDriverAgent instead of wda module
"""

import os
import time
import requests
import json
import numpy as np
import cv2
import base64
from PIL import Image
from io import BytesIO
import datetime
from airtest.aircv import crop_image

from airtest.core.device import Device

class MinimalIOSDevice(Device):
    """Minimal iOS Device class that directly uses HTTP requests to WDA."""
    
    def __init__(self, device_id=None, wda_url="http://localhost:8100"):
        super(MinimalIOSDevice, self).__init__()
        self.device_id = device_id
        self.wda_url = wda_url
        self.device_info = {}
        self.display_info = {}
        self.session_id = None
        
        # Initialize connection
        self._init_connection()
        
    def _init_connection(self):
        """Initialize WebDriverAgent connection and get device info."""
        print(f"Connecting to WebDriverAgent at {self.wda_url}")
        try:
            # Get WDA status
            status_response = requests.get(f"{self.wda_url}/status")
            if status_response.status_code != 200:
                raise ConnectionError(f"Failed to connect to WDA: {status_response.status_code}")
                
            status_data = status_response.json()
            self.session_id = status_data.get("sessionId")
            
            value = status_data.get("value", {})
            ready = value.get("ready", False)
            
            if not ready:
                raise ConnectionError("WebDriverAgent not ready")
                
            # Store device info
            self.device_info = {
                "udid": self.device_id or status_data.get("sessionId", ""),
                "wda_status": value.get("message", ""),
                "ios_version": value.get("os", {}).get("version", ""),
                "device_type": value.get("device", ""),
                "ip": value.get("ios", {}).get("ip", "")
            }
            
            # Create new session if needed
            if not self.session_id:
                self._create_session()
                
            # Get screen size
            self._update_screen_info()
            
            print(f"Connected to iOS device: {self.device_info}")
            print(f"Screen size: {self.display_info['width']}x{self.display_info['height']}")
            
        except Exception as e:
            print(f"Error connecting to WebDriverAgent: {str(e)}")
            raise
            
    def _create_session(self):
        """Create a new session with WebDriverAgent."""
        try:
            capabilities = {
                "capabilities": {
                    "firstMatch": [{"bundleId": "com.apple.mobilesafari"}]
                }
            }
            
            response = requests.post(f"{self.wda_url}/session", json=capabilities)
            if response.status_code != 200:
                raise ConnectionError(f"Failed to create session: {response.status_code}")
                
            session_data = response.json()
            self.session_id = session_data.get("sessionId")
            print(f"Created new session: {self.session_id}")
            
        except Exception as e:
            print(f"Error creating session: {str(e)}")
            raise
    
    def _update_screen_info(self):
        """Get current screen size information."""
        try:
            response = requests.get(f"{self.wda_url}/window/size")
            if response.status_code != 200:
                raise ConnectionError(f"Failed to get window size: {response.status_code}")
                
            size_data = response.json().get("value", {})
            self.display_info = {
                "width": size_data.get("width", 0),
                "height": size_data.get("height", 0),
                "orientation": 0  # portrait by default
            }
        except Exception as e:
            print(f"Error getting screen size: {str(e)}")
            # Set default values if we can't get real ones
            self.display_info = {
                "width": 390,  # iPhone default
                "height": 844,
                "orientation": 0
            }
    
    def get_current_resolution(self):
        """Get current screen resolution."""
        return self.display_info["width"], self.display_info["height"]
        
    def get_render_resolution(self):
        """
        Get render resolution for current orientation.
        This is a required method for Airtest iOS API compatibility.
        
        Returns:
            tuple: (width, height) of the rendered screen
        """
        # For most iOS devices, render resolution is the same as screen resolution
        # but we could get it from WDA if available
        try:
            # First try to use the same values as current resolution 
            # since most implementations work this way
            return self.get_current_resolution()
        except Exception as e:
            print(f"Error getting render resolution: {e}")
            # Return a reasonable default if we can't get it
            return 1170, 2532  # Default for iPhone 14 Pro
        
    @property
    def touch_factor(self):
        """
        The scaling factor for touch coordinates based on device screen.
        This is a required property for Airtest iOS API compatibility.
        Documentation: https://airtest.readthedocs.io/en/latest/all_module/airtest.core.ios.ios.html#airtest.core.ios.ios.IOS.touch_factor
        
        Returns:
            float: Touch scaling factor (usually 1.0 for most devices)
        """
        # For most iOS devices with WDA, touch coordinates are already properly scaled
        # so we can use 1.0 as the default factor 
        return 1.0
        
    def home(self):
        """Press Home button."""
        print("Pressing Home button")
        response = requests.post(f"{self.wda_url}/wda/homescreen")
        return response.status_code == 200
        
    def snapshot(self, filename=None):
        """Take a screenshot."""
        try:
            response = requests.get(f"{self.wda_url}/screenshot")
            if response.status_code != 200:
                raise ConnectionError(f"Failed to take screenshot: {response.status_code}")
                
            # Get base64 encoded image data
            image_data = response.json().get("value")
            if not image_data:
                raise ValueError("No image data received")
                
            # Decode base64 data
            png_data = base64.b64decode(image_data)
            
            # Save or return image
            if filename:
                os.makedirs(os.path.dirname(filename) or '.', exist_ok=True)
                with open(filename, 'wb') as f:
                    f.write(png_data)
                return filename
            else:
                # Convert to numpy array for Airtest compatibility
                pil_image = Image.open(BytesIO(png_data))
                cv_image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
                return cv_image
                
        except Exception as e:
            print(f"Screenshot failed: {str(e)}")
            raise
            
    def touch(self, pos):
        """Perform touch action at given position."""
        x, y = pos
        print(f"Tapping at position: ({x}, {y})")
        
        tap_data = {
            "x": x,
            "y": y
        }
        
        response = requests.post(f"{self.wda_url}/wda/tap/0", json=tap_data)
        return response.status_code == 200
        
    def swipe(self, start_pos, end_pos, duration=0.5):
        """Perform swipe action from start position to end position."""
        from_x, from_y = start_pos
        to_x, to_y = end_pos
        
        print(f"Swiping from ({from_x}, {from_y}) to ({to_x}, {to_y})")
        
        swipe_data = {
            "fromX": from_x,
            "fromY": from_y,
            "toX": to_x,
            "toY": to_y,
            "duration": duration
        }
        
        response = requests.post(f"{self.wda_url}/wda/dragfromtoforduration", json=swipe_data)
        return response.status_code == 200
        
    def text(self, text):
        """Input text."""
        print(f"Inputting text: {text}")
        
        text_data = {
            "value": list(text)
        }
        
        response = requests.post(f"{self.wda_url}/wda/keys", json=text_data)
        return response.status_code == 200
        
    def start_app(self, bundle_id):
        """Start app by bundle id."""
        print(f"Starting app: {bundle_id}")
        
        app_data = {
            "bundleId": bundle_id
        }
        
        response = requests.post(f"{self.wda_url}/wda/apps/launch", json=app_data)
        time.sleep(1)  # Wait for app to start
        return response.status_code == 200
        
    def stop_app(self, bundle_id):
        """Stop app by bundle id."""
        print(f"Stopping app: {bundle_id}")
        
        app_data = {
            "bundleId": bundle_id
        }
        
        response = requests.post(f"{self.wda_url}/wda/apps/terminate", json=app_data)
        return response.status_code == 200
        
    def crop_and_save_screenshot(self, center_x, center_y, padding=20, filename=None):
        """
        Take a screenshot, crop a rectangle around specified coordinates with padding,
        and save it with a timestamp in the filename.
        
        Args:
            center_x (int): X coordinate of center point
            center_y (int): Y coordinate of center point
            padding (int): Padding to add around the center point (in pixels)
            filename (str, optional): Base filename, timestamp will be added
            
        Returns:
            str: Path to the saved cropped image
        """
        # Take a full screenshot
        print(f"Taking screenshot for cropping around ({center_x}, {center_y}) with {padding}px padding")
        screen = self.snapshot()
        
        # Calculate crop coordinates
        x1 = max(0, center_x - padding)
        y1 = max(0, center_y - padding)
        x2 = min(self.display_info["width"], center_x + padding)
        y2 = min(self.display_info["height"], center_y + padding)
        
        print(f"Cropping image at coordinates: ({x1}, {y1}, {x2}, {y2})")
        
        # Use airtest's crop_image function
        cropped_image = crop_image(screen, (x1, y1, x2, y2))
        
        # Generate timestamp for filename
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Create filename with timestamp
        if not filename:
            filename = f"cropped_image_{timestamp}.png"
        else:
            # Add timestamp before extension
            name, ext = os.path.splitext(filename)
            filename = f"{name}_{timestamp}{ext}"
        
        # Save to root project directory
        save_path = os.path.join(os.getcwd(), filename)
        
        print(f"Saving cropped image to: {save_path}")
        
        # Save cropped image
        cv2.imwrite(save_path, cropped_image)
        
        return save_path

# Rename to CustomIOSDevice for compatibility with the original test
CustomIOSDevice = MinimalIOSDevice

# Usage example:
# device = CustomIOSDevice("http://localhost:8100")
# device.snapshot("screenshot.png")
# device.home() 