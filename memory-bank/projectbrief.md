# Project Brief

<!-- 
This document is the foundation for the Memory Bank.
It defines the core requirements, goals, and overall scope of the project.
It serves as the source of truth for what the project aims to achieve. 
-->

## Core Mission
To provide a UI (web app) for automating mobile app testing across both iOS and Android platforms.

## Key Goals
- Enable easy interaction with mobile applications for testing purposes.
- Facilitate the acquisition of element locator values.
- Support identification of screen elements via images.
- Allow extraction of text from device screens.
- Build a robust automation framework.

## Scope
### In Scope
- Automated UI testing for iOS and Android mobile applications.
- A web-based user interface for managing and running tests.
- Tools for interacting with screen elements, including locator strategies, image recognition (leveraging OpenCV), and text extraction.
- Integration with Appium drivers.
- Development primarily in Python.

### Out of Scope 
<!-- (To be defined as needed) --> 