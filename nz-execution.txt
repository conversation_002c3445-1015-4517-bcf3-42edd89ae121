Action Log Export - 28/06/2025, 20:26:51
================================================================================

[20:25:22] [INFO] Refreshing device list...
[20:25:22] [INFO] Found 1 device(s)
[20:25:24] [INFO] Connecting to device: 00008120-00186C801E13C01E (Platform: iOS)...
[20:25:27] [INFO] Device info updated: 00008120-00186C801E13C01E
[20:25:27] [INFO] Connected to device: 00008120-00186C801E13C01E with AirTest support
[20:25:28] [INFO] Refreshing screenshot...
[20:25:28] [INFO] Screenshot refreshed
[20:25:29] [INFO] Screenshot refreshed successfully
[20:25:46] [INFO] Cleaning up screenshots...
[20:25:46] [INFO] All actions cleared
[20:25:46] [INFO] Added action: restartApp
[20:25:46] [INFO] Added action: tap
[20:25:46] [INFO] Added action: iosFunctions
[20:25:46] [INFO] Added action: waitTill
[20:25:46] [INFO] Added action: multiStep
[20:25:46] [INFO] Added action: tapOnText
[20:25:46] [INFO] Added action: iosFunctions
[20:25:46] [INFO] Added action: waitTill
[20:25:46] [INFO] Added action: tap
[20:25:46] [INFO] Added action: exists
[20:25:46] [INFO] Added action: tap
[20:25:46] [INFO] Added action: exists
[20:25:46] [INFO] Added action: tapOnText
[20:25:46] [INFO] Added action: swipe
[20:25:46] [INFO] Added action: tap
[20:25:46] [INFO] Added action: tap
[20:25:46] [INFO] Added action: tap
[20:25:46] [INFO] Added action: swipe
[20:25:46] [INFO] Added action: tap
[20:25:46] [INFO] Added action: tap
[20:25:46] [INFO] Added action: tapOnText
[20:25:46] [INFO] Added action: iosFunctions
[20:25:46] [INFO] Added action: waitTill
[20:25:46] [INFO] Added action: tap
[20:25:46] [INFO] Added action: tap
[20:25:46] [INFO] Added action: multiStep
[20:25:46] [INFO] Loaded test case "Delivery & CNC- NZ" with 26 actions
[20:25:46] [INFO] All screenshots deleted successfully
[20:25:58] [INFO] Skipping report initialization - single test case execution
[20:25:58] [INFO] Deleting all screenshots in app/static/screenshots folder...
[20:25:58] [INFO] All screenshots deleted successfully
[20:25:58] [INFO] Clearing screenshots from database before execution...
[20:25:58] [INFO] Cleared 1 screenshots from database
[20:25:58] [INFO] ExecutionManager: Starting execution of 26 actions...
[20:25:58] [INFO] Executing action 1/26: Restart app: env[appid]
[20:25:58] [INFO] HotUJOd6oB=running
[20:25:58] [INFO] Error executing action 1: Cannot access 'currentTestCaseContainer' before initialization
[20:25:58] [INFO] Executing action 2/26: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[20:25:58] [INFO] rkL0oz4kiL=running
[20:25:58] [INFO] Error executing action 2: Cannot access 'currentTestCaseContainer' before initialization
[20:25:58] [INFO] Executing action 3/26: iOS Function: alert_accept
[20:25:58] [INFO] yUJyVO5Wev=running
[20:25:58] [INFO] Error executing action 3: Cannot access 'currentTestCaseContainer' before initialization
[20:25:58] [INFO] Executing action 4/26: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[20:25:58] [INFO] 3caMBvQX7k=running
[20:25:58] [INFO] Error executing action 4: Cannot access 'currentTestCaseContainer' before initialization
[20:25:58] [INFO] Executing action 5/26: Execute Test Case: Kmart-NZ-Signin (6 steps)
[20:25:58] [INFO] SVt620PG1t=running
[20:25:58] [INFO] Loading steps for multiStep action: Kmart-NZ-Signin
[20:25:58] [INFO] Loaded 5 steps from test case: Kmart-NZ-Signin
[20:25:58] [INFO] Executing Multi Step action step 1/5: Wait till xpath=//XCUIElementTypeTextField[@name="Email"]
[20:25:58] [INFO] Error executing Multi Step action step 1: Cannot access 'currentTestCaseContainer' before initialization
[20:25:58] [INFO] Moving to the next test case after failure (server will handle retry)
[20:25:58] [INFO] Executing action 6/26: Tap on Text: "Find"
[20:25:58] [INFO] rqLJpAP0mA=running
[20:25:58] [INFO] Error executing action 6: Cannot access 'currentTestCaseContainer' before initialization
[20:25:58] [INFO] Executing action 7/26: iOS Function: text - Text: "P_43250042"
[20:25:58] [INFO] 13YG4jrM9E=running
[20:25:58] [INFO] Error executing action 7: Cannot access 'currentTestCaseContainer' before initialization
[20:25:58] [INFO] Executing action 8/26: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[20:25:58] [INFO] nAB6Q8LAdv=running
[20:25:58] [INFO] Error executing action 8: Cannot access 'currentTestCaseContainer' before initialization
[20:25:58] [INFO] Executing action 9/26: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::*[1]//XCUIElementTypeButton)[1]
[20:25:58] [INFO] FnrbyHq7bU=running
[20:25:58] [INFO] Error executing action 9: Cannot access 'currentTestCaseContainer' before initialization
[20:25:58] [INFO] Executing action 10/26: Check if element with xpath="//XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]" exists
[20:25:58] [INFO] jY0oPjKbuS=running
[20:25:58] [INFO] Error executing action 10: Cannot access 'currentTestCaseContainer' before initialization
[20:25:58] [INFO] Executing action 11/26: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[20:25:58] [INFO] F1olhgKhUt=running
[20:25:58] [INFO] Error executing action 11: Cannot access 'currentTestCaseContainer' before initialization
[20:25:58] [INFO] Executing action 12/26: Check if image "cnc-tab-se.png" exists on screen
[20:25:58] [INFO] uM5FOSrU5U=running
[20:25:58] [INFO] Error executing action 12: Cannot access 'currentTestCaseContainer' before initialization
[20:25:58] [INFO] Executing action 13/26: Tap on Text: "Collect"
[20:25:58] [INFO] qjj0i3rcUh=running
[20:25:58] [INFO] Error executing action 13: Cannot access 'currentTestCaseContainer' before initialization
[20:25:58] [INFO] Executing action 14/26: Swipe from (50%, 70%) to (50%, 30%)
[20:25:58] [INFO] Qbg9bipTGs=running
[20:25:58] [INFO] Error executing action 14: Cannot access 'currentTestCaseContainer' before initialization
[20:25:58] [INFO] Executing action 15/26: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Remove")]
[20:25:58] [INFO] pr9o8Zsm5p=running
[20:25:58] [INFO] Error executing action 15: Cannot access 'currentTestCaseContainer' before initialization
[20:25:58] [INFO] Executing action 16/26: Tap on image: banner-close-updated.png
[20:25:58] [INFO] 8umPSX0vrr=running
[20:25:58] [INFO] Error executing action 16: Cannot access 'currentTestCaseContainer' before initialization
[20:25:58] [INFO] Executing action 17/26: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 5 of 5")]
[20:25:58] [INFO] k3mu9Mt7Ec=running
[20:25:58] [INFO] Error executing action 17: Cannot access 'currentTestCaseContainer' before initialization
[20:25:58] [INFO] Executing action 18/26: Swipe from (50%, 70%) to (50%, 30%)
[20:25:58] [INFO] Ob26qqcA0p=running
[20:25:58] [INFO] Error executing action 18: Cannot access 'currentTestCaseContainer' before initialization
[20:25:58] [INFO] Executing action 19/26: Tap on element with xpath: //XCUIElementTypeButton[@name="txtSign out"]
[20:25:58] [INFO] OyUowAaBzD=running
[20:25:58] [INFO] Error executing action 19: Cannot access 'currentTestCaseContainer' before initialization
[20:25:58] [INFO] Executing action 20/26: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 1 of 5")]
[20:25:58] [INFO] cKNu2QoRC1=running
[20:25:58] [INFO] Error executing action 20: Cannot access 'currentTestCaseContainer' before initialization
[20:25:58] [INFO] Executing action 21/26: Tap on Text: "Find"
[20:25:58] [INFO] rqLJpAP0mA=running
[20:25:58] [INFO] Error executing action 21: Cannot access 'currentTestCaseContainer' before initialization
[20:25:58] [INFO] Executing action 22/26: iOS Function: text - Text: "P_43250042"
[20:25:58] [INFO] 13YG4jrM9E=running
[20:25:58] [INFO] Error executing action 22: Cannot access 'currentTestCaseContainer' before initialization
[20:25:58] [INFO] Executing action 23/26: Wait till xpath=//XCUIElementTypeButton[@name="Filter"]
[20:25:58] [INFO] nAB6Q8LAdv=running
[20:25:58] [INFO] Error executing action 23: Cannot access 'currentTestCaseContainer' before initialization
[20:25:58] [INFO] Executing action 24/26: Tap on element with xpath: (//XCUIElementTypeOther[@name="Sort by: Relevance"]/following-sibling::*[1]//XCUIElementTypeButton)[1]
[20:25:58] [INFO] FnrbyHq7bU=running
[20:25:58] [INFO] Error executing action 24: Cannot access 'currentTestCaseContainer' before initialization
[20:25:58] [INFO] Executing action 25/26: Tap on element with xpath: //XCUIElementTypeButton[contains(@name,"Tab 4 of 5")]
[20:25:58] [INFO] jY0oPjKbuS=running
[20:25:58] [INFO] Error executing action 25: Cannot access 'currentTestCaseContainer' before initialization
[20:25:58] [INFO] Executing action 26/26: Execute Test Case: Delivery Buy Step NZ (33 steps)
[20:25:58] [INFO] MuX1dfl3aB=running
[20:25:58] [INFO] Loading steps for multiStep action: Delivery Buy Step NZ
[20:25:58] [INFO] Loaded 33 steps from test case: Delivery Buy Step NZ
[20:25:58] [INFO] Executing Multi Step action step 1/33: Wait till xpath=//XCUIElementTypeButton[@name="Delivery"]
[20:25:58] [INFO] Error executing Multi Step action step 1: Cannot access 'currentTestCaseContainer' before initialization
[20:25:58] [INFO] Moving to the next test case after failure (server will handle retry)
[20:25:58] [INFO] All tests passed successfully!
[20:25:58] [INFO] Generating execution report...
[20:25:58] [INFO] Saving 132 action log entries to file...
[20:25:58] [INFO] Execution failed but report was generated.
[20:25:58] [INFO] Action logs saved successfully
[20:25:58] [INFO] Skipping report generation - individual test case execution (not from test suite)
