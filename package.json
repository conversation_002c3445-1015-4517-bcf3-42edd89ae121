{"name": "mobile-app-autotest", "version": "1.0.0", "description": "Mobile automation testing tool", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:main": "jest tests/unit/main.test.js", "test:device": "jest tests/unit/modules/device.test.js", "test:actions": "jest tests/unit/modules/actions.test.js", "test:integration": "jest tests/integration", "appium:install": "appium driver install uiautomator2 && appium driver install xcuitest && appium plugin install --source=npm appium-inspector-plugin", "appium:start": "appium --base-path /wd/hub --relaxed-security --use-plugins images,inspector --use-drivers xcuitest,uiautomator2 --no-reset --session-override", "setup": "npm install && npm run appium:install", "start:all": "npm run appium:start & python run.py", "allure:generate": "node_modules/.bin/allure generate reports/allure-results -o reports/allure-report --clean", "allure:serve": "node_modules/.bin/allure serve reports/allure-results"}, "dependencies": {"@mobilenext/mobile-mcp": "^0.0.13", "appium": "^2.17.1", "express": "^4.17.1"}, "devDependencies": {"allure-commandline": "^2.34.0", "appium-inspector-plugin": "^2025.3.1", "appium-uiautomator2-driver": "^4.2.1", "appium-xcuitest-driver": "^9.2.0", "jest": "^29.5.0", "jest-environment-jsdom": "^29.5.0", "nodemon": "^2.0.13"}, "jest": {"testEnvironment": "jsdom", "verbose": true, "collectCoverageFrom": ["app/static/js/**/*.js"], "setupFiles": ["./tests/jest.setup.js"]}}