/**
 * Platform UI Enhancements for Multi-Platform Device Support
 * Enhances the UI to clearly show device platform information (iOS/Android)
 */

// Platform-specific styling and icons
const PLATFORM_CONFIG = {
    'iOS': {
        icon: 'bi-apple',
        color: '#007AFF',
        bgColor: '#E3F2FD',
        name: 'iOS'
    },
    'Android': {
        icon: 'bi-android2',
        color: '#3DDC84',
        bgColor: '#E8F5E8',
        name: 'Android'
    }
};

/**
 * Enhance device selector options with platform indicators
 */
function enhanceDeviceSelector() {
    const deviceSelect = document.getElementById('device-selector');
    if (!deviceSelect) return;

    // Add custom styling for platform indicators
    const style = document.createElement('style');
    style.textContent = `
        .device-option-enhanced {
            display: flex;
            align-items: center;
            padding: 8px 12px;
        }
        .platform-badge {
            display: inline-flex;
            align-items: center;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 500;
            margin-right: 8px;
            min-width: 60px;
            justify-content: center;
        }
        .platform-badge i {
            margin-right: 4px;
        }
        .device-info {
            flex: 1;
        }
        .device-platform-ios {
            background-color: #E3F2FD;
            color: #007AFF;
        }
        .device-platform-android {
            background-color: #E8F5E8;
            color: #3DDC84;
        }
        .device-selector-enhanced {
            min-height: 45px;
        }
        .platform-indicator {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 0.8rem;
            padding: 2px 6px;
            border-radius: 8px;
        }
    `;
    document.head.appendChild(style);

    // Override the device refresh function to add platform indicators
    if (window.app && window.app.refreshDevices) {
        const originalRefreshDevices = window.app.refreshDevices.bind(window.app);
        
        window.app.refreshDevices = async function() {
            await originalRefreshDevices();
            enhanceDeviceOptions();
        };
    }
}

/**
 * Enhance device options with platform badges
 */
function enhanceDeviceOptions() {
    const deviceSelect = document.getElementById('device-selector');
    if (!deviceSelect) return;

    Array.from(deviceSelect.options).forEach(option => {
        if (option.value && option.dataset.platform) {
            const platform = option.dataset.platform;
            const config = PLATFORM_CONFIG[platform];
            
            if (config) {
                // Create enhanced option text with platform badge
                const platformBadge = `[${config.name}]`;
                const deviceInfo = option.textContent.replace(/^\[.*?\]\s*/, ''); // Remove existing badge if any
                option.textContent = `${platformBadge} ${deviceInfo}`;
                
                // Add platform-specific styling
                option.style.backgroundColor = config.bgColor;
                option.style.color = config.color;
                option.style.fontWeight = '500';
            }
        }
    });
}

/**
 * Add platform indicator to device connection status
 */
function enhanceDeviceConnectionStatus() {
    const deviceConnectionCard = document.querySelector('.card-header h5');
    if (!deviceConnectionCard) return;

    // Create platform status indicator
    const platformIndicator = document.createElement('span');
    platformIndicator.id = 'platform-indicator';
    platformIndicator.className = 'badge ms-2';
    platformIndicator.style.display = 'none';
    
    deviceConnectionCard.appendChild(platformIndicator);

    // Update platform indicator when device is selected
    const deviceSelect = document.getElementById('device-selector');
    if (deviceSelect) {
        deviceSelect.addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            const platform = selectedOption.dataset.platform;
            
            if (platform && PLATFORM_CONFIG[platform]) {
                const config = PLATFORM_CONFIG[platform];
                platformIndicator.innerHTML = `<i class="${config.icon}"></i> ${config.name}`;
                platformIndicator.style.backgroundColor = config.color;
                platformIndicator.style.display = 'inline-flex';
                platformIndicator.style.alignItems = 'center';
            } else {
                platformIndicator.style.display = 'none';
            }
        });
    }
}

/**
 * Add platform information to action type selector
 */
function enhanceActionTypeSelector() {
    const actionTypeSelect = document.getElementById('actionType');
    if (!actionTypeSelect) return;

    // Add platform-specific labels to action options
    Array.from(actionTypeSelect.options).forEach(option => {
        const value = option.value;
        const text = option.textContent;
        
        // Add platform indicators to platform-specific actions
        if (text.includes('(iOS)') || value === 'iosFunctions') {
            option.textContent = `🍎 ${text}`;
            option.style.color = PLATFORM_CONFIG.iOS.color;
        } else if (text.includes('(Android)') || value === 'androidFunctions') {
            option.textContent = `🤖 ${text}`;
            option.style.color = PLATFORM_CONFIG.Android.color;
        }
    });
}

/**
 * Add platform routing information display
 */
function addPlatformRoutingInfo() {
    const deviceConnectionCard = document.querySelector('.card-body');
    if (!deviceConnectionCard) return;

    // Create platform routing info panel
    const routingInfo = document.createElement('div');
    routingInfo.id = 'platform-routing-info';
    routingInfo.className = 'alert alert-info mt-2';
    routingInfo.style.display = 'none';
    routingInfo.innerHTML = `
        <div class="d-flex align-items-center">
            <i class="bi bi-info-circle me-2"></i>
            <div>
                <strong>Platform Routing:</strong>
                <span id="routing-details"></span>
            </div>
        </div>
    `;
    
    deviceConnectionCard.appendChild(routingInfo);

    // Update routing info when device is selected
    const deviceSelect = document.getElementById('device-selector');
    if (deviceSelect) {
        deviceSelect.addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            const platform = selectedOption.dataset.platform;
            const routingDetails = document.getElementById('routing-details');
            
            if (platform) {
                const port = platform === 'iOS' ? '8080' : '8081';
                const entryPoint = platform === 'iOS' ? 'run.py' : 'run_android.py';
                
                routingDetails.innerHTML = `
                    This ${platform} device will connect to the ${platform} backend 
                    (Port: ${port}, Entry: ${entryPoint})
                `;
                routingInfo.style.display = 'block';
            } else {
                routingInfo.style.display = 'none';
            }
        });
    }
}

/**
 * Add platform health status indicators
 */
function addPlatformHealthStatus() {
    // Create platform health status panel
    const healthPanel = document.createElement('div');
    healthPanel.className = 'card mb-3';
    healthPanel.innerHTML = `
        <div class="card-header">
            <h6 class="mb-0">Platform Status</h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-6">
                    <div class="d-flex align-items-center">
                        <i class="bi bi-apple text-primary me-2"></i>
                        <span>iOS Backend</span>
                        <span id="ios-status" class="badge bg-secondary ms-auto">Unknown</span>
                    </div>
                </div>
                <div class="col-6">
                    <div class="d-flex align-items-center">
                        <i class="bi bi-android2 text-success me-2"></i>
                        <span>Android Backend</span>
                        <span id="android-status" class="badge bg-secondary ms-auto">Unknown</span>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Insert after device connection card
    const deviceCard = document.querySelector('.card');
    if (deviceCard && deviceCard.nextSibling) {
        deviceCard.parentNode.insertBefore(healthPanel, deviceCard.nextSibling);
    }

    // Check platform health periodically
    checkPlatformHealth();
    setInterval(checkPlatformHealth, 30000); // Check every 30 seconds
}

/**
 * Check health of both platform backends
 */
async function checkPlatformHealth() {
    const iosStatus = document.getElementById('ios-status');
    const androidStatus = document.getElementById('android-status');
    
    if (!iosStatus || !androidStatus) return;

    // Check iOS backend (port 8080)
    try {
        const iosResponse = await fetch('http://localhost:8080/api/health', { 
            method: 'GET',
            timeout: 5000 
        });
        
        if (iosResponse.ok) {
            iosStatus.textContent = 'Online';
            iosStatus.className = 'badge bg-success ms-auto';
        } else {
            iosStatus.textContent = 'Error';
            iosStatus.className = 'badge bg-warning ms-auto';
        }
    } catch (error) {
        iosStatus.textContent = 'Offline';
        iosStatus.className = 'badge bg-danger ms-auto';
    }

    // Check Android backend (port 8081)
    try {
        const androidResponse = await fetch('http://localhost:8081/api/health', { 
            method: 'GET',
            timeout: 5000 
        });
        
        if (androidResponse.ok) {
            androidStatus.textContent = 'Online';
            androidStatus.className = 'badge bg-success ms-auto';
        } else {
            androidStatus.textContent = 'Error';
            androidStatus.className = 'badge bg-warning ms-auto';
        }
    } catch (error) {
        androidStatus.textContent = 'Offline';
        androidStatus.className = 'badge bg-danger ms-auto';
    }
}

/**
 * Initialize all platform UI enhancements
 */
function initializePlatformUI() {
    console.log('Initializing platform UI enhancements...');
    
    enhanceDeviceSelector();
    enhanceDeviceConnectionStatus();
    enhanceActionTypeSelector();
    addPlatformRoutingInfo();
    addPlatformHealthStatus();
    
    // Enhance existing devices if already loaded
    setTimeout(enhanceDeviceOptions, 1000);
    
    console.log('Platform UI enhancements initialized');
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializePlatformUI);
} else {
    initializePlatformUI();
}
