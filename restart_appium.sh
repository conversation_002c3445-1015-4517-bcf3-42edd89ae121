#!/bin/bash

# Exit on error
set -e

echo "===== Restarting Appium server ====="

# Kill any existing Appium processes
echo "Stopping all Appium processes..."
pkill -f appium || true
sleep 2

# Check if any Appium processes are still running
if pgrep -f appium > /dev/null; then
  echo "Force killing remaining Appium processes..."
  pkill -9 -f appium || true
  sleep 1
fi

# Clear port 4723 if it's in use
PORT=4723
if lsof -i :$PORT > /dev/null; then
  echo "Port $PORT is still in use, attempting to free it..."
  lsof -ti :$PORT | xargs kill -9 || true
  sleep 1
fi

# Use the local Appium instance if available
LOCAL_APPIUM="./node_modules/.bin/appium"
if [ -f "$LOCAL_APPIUM" ]; then
  APPIUM_CMD="$LOCAL_APPIUM"
  echo "Using local Appium installation: $APPIUM_CMD"
else
  APPIUM_CMD="appium"
  echo "Using global Appium installation: $APPIUM_CMD"
fi

# Check if inspector plugin is installed for this Appium instance
echo "Checking for inspector plugin..."
INSPECTOR_CHECK=$($APPIUM_CMD plugin list --installed)
echo "$INSPECTOR_CHECK"

# Start Appium with more verbose logging and correct parameters
echo "Starting Appium server..."
$APPIUM_CMD --log-level debug --relaxed-security --base-path /wd/hub --use-plugins=inspector --allow-cors --allow-insecure chromedriver_autodownload > appium.log 2>&1 &

# Wait for Appium to start
echo "Waiting for Appium to start..."
sleep 5

# Check if Appium is running
if curl -s http://localhost:4723/wd/hub/status > /dev/null; then
  echo "✅ Appium server started successfully"
  echo "To view logs: tail -f appium.log"
else
  echo "❌ Appium server failed to start. Check appium.log for details."
  tail -10 appium.log
fi

echo "===== Appium restart process completed ====="