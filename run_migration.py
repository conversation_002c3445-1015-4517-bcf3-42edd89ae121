#!/usr/bin/env python3
"""
Mobile App Automation Tool - Complete Migration Runner

This script runs the complete unique ID migration process including:
1. Data migration (unique_id_migration.py)
2. Code updates (update_tracking_system.py)
3. Validation testing (test_unique_id_system.py)

Usage:
    python run_migration.py [--platform ios|android|both] [--dry-run] [--skip-tests]
"""

import os
import sys
import subprocess
import argparse
import logging
from datetime import datetime

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'complete_migration_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class CompleteMigrationRunner:
    """Runs the complete migration process"""
    
    def __init__(self, platform='both', dry_run=False, skip_tests=False):
        self.platform = platform
        self.dry_run = dry_run
        self.skip_tests = skip_tests
        
    def run_script(self, script_name, args=None):
        """Run a Python script with arguments"""
        cmd = [sys.executable, script_name]
        if args:
            cmd.extend(args)
        
        logger.info(f"Running: {' '.join(cmd)}")
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            logger.info(f"Script {script_name} completed successfully")
            if result.stdout:
                logger.info(f"Output: {result.stdout}")
            return True
        except subprocess.CalledProcessError as e:
            logger.error(f"Script {script_name} failed with return code {e.returncode}")
            if e.stdout:
                logger.error(f"Stdout: {e.stdout}")
            if e.stderr:
                logger.error(f"Stderr: {e.stderr}")
            return False
        except Exception as e:
            logger.error(f"Error running script {script_name}: {e}")
            return False
    
    def check_prerequisites(self):
        """Check that all required scripts exist"""
        required_scripts = [
            'unique_id_migration.py',
            'update_tracking_system.py',
            'test_unique_id_system.py'
        ]
        
        missing_scripts = []
        for script in required_scripts:
            if not os.path.exists(script):
                missing_scripts.append(script)
        
        if missing_scripts:
            logger.error(f"Missing required scripts: {', '.join(missing_scripts)}")
            return False
        
        logger.info("All required scripts found")
        return True
    
    def run_data_migration(self):
        """Run the unique ID data migration"""
        logger.info("\n=== STEP 1: DATA MIGRATION ===")
        
        args = ['--platform', self.platform]
        if self.dry_run:
            args.append('--dry-run')
        
        return self.run_script('unique_id_migration.py', args)
    
    def run_code_updates(self):
        """Run the tracking system code updates"""
        logger.info("\n=== STEP 2: CODE UPDATES ===")
        
        args = ['--platform', self.platform]
        if self.dry_run:
            args.append('--dry-run')
        
        return self.run_script('update_tracking_system.py', args)
    
    def run_validation_tests(self):
        """Run the validation tests"""
        if self.skip_tests:
            logger.info("\n=== STEP 3: VALIDATION TESTS (SKIPPED) ===")
            return True
        
        logger.info("\n=== STEP 3: VALIDATION TESTS ===")
        
        args = ['--platform', self.platform]
        
        return self.run_script('test_unique_id_system.py', args)
    
    def run_complete_migration(self):
        """Run the complete migration process"""
        logger.info("=" * 60)
        logger.info("MOBILE APP AUTOMATION TOOL - UNIQUE ID MIGRATION")
        logger.info("=" * 60)
        logger.info(f"Platform: {self.platform}")
        logger.info(f"Dry run: {self.dry_run}")
        logger.info(f"Skip tests: {self.skip_tests}")
        logger.info(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Check prerequisites
        if not self.check_prerequisites():
            logger.error("Prerequisites check failed")
            return False
        
        # Step 1: Data migration
        if not self.run_data_migration():
            logger.error("Data migration failed")
            return False
        
        # Step 2: Code updates (skip if dry run)
        if not self.dry_run:
            if not self.run_code_updates():
                logger.error("Code updates failed")
                return False
        else:
            logger.info("\n=== STEP 2: CODE UPDATES (SKIPPED - DRY RUN) ===")
        
        # Step 3: Validation tests (skip if dry run)
        if not self.dry_run:
            if not self.run_validation_tests():
                logger.error("Validation tests failed")
                return False
        else:
            logger.info("\n=== STEP 3: VALIDATION TESTS (SKIPPED - DRY RUN) ===")
        
        # Success
        logger.info("\n" + "=" * 60)
        if self.dry_run:
            logger.info("✅ DRY RUN COMPLETED SUCCESSFULLY")
            logger.info("No changes were made. Review the logs and run without --dry-run to apply changes.")
        else:
            logger.info("✅ COMPLETE MIGRATION COMPLETED SUCCESSFULLY")
            logger.info("The unique ID system has been successfully implemented!")
        logger.info("=" * 60)
        
        return True


def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(
        description='Mobile App Automation Tool - Complete Migration Runner',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Dry run to see what would be changed
  python run_migration.py --dry-run
  
  # Run complete migration for both platforms
  python run_migration.py --platform both
  
  # Run migration for iOS only
  python run_migration.py --platform ios
  
  # Run migration without validation tests
  python run_migration.py --skip-tests
        """
    )
    
    parser.add_argument(
        '--platform',
        choices=['ios', 'android', 'both'],
        default='both',
        help='Platform to migrate (default: both)'
    )
    
    parser.add_argument(
        '--dry-run',
        action='store_true',
        help='Perform a dry run without making changes'
    )
    
    parser.add_argument(
        '--skip-tests',
        action='store_true',
        help='Skip validation tests'
    )
    
    parser.add_argument(
        '--verbose',
        action='store_true',
        help='Enable verbose logging'
    )
    
    args = parser.parse_args()
    
    # Set logging level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Confirm if not dry run
    if not args.dry_run:
        print("\n⚠️  WARNING: This will modify your database and files!")
        print("Make sure you have backups of your important data.")
        print("The script will create automatic backups, but manual backups are recommended.")
        
        response = input("\nDo you want to continue? (yes/no): ").lower().strip()
        if response not in ['yes', 'y']:
            print("Migration cancelled by user.")
            return 0
    
    # Run migration
    runner = CompleteMigrationRunner(
        platform=args.platform,
        dry_run=args.dry_run,
        skip_tests=args.skip_tests
    )
    
    if runner.run_complete_migration():
        return 0
    else:
        logger.error("Migration failed. Check the logs for details.")
        return 1


if __name__ == '__main__':
    sys.exit(main())
