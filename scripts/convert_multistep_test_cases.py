#!/usr/bin/env python3
"""
Script to convert all test cases with multistep actions to the new format.
The new format embeds the test case steps directly in the multistep action.
"""

import os
import json
import sys
import logging
from datetime import datetime

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger('convert_multistep')

# Get the project root directory
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
test_cases_dir = os.path.join(project_root, 'test_cases')

def load_test_case(filename):
    """Load a test case from a file"""
    try:
        file_path = os.path.join(test_cases_dir, filename)
        if not os.path.exists(file_path):
            logger.error(f"Test case file not found: {filename}")
            return None

        with open(file_path, 'r') as f:
            test_case_data = json.load(f)
        
        return test_case_data
    except Exception as e:
        logger.error(f"Error loading test case {filename}: {str(e)}")
        return None

def save_test_case(test_case_data, filename):
    """Save a test case to a file"""
    try:
        file_path = os.path.join(test_cases_dir, filename)
        
        # Create a backup of the original file
        if os.path.exists(file_path):
            backup_path = f"{file_path}.bak"
            try:
                import shutil
                shutil.copy2(file_path, backup_path)
                logger.info(f"Created backup: {backup_path}")
            except Exception as e:
                logger.error(f"Error creating backup for {filename}: {str(e)}")
        
        # Save the updated test case
        with open(file_path, 'w') as f:
            json.dump(test_case_data, f, indent=2)
        
        logger.info(f"Saved updated test case: {filename}")
        return True
    except Exception as e:
        logger.error(f"Error saving test case {filename}: {str(e)}")
        return False

def convert_test_case(filename):
    """Convert a test case to the new format"""
    logger.info(f"Converting test case: {filename}")
    
    # Load the test case
    test_case = load_test_case(filename)
    if not test_case:
        return False
    
    # Check if the test case has any multistep actions
    actions = test_case.get('actions', [])
    has_multistep = False
    modified = False
    
    for i, action in enumerate(actions):
        if action.get('type') == 'multiStep':
            has_multistep = True
            
            # Check if the action already has embedded steps
            if 'test_case_steps' in action and action.get('steps_loaded', False):
                logger.info(f"Action {i+1} already has embedded steps")
                continue
            
            # Get the referenced test case ID
            test_case_id = action.get('test_case_id')
            if not test_case_id:
                logger.warning(f"Action {i+1} has no test_case_id")
                continue
            
            # Load the referenced test case
            referenced_test_case = load_test_case(test_case_id)
            if not referenced_test_case:
                logger.warning(f"Referenced test case not found: {test_case_id}")
                continue
            
            # Get the steps from the referenced test case
            referenced_steps = referenced_test_case.get('actions', [])
            if not referenced_steps:
                logger.warning(f"Referenced test case has no actions: {test_case_id}")
                continue
            
            # Embed the steps in the multistep action
            action['test_case_steps'] = referenced_steps
            action['steps_loaded'] = True
            logger.info(f"Embedded {len(referenced_steps)} steps from {test_case_id} into action {i+1}")
            modified = True
    
    # If the test case was modified, save it
    if modified:
        # Update the 'updated' timestamp
        test_case['updated'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        # Save the updated test case
        if save_test_case(test_case, filename):
            logger.info(f"Successfully converted test case: {filename}")
            return True
        else:
            logger.error(f"Failed to save converted test case: {filename}")
            return False
    elif has_multistep:
        logger.info(f"Test case has multistep actions but no conversion needed: {filename}")
        return True
    else:
        logger.info(f"Test case has no multistep actions: {filename}")
        return True

def main():
    """Main function"""
    logger.info("Starting conversion of multistep test cases")
    
    # Get all test case files
    test_case_files = [f for f in os.listdir(test_cases_dir) if f.endswith('.json')]
    logger.info(f"Found {len(test_case_files)} test case files")
    
    # Convert each test case
    success_count = 0
    failure_count = 0
    skipped_count = 0
    
    for filename in test_case_files:
        try:
            if convert_test_case(filename):
                success_count += 1
            else:
                failure_count += 1
        except Exception as e:
            logger.error(f"Error converting test case {filename}: {str(e)}")
            failure_count += 1
    
    logger.info(f"Conversion complete: {success_count} succeeded, {failure_count} failed, {skipped_count} skipped")

if __name__ == "__main__":
    main()
