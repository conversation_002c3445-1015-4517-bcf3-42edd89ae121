"""
Session router for directing requests to appropriate platform backends
"""
import requests
import logging
from unified_device_discovery import get_device_platform

logger = logging.getLogger(__name__)

class SessionRouter:
    """Routes session requests to appropriate platform backends"""
    
    def __init__(self):
        self.backends = {
            'iOS': {
                'port': 8080,
                'url': 'http://localhost:8080',
                'name': 'iOS Backend'
            },
            'Android': {
                'port': 8081,
                'url': 'http://localhost:8081', 
                'name': 'Android Backend'
            }
        }
    
    def get_backend_for_device(self, device_id):
        """
        Get the appropriate backend for a device
        
        Args:
            device_id (str): Device ID
            
        Returns:
            dict: Backend configuration or None if device not found
        """
        platform = get_device_platform(device_id)
        if platform and platform in self.backends:
            return self.backends[platform]
        return None
    
    def route_request(self, device_id, endpoint, method='GET', data=None, headers=None):
        """
        Route a request to the appropriate backend
        
        Args:
            device_id (str): Device ID to determine routing
            endpoint (str): API endpoint to call
            method (str): HTTP method
            data (dict): Request data
            headers (dict): Request headers
            
        Returns:
            dict: Response from backend or error
        """
        backend = self.get_backend_for_device(device_id)
        if not backend:
            return {
                'error': f'No backend found for device {device_id}',
                'status_code': 404
            }
        
        url = f"{backend['url']}{endpoint}"
        
        try:
            logger.info(f"Routing {method} request to {backend['name']}: {url}")
            
            if method.upper() == 'GET':
                response = requests.get(url, params=data, headers=headers, timeout=30)
            elif method.upper() == 'POST':
                response = requests.post(url, json=data, headers=headers, timeout=30)
            elif method.upper() == 'PUT':
                response = requests.put(url, json=data, headers=headers, timeout=30)
            elif method.upper() == 'DELETE':
                response = requests.delete(url, headers=headers, timeout=30)
            else:
                return {
                    'error': f'Unsupported HTTP method: {method}',
                    'status_code': 400
                }
            
            return {
                'status_code': response.status_code,
                'data': response.json() if response.content else {},
                'backend': backend['name']
            }
            
        except requests.exceptions.ConnectionError:
            return {
                'error': f'Could not connect to {backend["name"]} at {backend["url"]}',
                'status_code': 503,
                'suggestion': f'Make sure the {backend["name"]} is running on port {backend["port"]}'
            }
        except requests.exceptions.Timeout:
            return {
                'error': f'Request to {backend["name"]} timed out',
                'status_code': 504
            }
        except Exception as e:
            return {
                'error': f'Error routing request to {backend["name"]}: {str(e)}',
                'status_code': 500
            }
    
    def check_backend_health(self, platform):
        """
        Check if a backend is healthy
        
        Args:
            platform (str): Platform name ('iOS' or 'Android')
            
        Returns:
            dict: Health status
        """
        if platform not in self.backends:
            return {'healthy': False, 'error': 'Unknown platform'}
        
        backend = self.backends[platform]
        
        try:
            response = requests.get(f"{backend['url']}/api/health", timeout=5)
            return {
                'healthy': response.status_code == 200,
                'status_code': response.status_code,
                'backend': backend['name']
            }
        except Exception as e:
            return {
                'healthy': False,
                'error': str(e),
                'backend': backend['name']
            }
    
    def get_all_backend_health(self):
        """
        Check health of all backends
        
        Returns:
            dict: Health status for all backends
        """
        health_status = {}
        for platform in self.backends:
            health_status[platform] = self.check_backend_health(platform)
        return health_status
    
    def start_session(self, device_id, session_config=None):
        """
        Start a session on the appropriate backend
        
        Args:
            device_id (str): Device ID
            session_config (dict): Session configuration
            
        Returns:
            dict: Session start response
        """
        platform = get_device_platform(device_id)
        if not platform:
            return {
                'error': f'Device {device_id} not found',
                'status_code': 404
            }
        
        # Add device_type to session config
        if session_config is None:
            session_config = {}
        session_config['device_id'] = device_id
        session_config['device_type'] = platform
        
        return self.route_request(
            device_id=device_id,
            endpoint='/api/connect',
            method='POST',
            data=session_config
        )
    
    def stop_session(self, device_id):
        """
        Stop a session on the appropriate backend
        
        Args:
            device_id (str): Device ID
            
        Returns:
            dict: Session stop response
        """
        return self.route_request(
            device_id=device_id,
            endpoint='/api/disconnect',
            method='POST',
            data={'device_id': device_id}
        )
    
    def execute_action(self, device_id, action_data):
        """
        Execute an action on the appropriate backend
        
        Args:
            device_id (str): Device ID
            action_data (dict): Action configuration
            
        Returns:
            dict: Action execution response
        """
        return self.route_request(
            device_id=device_id,
            endpoint='/api/execute',
            method='POST',
            data=action_data
        )
    
    def get_screenshot(self, device_id):
        """
        Get a screenshot from the appropriate backend
        
        Args:
            device_id (str): Device ID
            
        Returns:
            dict: Screenshot response
        """
        return self.route_request(
            device_id=device_id,
            endpoint='/api/screenshot',
            method='GET'
        )

# Global session router instance
session_router = SessionRouter()
