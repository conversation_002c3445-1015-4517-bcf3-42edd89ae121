# Text Detection Tool

This tool detects text in images and returns the coordinates of the text regions.

## Features

- Detect text in images using two methods:
  - Basic method: Uses OpenCV contour detection and Pytesseract OCR
  - EAST method: Uses the EAST text detector model with Pytesseract OCR
- Returns coordinates of detected text regions
- Generates annotated images with bounding boxes around text

## Requirements

- Python 3.6+
- OpenCV
- Pytesseract
- NumPy
- PIL (Pillow)

## Usage

### Basic Method

```bash
python text_detector.py path/to/your/image.jpg --method basic --output_dir output
```

### EAST Method (More accurate but requires model download)

First, download the EAST model:

```bash
python download_east_model.py
```

Then run the detection:

```bash
python text_detector.py path/to/your/image.jpg --method east --output_dir output
```

## Output

The script will:
1. Print the detected text and its coordinates to the console
2. Save an annotated image with bounding boxes to the specified output directory

## Example Output

```
Detected Text Regions:

Region 1:
  Text: Hello World
  Coordinates: (x1=100, y1=200, x2=300, y2=250)
  Center: (200, 225)
  Dimensions: 200x50
  Confidence: 0.95

Total regions detected: 1
```

## Notes

- The EAST method generally provides better accuracy but requires downloading the model.
- Adjust the `min_confidence` parameter to filter out low-confidence detections.
- For better OCR results, ensure the image has good lighting and contrast.
