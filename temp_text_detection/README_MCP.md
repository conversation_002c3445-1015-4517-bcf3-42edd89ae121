# Mobile Control Protocol (MCP) Server

This directory contains a Mobile-Next compatible MCP (Mobile Control Protocol) server implementation for mobile device automation. The server provides standardized endpoints for controlling mobile devices and automating UI interactions.

## Overview

The MCP server acts as a bridge between AI systems and mobile devices, allowing for programmatic control of mobile applications. It provides a RESTful API that can be used to perform various actions on mobile devices, such as:

- Listing installed applications
- Launching applications
- Tapping on screen coordinates
- Swiping between points
- Inputting text
- Finding UI elements
- Taking screenshots
- Finding and interacting with images on screen

## Requirements

- Python 3.7+
- Flask
- Appium (for device control)
- AirTest (optional, for enhanced image recognition)

## Installation

The MCP server is designed to work with the existing Mobile App Automation Tool. It leverages the `AppiumDeviceController` class for device interaction.

No additional installation is required if you already have the Mobile App Automation Tool set up. If not, make sure to install the required dependencies:

```bash
pip install flask requests
```

## Usage

### Starting the MCP Server

To start the MCP server, run:

```bash
cd /path/to/MobileApp-AutoTest
source venv/bin/activate  # On Windows: venv\Scripts\activate
python temp_text_detection/mcp.py
```

The server will start on port 8080 by default. You can change the port by setting the `MCP_PORT` environment variable:

```bash
MCP_PORT=8081 python temp_text_detection/mcp.py
```

### Testing the MCP Server

A test client is provided to verify that the MCP server is working correctly. To run all tests:

```bash
python temp_text_detection/mcp_client.py
```

To run a specific test:

```bash
python temp_text_detection/mcp_client.py list_apps
python temp_text_detection/mcp_client.py launch_app com.apple.Preferences
python temp_text_detection/mcp_client.py tap 100 200
```

### API Endpoints

The MCP server provides the following endpoints:

#### Health and Status

- `GET /health` - Health check endpoint
- `GET /status` - Server status endpoint

#### Mobile Device Control

- `POST /tool/mobile_list_apps` - List installed applications
- `POST /tool/mobile_launch_app` - Launch an application by bundle ID
- `POST /tool/mobile_tap` - Tap at specific coordinates
- `POST /tool/mobile_swipe` - Swipe from one point to another
- `POST /tool/mobile_input_text` - Input text into the currently focused element
- `POST /tool/mobile_find_element` - Find an element using various locator strategies
- `POST /tool/mobile_get_screenshot` - Take a screenshot and return it as base64
- `POST /tool/mobile_get_page_source` - Get the XML representation of the current screen
- `POST /tool/mobile_find_image` - Find an image on the screen
- `POST /tool/mobile_tap_image` - Tap on an image found on the screen
- `POST /tool/mobile_wait_for_image` - Wait for an image to appear on the screen

### Example API Calls

#### List Installed Apps

```bash
curl -X POST http://localhost:8080/tool/mobile_list_apps -H "Content-Type: application/json" -d '{}'
```

#### Launch an App

```bash
curl -X POST http://localhost:8080/tool/mobile_launch_app -H "Content-Type: application/json" -d '{"bundle_id": "com.apple.Preferences"}'
```

#### Tap at Coordinates

```bash
curl -X POST http://localhost:8080/tool/mobile_tap -H "Content-Type: application/json" -d '{"x": 100, "y": 200}'
```

#### Swipe

```bash
curl -X POST http://localhost:8080/tool/mobile_swipe -H "Content-Type: application/json" -d '{"start_x": 100, "start_y": 300, "end_x": 100, "end_y": 100}'
```

#### Input Text

```bash
curl -X POST http://localhost:8080/tool/mobile_input_text -H "Content-Type: application/json" -d '{"text": "Hello, World!"}'
```

#### Find Element

```bash
curl -X POST http://localhost:8080/tool/mobile_find_element -H "Content-Type: application/json" -d '{"locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Settings\"]"}'
```

#### Take Screenshot

```bash
curl -X POST http://localhost:8080/tool/mobile_get_screenshot -H "Content-Type: application/json" -d '{}'
```

#### Find and Tap Image

```bash
curl -X POST http://localhost:8080/tool/mobile_tap_image -H "Content-Type: application/json" -d '{"image_path": "reference_images/settings_icon.png", "threshold": 0.8}'
```

## Integration with AI Systems

This MCP server can be integrated with AI systems like Claude to enable AI-driven mobile automation. The server follows the Mobile-Next MCP protocol, making it compatible with various AI tools and frameworks.

## Troubleshooting

If you encounter issues with the MCP server:

1. Check that the Appium server is running and accessible
2. Verify that a device is connected and recognized by the system
3. Check the server logs for detailed error messages
4. Ensure that the required dependencies are installed

## License

This MCP server implementation is part of the Mobile App Automation Tool and is subject to the same license terms.
