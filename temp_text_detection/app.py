import base64
import os
import sys
import argparse
from openai import OpenAI

def image_to_text(image_path: str, prompt: str = "Extract all text from this image") -> str:
    """Extract text from image using OpenRouter's Qwen2.5-VL-3B-Instruct model"""

    try:
        # Check if the image file exists
        if not os.path.exists(image_path):
            raise FileNotFoundError(f"Image file not found: {image_path}")

        # Determine image format from file extension
        image_format = os.path.splitext(image_path)[1].lower().replace('.', '')
        if not image_format:
            image_format = 'jpeg'  # Default to jpeg if no extension

        # Encode image to base64
        with open(image_path, "rb") as image_file:
            base64_image = base64.b64encode(image_file.read()).decode('utf-8')

        client = OpenAI(
            base_url="https://openrouter.ai/api/v1",
            api_key="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"
        )

        response = client.chat.completions.create(
            model="qwen/qwen2.5-vl-3b-instruct:free",  # Using the correct model name with :free suffix
            messages=[{
                "role": "user",
                "content": [
                    {"type": "text", "text": prompt},
                    {"type": "image_url", "image_url": {"url": f"data:image/{image_format};base64,{base64_image}"}}
                ]
            }],
            max_tokens=1000
        )
        return response.choices[0].message.content

    except FileNotFoundError as e:
        print(f"Error: {e}")
        return None
    except Exception as e:
        print(f"Error processing image: {e}")
        return None

def main():
    """Command-line interface for the image-to-text functionality"""
    parser = argparse.ArgumentParser(description='Extract text from images using AI')
    parser.add_argument('image_path', help='Path to the image file')
    parser.add_argument('--prompt', default="Extract all text exactly as shown",
                        help='Prompt for the AI model (default: "Extract all text exactly as shown")')
    parser.add_argument('--output', help='Output file to save the extracted text (optional)')

    args = parser.parse_args()

    # Use the correct path - if relative, make it relative to the script location
    image_path = args.image_path
    if not os.path.isabs(image_path):
        # Check if the file exists in the current directory
        if not os.path.exists(image_path):
            # Try in the parent directory
            parent_path = os.path.join("..", image_path)
            if os.path.exists(parent_path):
                image_path = parent_path

    # Extract text from the image
    text = image_to_text(image_path, args.prompt)

    if text:
        # Print the extracted text
        print("\nExtracted Text:")
        print("--------------")
        print(text)
        print("--------------")

        # Save to output file if specified
        if args.output:
            try:
                with open(args.output, 'w') as f:
                    f.write(text)
                print(f"\nText saved to {args.output}")
            except Exception as e:
                print(f"Error saving to output file: {e}")
    else:
        print("Failed to extract text from the image.")

if __name__ == "__main__":
    main()
