#!/usr/bin/env python3
"""
Download EAST Text Detection Model

This script downloads the EAST text detection model if it doesn't exist.
"""

import os
import urllib.request
import sys

def download_east_model():
    """Download the EAST text detection model."""
    model_url = "https://github.com/oyyd/frozen_east_text_detection.pb/raw/master/frozen_east_text_detection.pb"
    model_path = "frozen_east_text_detection.pb"
    
    if os.path.exists(model_path):
        print(f"EAST model already exists at {model_path}")
        return True
    
    try:
        print(f"Downloading EAST model from {model_url}...")
        urllib.request.urlretrieve(model_url, model_path)
        print(f"EAST model downloaded to {model_path}")
        return True
    except Exception as e:
        print(f"Error downloading EAST model: {e}")
        print("Please download the EAST model manually from:")
        print("https://github.com/oyyd/frozen_east_text_detection.pb/raw/master/frozen_east_text_detection.pb")
        print("and place it in the current directory.")
        return False

if __name__ == "__main__":
    success = download_east_model()
    sys.exit(0 if success else 1)
