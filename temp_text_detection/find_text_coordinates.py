#!/usr/bin/env python3
"""
Find Text Coordinates

A simple wrapper script to find text in screenshots and return the coordinates.
This script can be used in automation tools to locate UI elements by their text labels.
"""

import os
import sys
import json
import argparse
from pathlib import Path

# Add the parent directory to the path so we can import the find_text_in_screenshot module
sys.path.append(str(Path(__file__).parent.parent))
from find_text_in_screenshot import find_text

def main():
    """Main function to parse arguments and find text coordinates."""
    parser = argparse.ArgumentParser(description='Find text in a screenshot and return the coordinates.')
    parser.add_argument('image_path', help='Path to the input image')
    parser.add_argument('target_text', help='Text to find in the image')
    parser.add_argument('--output_format', choices=['json', 'simple'], default='simple',
                        help='Output format (json or simple)')
    parser.add_argument('--output_dir', help='Directory to save output images', default=None)
    parser.add_argument('--method', choices=['basic', 'east'], default='east',
                        help='Text detection method to use (basic or EAST)')
    parser.add_argument('--min_confidence', type=float, default=0.5,
                        help='Minimum confidence threshold for text detection')
    parser.add_argument('--device_width', type=int, help='Target device width for coordinate scaling')
    parser.add_argument('--device_height', type=int, help='Target device height for coordinate scaling')

    args = parser.parse_args()

    try:
        result = find_text(
            args.image_path,
            args.target_text,
            args.output_dir,
            args.method,
            args.min_confidence,
            args.device_width,
            args.device_height
        )

        if result:
            if args.output_format == 'json':
                # Output as JSON
                print(json.dumps(result, indent=2))
            else:
                # Output in simple format (just the center coordinates)
                print(f"{result['coordinates']['center_x']},{result['coordinates']['center_y']}")
        else:
            if args.output_format == 'json':
                print(json.dumps({"error": f"Text '{args.target_text}' not found in the image."}, indent=2))
            else:
                print(f"ERROR: Text '{args.target_text}' not found in the image.")
            sys.exit(1)

    except Exception as e:
        if args.output_format == 'json':
            print(json.dumps({"error": str(e)}, indent=2))
        else:
            print(f"ERROR: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
