#!/bin/bash
# Find text on a device with specific dimensions
# Usage: ./find_text_on_device.sh <image_path> <text_to_find> <device_width> <device_height>

# Check if all required arguments are provided
if [ $# -lt 4 ]; then
    echo "Usage: $0 <image_path> <text_to_find> <device_width> <device_height> [output_format]"
    echo "  image_path: Path to the screenshot"
    echo "  text_to_find: Text to find in the image"
    echo "  device_width: Width of the target device"
    echo "  device_height: Height of the target device"
    echo "  output_format: (optional) 'simple' or 'json' (default: simple)"
    exit 1
fi

# Get arguments
IMAGE_PATH="$1"
TEXT_TO_FIND="$2"
DEVICE_WIDTH="$3"
DEVICE_HEIGHT="$4"
OUTPUT_FORMAT="${5:-simple}"

# Activate virtual environment if it exists
if [ -d "../venv" ]; then
    source ../venv/bin/activate
fi

# Run the script
python find_text_coordinates.py "$IMAGE_PATH" "$TEXT_TO_FIND" \
    --output_format "$OUTPUT_FORMAT" \
    --device_width "$DEVICE_WIDTH" \
    --device_height "$DEVICE_HEIGHT" \
    --method east
