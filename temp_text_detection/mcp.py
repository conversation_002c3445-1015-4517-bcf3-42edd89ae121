#!/usr/bin/env python3
"""
Mobile Control Protocol (MCP) Server for Mobile Automation

This script implements a Mobile-Next compatible MCP server for mobile device automation.
It provides standardized endpoints for controlling mobile devices and automating UI interactions.
"""

import os
import sys
import json
import logging
import traceback
from flask import Flask, request, jsonify
from functools import wraps

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger('mobile-mcp-server')

# Create Flask app
app = Flask(__name__)

# Default MCP server port (can be overridden with environment variable)
MCP_PORT = int(os.environ.get('MCP_PORT', 8080))

# Try to import the AppiumDeviceController from the main app
try:
    # Add parent directory to path to allow imports
    parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    if parent_dir not in sys.path:
        sys.path.append(parent_dir)

    from app.utils.appium_device_controller import AppiumDeviceController
    # Try to get configured ports
    try:
        import config
        appium_port = getattr(config, 'APPIUM_PORT', 4723)
        wda_port = getattr(config, 'WDA_PORT', 8100)
        device_controller = AppiumDeviceController(appium_port=appium_port, wda_port=wda_port)
    except ImportError:
        device_controller = AppiumDeviceController()
    logger.info("Successfully imported AppiumDeviceController")
except ImportError:
    logger.warning("Could not import AppiumDeviceController, using mock implementation")
    device_controller = None

# Decorator for handling exceptions in routes
def handle_exceptions(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        try:
            return f(*args, **kwargs)
        except Exception as e:
            logger.error(f"Error in {f.__name__}: {str(e)}")
            logger.error(traceback.format_exc())
            return jsonify({
                "status": "error",
                "error": str(e),
                "traceback": traceback.format_exc()
            }), 500
    return decorated_function

# MCP Tool endpoints
@app.route('/tool/mobile_connect_simulator', methods=['POST'])
@handle_exceptions
def mobile_connect_simulator():
    """Connect to an iOS simulator by UDID"""
    if not device_controller:
        return jsonify({"status": "error", "error": "Device controller not initialized"}), 500

    data = request.json
    udid = data.get('udid')

    if not udid:
        return jsonify({"status": "error", "error": "Simulator UDID is required"}), 400

    try:
        # Launch the simulator if it's not already running
        import subprocess

        # Check if the simulator is already booted
        result = subprocess.run(['xcrun', 'simctl', 'list', 'devices'], capture_output=True, text=True)
        if udid not in result.stdout or 'Booted' not in result.stdout:
            logger.info(f"Booting simulator with UDID: {udid}")
            subprocess.run(['xcrun', 'simctl', 'boot', udid], check=True)

        # Connect to the simulator using Appium
        capabilities = {
            'platformName': 'iOS',
            'automationName': 'XCUITest',
            'deviceName': 'iPhone Simulator',
            'udid': udid,
            'noReset': True,
            'useNewWDA': False,
            'usePrebuiltWDA': True,
            'wdaLaunchTimeout': 120000,
            'wdaConnectionTimeout': 120000,
            'shouldUseSingletonTestManager': False,
            'maxTypingFrequency': 10,
            'simpleIsVisibleCheck': True
        }

        # Try to connect to the simulator
        if hasattr(device_controller, 'connect_to_device'):
            result = device_controller.connect_to_device(udid, capabilities)
            return jsonify({
                "status": "success",
                "result": result
            })
        else:
            # Mock implementation for testing
            logger.info(f"Mock: Connecting to simulator with UDID: {udid}")
            return jsonify({
                "status": "success",
                "result": {
                    "status": "success",
                    "message": f"Connected to simulator with UDID: {udid}"
                }
            })
    except Exception as e:
        logger.error(f"Error connecting to simulator {udid}: {str(e)}")
        return jsonify({"status": "error", "error": str(e)}), 500

@app.route('/tool/mobile_list_apps', methods=['POST'])
@handle_exceptions
def mobile_list_apps():
    """List all installed applications on the connected device"""
    if not device_controller:
        return jsonify({"status": "error", "error": "Device controller not initialized"}), 500

    try:
        # Get the list of installed apps from the device controller
        apps = device_controller.get_installed_apps()
        return jsonify({
            "status": "success",
            "apps": apps
        })
    except Exception as e:
        logger.error(f"Error listing apps: {str(e)}")
        return jsonify({"status": "error", "error": str(e)}), 500

@app.route('/tool/mobile_launch_app', methods=['POST'])
@handle_exceptions
def mobile_launch_app():
    """Launch an application on the connected device by bundle ID"""
    if not device_controller:
        return jsonify({"status": "error", "error": "Device controller not initialized"}), 500

    data = request.json
    bundle_id = data.get('bundle_id')

    if not bundle_id:
        return jsonify({"status": "error", "error": "Bundle ID is required"}), 400

    try:
        # Launch the app using the device controller
        result = device_controller.launch_app(bundle_id)
        return jsonify({
            "status": "success",
            "result": result
        })
    except Exception as e:
        logger.error(f"Error launching app {bundle_id}: {str(e)}")
        return jsonify({"status": "error", "error": str(e)}), 500

@app.route('/tool/mobile_tap', methods=['POST'])
@handle_exceptions
def mobile_tap():
    """Tap at specific coordinates on the device screen"""
    if not device_controller:
        return jsonify({"status": "error", "error": "Device controller not initialized"}), 500

    data = request.json
    x = data.get('x')
    y = data.get('y')

    if x is None or y is None:
        return jsonify({"status": "error", "error": "Coordinates (x, y) are required"}), 400

    try:
        # Tap at the specified coordinates
        result = device_controller.tap(x, y)
        return jsonify({
            "status": "success",
            "result": result
        })
    except Exception as e:
        logger.error(f"Error tapping at ({x}, {y}): {str(e)}")
        return jsonify({"status": "error", "error": str(e)}), 500

@app.route('/tool/mobile_swipe', methods=['POST'])
@handle_exceptions
def mobile_swipe():
    """Swipe from one point to another on the device screen"""
    if not device_controller:
        return jsonify({"status": "error", "error": "Device controller not initialized"}), 500

    data = request.json
    start_x = data.get('start_x')
    start_y = data.get('start_y')
    end_x = data.get('end_x')
    end_y = data.get('end_y')
    duration = data.get('duration', 500)  # Default duration: 500ms

    if None in (start_x, start_y, end_x, end_y):
        return jsonify({"status": "error", "error": "Start and end coordinates are required"}), 400

    try:
        # Perform the swipe action
        result = device_controller.swipe(start_x, start_y, end_x, end_y, duration)
        return jsonify({
            "status": "success",
            "result": result
        })
    except Exception as e:
        logger.error(f"Error swiping from ({start_x}, {start_y}) to ({end_x}, {end_y}): {str(e)}")
        return jsonify({"status": "error", "error": str(e)}), 500

@app.route('/tool/mobile_input_text', methods=['POST'])
@handle_exceptions
def mobile_input_text():
    """Input text into the currently focused element"""
    if not device_controller:
        return jsonify({"status": "error", "error": "Device controller not initialized"}), 500

    data = request.json
    text = data.get('text')

    if text is None:
        return jsonify({"status": "error", "error": "Text is required"}), 400

    try:
        # Input the text
        result = device_controller.input_text(text)
        return jsonify({
            "status": "success",
            "result": result
        })
    except Exception as e:
        logger.error(f"Error inputting text: {str(e)}")
        return jsonify({"status": "error", "error": str(e)}), 500

@app.route('/tool/mobile_find_element', methods=['POST'])
@handle_exceptions
def mobile_find_element():
    """Find an element on the screen using various locator strategies"""
    if not device_controller:
        return jsonify({"status": "error", "error": "Device controller not initialized"}), 500

    data = request.json
    locator_type = data.get('locator_type')  # id, xpath, accessibility_id, etc.
    locator_value = data.get('locator_value')

    if not locator_type or not locator_value:
        return jsonify({"status": "error", "error": "Locator type and value are required"}), 400

    try:
        # Find the element
        element = device_controller.find_element(locator_type, locator_value)
        if element:
            return jsonify({
                "status": "success",
                "element": element
            })
        else:
            return jsonify({
                "status": "error",
                "error": f"Element not found with {locator_type}='{locator_value}'"
            }), 404
    except Exception as e:
        logger.error(f"Error finding element with {locator_type}='{locator_value}': {str(e)}")
        return jsonify({"status": "error", "error": str(e)}), 500

@app.route('/tool/mobile_get_screenshot', methods=['POST'])
@handle_exceptions
def mobile_get_screenshot():
    """Take a screenshot of the device screen and return it as base64"""
    if not device_controller:
        return jsonify({"status": "error", "error": "Device controller not initialized"}), 500

    try:
        # Take a screenshot
        screenshot = device_controller.get_screenshot_as_base64()
        return jsonify({
            "status": "success",
            "screenshot": screenshot
        })
    except Exception as e:
        logger.error(f"Error taking screenshot: {str(e)}")
        return jsonify({"status": "error", "error": str(e)}), 500

@app.route('/tool/mobile_get_page_source', methods=['POST'])
@handle_exceptions
def mobile_get_page_source():
    """Get the XML representation of the current screen"""
    if not device_controller:
        return jsonify({"status": "error", "error": "Device controller not initialized"}), 500

    try:
        # Get the page source
        page_source = device_controller.get_page_source()
        return jsonify({
            "status": "success",
            "page_source": page_source
        })
    except Exception as e:
        logger.error(f"Error getting page source: {str(e)}")
        return jsonify({"status": "error", "error": str(e)}), 500

@app.route('/tool/mobile_find_image', methods=['POST'])
@handle_exceptions
def mobile_find_image():
    """Find an image on the screen using template matching"""
    if not device_controller:
        return jsonify({"status": "error", "error": "Device controller not initialized"}), 500

    data = request.json
    image_path = data.get('image_path')
    threshold = data.get('threshold', 0.8)  # Default threshold

    if not image_path:
        return jsonify({"status": "error", "error": "Image path is required"}), 400

    try:
        # Find the image
        result = device_controller.find_image(image_path, threshold)
        return jsonify({
            "status": "success",
            "result": result
        })
    except Exception as e:
        logger.error(f"Error finding image {image_path}: {str(e)}")
        return jsonify({"status": "error", "error": str(e)}), 500

@app.route('/tool/mobile_tap_image', methods=['POST'])
@handle_exceptions
def mobile_tap_image():
    """Tap on an image found on the screen"""
    if not device_controller:
        return jsonify({"status": "error", "error": "Device controller not initialized"}), 500

    data = request.json
    image_path = data.get('image_path')
    threshold = data.get('threshold', 0.8)  # Default threshold

    if not image_path:
        return jsonify({"status": "error", "error": "Image path is required"}), 400

    try:
        # Tap on the image
        result = device_controller.tap_image(image_path, threshold)
        return jsonify({
            "status": "success",
            "result": result
        })
    except Exception as e:
        logger.error(f"Error tapping on image {image_path}: {str(e)}")
        return jsonify({"status": "error", "error": str(e)}), 500

@app.route('/tool/mobile_wait_for_image', methods=['POST'])
@handle_exceptions
def mobile_wait_for_image():
    """Wait for an image to appear on the screen"""
    if not device_controller:
        return jsonify({"status": "error", "error": "Device controller not initialized"}), 500

    data = request.json
    image_path = data.get('image_path')
    timeout = data.get('timeout', 10)  # Default timeout: 10 seconds
    threshold = data.get('threshold', 0.8)  # Default threshold

    if not image_path:
        return jsonify({"status": "error", "error": "Image path is required"}), 400

    try:
        # Wait for the image
        result = device_controller.wait_for_image(image_path, timeout, threshold)
        return jsonify({
            "status": "success",
            "result": result
        })
    except Exception as e:
        logger.error(f"Error waiting for image {image_path}: {str(e)}")
        return jsonify({"status": "error", "error": str(e)}), 500

# Mock implementation for when AppiumDeviceController is not available
class MockDeviceController:
    """Mock implementation of device controller for testing"""

    def __init__(self):
        self.connected_device = None
        self.connected_simulator = None

    def connect_to_device(self, device_id, capabilities=None):
        """Connect to a device or simulator"""
        logger.info(f"Mock: Connecting to device {device_id}")
        self.connected_device = device_id

        # If this is a simulator, store it as such
        if capabilities and capabilities.get('platformName') == 'iOS' and 'udid' in capabilities:
            self.connected_simulator = capabilities.get('udid')
            logger.info(f"Mock: Connected to iOS simulator with UDID: {self.connected_simulator}")

        return {"status": "success", "message": f"Connected to device {device_id}"}

    def get_installed_apps(self):
        """Get list of installed apps"""
        logger.info("Mock: Getting installed apps")

        # If we're connected to a simulator, try to get real apps
        if self.connected_simulator:
            try:
                import subprocess
                result = subprocess.run(['xcrun', 'simctl', 'listapps', self.connected_simulator],
                                       capture_output=True, text=True)

                # Parse the output to extract bundle IDs
                import re
                bundle_ids = re.findall(r'CFBundleIdentifier = "([^"]+)"', result.stdout)

                if bundle_ids:
                    logger.info(f"Mock: Found {len(bundle_ids)} apps on simulator")
                    return bundle_ids
            except Exception as e:
                logger.warning(f"Mock: Error getting real apps from simulator: {e}")

        # Default list of apps
        return ["com.apple.Preferences", "com.apple.MobileSafari", "com.apple.mobileslideshow", "com.apple.Health"]

    def launch_app(self, bundle_id):
        """Launch an app by bundle ID"""
        logger.info(f"Mock: Launching app {bundle_id}")

        # If we're connected to a simulator, try to launch the app for real
        if self.connected_simulator:
            try:
                import subprocess
                logger.info(f"Launching app {bundle_id} on simulator {self.connected_simulator}")
                result = subprocess.run(['xcrun', 'simctl', 'launch', self.connected_simulator, bundle_id],
                                       capture_output=True, text=True)

                if "launched successfully" in result.stdout:
                    return {"status": "success", "message": f"Launched app {bundle_id} on simulator"}
                else:
                    # Try to extract the error message
                    error_msg = result.stderr.strip() if result.stderr else "Unknown error"
                    return {"status": "error", "message": f"Failed to launch app {bundle_id}: {error_msg}"}
            except Exception as e:
                logger.warning(f"Mock: Error launching app on simulator: {e}")

        # Default mock response
        return {"status": "success", "message": f"Launched app {bundle_id}"}

    def tap(self, x, y):
        """Tap at coordinates"""
        logger.info(f"Mock: Tapping at ({x}, {y})")
        return {"status": "success", "message": f"Tapped at ({x}, {y})"}

    def swipe(self, start_x, start_y, end_x, end_y, duration=500):
        """Swipe from one point to another"""
        logger.info(f"Mock: Swiping from ({start_x}, {start_y}) to ({end_x}, {end_y})")
        return {"status": "success", "message": f"Swiped from ({start_x}, {start_y}) to ({end_x}, {end_y})"}

    def input_text(self, text):
        """Input text"""
        logger.info(f"Mock: Inputting text '{text}'")
        return {"status": "success", "message": f"Input text '{text}'"}

    def find_element(self, locator_type, locator_value):
        """Find an element using a locator"""
        logger.info(f"Mock: Finding element with {locator_type}='{locator_value}'")
        return {"element_id": "mock-element-id", "rect": {"x": 100, "y": 100, "width": 200, "height": 50}}

    def get_screenshot_as_base64(self):
        """Take a screenshot and return as base64"""
        logger.info("Mock: Taking screenshot")

        # If we're connected to a simulator, try to take a real screenshot
        if self.connected_simulator:
            try:
                import subprocess
                import base64
                import os

                # Create a temporary file for the screenshot
                temp_file = f"/tmp/simulator_screenshot_{self.connected_simulator}.png"

                # Take the screenshot
                subprocess.run(['xcrun', 'simctl', 'io', self.connected_simulator, 'screenshot', temp_file],
                              check=True)

                # Read the file and encode as base64
                if os.path.exists(temp_file):
                    with open(temp_file, 'rb') as f:
                        screenshot_data = base64.b64encode(f.read()).decode('utf-8')

                    # Clean up the temporary file
                    os.remove(temp_file)

                    return screenshot_data
            except Exception as e:
                logger.warning(f"Mock: Error taking real screenshot from simulator: {e}")

        # Default mock response
        return "base64_encoded_screenshot_data"

    def get_page_source(self):
        """Get the XML representation of the current screen"""
        logger.info("Mock: Getting page source")
        return "<AppiumAUT><XCUIElementTypeApplication>...</XCUIElementTypeApplication></AppiumAUT>"

    def find_image(self, image_path, threshold=0.8):
        """Find an image on the screen"""
        logger.info(f"Mock: Finding image {image_path}")
        return {"status": "success", "position": {"x": 150, "y": 300}}

    def tap_image(self, image_path, threshold=0.8):
        """Tap on an image found on the screen"""
        logger.info(f"Mock: Tapping on image {image_path}")
        return {"status": "success", "message": f"Tapped on image {image_path}"}

    def wait_for_image(self, image_path, timeout=10, threshold=0.8):
        """Wait for an image to appear on the screen"""
        logger.info(f"Mock: Waiting for image {image_path}")
        return {"status": "success", "position": {"x": 150, "y": 300}}

# Use mock controller if real one is not available
if device_controller is None:
    device_controller = MockDeviceController()

# Health check endpoint
@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        "status": "ok",
        "service": "mobile-mcp-server",
        "version": "1.0.0"
    })

# MCP server status endpoint
@app.route('/status', methods=['GET'])
def status():
    """Server status endpoint"""
    return jsonify({
        "status": "ok",
        "service": "mobile-mcp-server",
        "version": "1.0.0",
        "device_controller": "real" if not isinstance(device_controller, MockDeviceController) else "mock"
    })

# Run the server
if __name__ == '__main__':
    logger.info(f"Starting Mobile MCP Server on port {MCP_PORT}")
    app.run(host='0.0.0.0', port=MCP_PORT, debug=True)
