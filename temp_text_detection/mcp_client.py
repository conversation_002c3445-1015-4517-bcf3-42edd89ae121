#!/usr/bin/env python3
"""
Mobile Control Protocol (MCP) Client for Testing

This script provides a simple client to test the MCP server functionality.
"""

import requests
import json
import sys

# MCP server URL (default port is 8080)
MCP_URL = "http://localhost:8080"

def print_response(response):
    """Print the response in a formatted way"""
    try:
        print(f"Status Code: {response.status_code}")
        print("Response:")
        print(json.dumps(response.json(), indent=2))
    except Exception as e:
        print(f"Error parsing response: {e}")
        print(response.text)

def test_health():
    """Test the health check endpoint"""
    print("\n=== Testing Health Check ===")
    response = requests.get(f"{MCP_URL}/health")
    print_response(response)

def test_status():
    """Test the status endpoint"""
    print("\n=== Testing Status ===")
    response = requests.get(f"{MCP_URL}/status")
    print_response(response)

def test_list_apps():
    """Test the list apps endpoint"""
    print("\n=== Testing List Apps ===")
    response = requests.post(f"{MCP_URL}/tool/mobile_list_apps", json={})
    print_response(response)

def test_launch_app(bundle_id="com.apple.Preferences"):
    """Test the launch app endpoint"""
    print(f"\n=== Testing Launch App ({bundle_id}) ===")
    response = requests.post(f"{MCP_URL}/tool/mobile_launch_app", json={"bundle_id": bundle_id})
    print_response(response)

def test_tap(x=100, y=100):
    """Test the tap endpoint"""
    print(f"\n=== Testing Tap at ({x}, {y}) ===")
    response = requests.post(f"{MCP_URL}/tool/mobile_tap", json={"x": x, "y": y})
    print_response(response)

def test_swipe(start_x=100, start_y=300, end_x=100, end_y=100):
    """Test the swipe endpoint"""
    print(f"\n=== Testing Swipe from ({start_x}, {start_y}) to ({end_x}, {end_y}) ===")
    response = requests.post(
        f"{MCP_URL}/tool/mobile_swipe", 
        json={
            "start_x": start_x, 
            "start_y": start_y, 
            "end_x": end_x, 
            "end_y": end_y
        }
    )
    print_response(response)

def test_input_text(text="Hello, World!"):
    """Test the input text endpoint"""
    print(f"\n=== Testing Input Text ('{text}') ===")
    response = requests.post(f"{MCP_URL}/tool/mobile_input_text", json={"text": text})
    print_response(response)

def test_find_element(locator_type="xpath", locator_value="//XCUIElementTypeButton[@name='Settings']"):
    """Test the find element endpoint"""
    print(f"\n=== Testing Find Element ({locator_type}='{locator_value}') ===")
    response = requests.post(
        f"{MCP_URL}/tool/mobile_find_element", 
        json={
            "locator_type": locator_type, 
            "locator_value": locator_value
        }
    )
    print_response(response)

def test_get_screenshot():
    """Test the get screenshot endpoint"""
    print("\n=== Testing Get Screenshot ===")
    response = requests.post(f"{MCP_URL}/tool/mobile_get_screenshot", json={})
    # Don't print the full base64 data
    if response.status_code == 200:
        data = response.json()
        if "screenshot" in data:
            # Truncate the screenshot data for display
            screenshot = data["screenshot"]
            data["screenshot"] = f"{screenshot[:50]}... (truncated)"
        print(f"Status Code: {response.status_code}")
        print("Response:")
        print(json.dumps(data, indent=2))
    else:
        print_response(response)

def test_get_page_source():
    """Test the get page source endpoint"""
    print("\n=== Testing Get Page Source ===")
    response = requests.post(f"{MCP_URL}/tool/mobile_get_page_source", json={})
    # Don't print the full XML
    if response.status_code == 200:
        data = response.json()
        if "page_source" in data:
            # Truncate the page source for display
            page_source = data["page_source"]
            data["page_source"] = f"{page_source[:100]}... (truncated)"
        print(f"Status Code: {response.status_code}")
        print("Response:")
        print(json.dumps(data, indent=2))
    else:
        print_response(response)

def test_find_image(image_path="reference_images/settings_icon.png"):
    """Test the find image endpoint"""
    print(f"\n=== Testing Find Image ({image_path}) ===")
    response = requests.post(
        f"{MCP_URL}/tool/mobile_find_image", 
        json={
            "image_path": image_path,
            "threshold": 0.8
        }
    )
    print_response(response)

def test_tap_image(image_path="reference_images/settings_icon.png"):
    """Test the tap image endpoint"""
    print(f"\n=== Testing Tap Image ({image_path}) ===")
    response = requests.post(
        f"{MCP_URL}/tool/mobile_tap_image", 
        json={
            "image_path": image_path,
            "threshold": 0.8
        }
    )
    print_response(response)

def test_wait_for_image(image_path="reference_images/settings_icon.png"):
    """Test the wait for image endpoint"""
    print(f"\n=== Testing Wait For Image ({image_path}) ===")
    response = requests.post(
        f"{MCP_URL}/tool/mobile_wait_for_image", 
        json={
            "image_path": image_path,
            "timeout": 5,
            "threshold": 0.8
        }
    )
    print_response(response)

def run_all_tests():
    """Run all tests"""
    test_health()
    test_status()
    test_list_apps()
    test_launch_app()
    test_tap()
    test_swipe()
    test_input_text()
    test_find_element()
    test_get_screenshot()
    test_get_page_source()
    test_find_image()
    test_tap_image()
    test_wait_for_image()

if __name__ == "__main__":
    # Check if a specific test was requested
    if len(sys.argv) > 1:
        test_name = sys.argv[1]
        if test_name == "health":
            test_health()
        elif test_name == "status":
            test_status()
        elif test_name == "list_apps":
            test_list_apps()
        elif test_name == "launch_app":
            bundle_id = sys.argv[2] if len(sys.argv) > 2 else "com.apple.Preferences"
            test_launch_app(bundle_id)
        elif test_name == "tap":
            x = int(sys.argv[2]) if len(sys.argv) > 2 else 100
            y = int(sys.argv[3]) if len(sys.argv) > 3 else 100
            test_tap(x, y)
        elif test_name == "swipe":
            start_x = int(sys.argv[2]) if len(sys.argv) > 2 else 100
            start_y = int(sys.argv[3]) if len(sys.argv) > 3 else 300
            end_x = int(sys.argv[4]) if len(sys.argv) > 4 else 100
            end_y = int(sys.argv[5]) if len(sys.argv) > 5 else 100
            test_swipe(start_x, start_y, end_x, end_y)
        elif test_name == "input_text":
            text = sys.argv[2] if len(sys.argv) > 2 else "Hello, World!"
            test_input_text(text)
        elif test_name == "find_element":
            locator_type = sys.argv[2] if len(sys.argv) > 2 else "xpath"
            locator_value = sys.argv[3] if len(sys.argv) > 3 else "//XCUIElementTypeButton[@name='Settings']"
            test_find_element(locator_type, locator_value)
        elif test_name == "get_screenshot":
            test_get_screenshot()
        elif test_name == "get_page_source":
            test_get_page_source()
        elif test_name == "find_image":
            image_path = sys.argv[2] if len(sys.argv) > 2 else "reference_images/settings_icon.png"
            test_find_image(image_path)
        elif test_name == "tap_image":
            image_path = sys.argv[2] if len(sys.argv) > 2 else "reference_images/settings_icon.png"
            test_tap_image(image_path)
        elif test_name == "wait_for_image":
            image_path = sys.argv[2] if len(sys.argv) > 2 else "reference_images/settings_icon.png"
            test_wait_for_image(image_path)
        else:
            print(f"Unknown test: {test_name}")
            print("Available tests: health, status, list_apps, launch_app, tap, swipe, input_text, find_element, get_screenshot, get_page_source, find_image, tap_image, wait_for_image")
    else:
        # Run all tests
        run_all_tests()
