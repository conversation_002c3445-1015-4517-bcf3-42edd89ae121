#!/usr/bin/env python3
"""
Test Text Detection

This script demonstrates how to use the text detection tool with a sample image.
"""

import os
import sys
import cv2
import numpy as np
from text_detector import detect_text, detect_text_east

def create_sample_image(output_path, text="Hello World", font_scale=2.0, thickness=2):
    """
    Create a sample image with text for testing.
    
    Args:
        output_path (str): Path to save the sample image
        text (str, optional): Text to add to the image
        font_scale (float, optional): Font scale
        thickness (int, optional): Text thickness
    """
    # Create a blank image
    img = np.ones((300, 600, 3), dtype=np.uint8) * 255
    
    # Add text
    font = cv2.FONT_HERSHEY_SIMPLEX
    text_size = cv2.getTextSize(text, font, font_scale, thickness)[0]
    
    # Calculate position to center the text
    text_x = (img.shape[1] - text_size[0]) // 2
    text_y = (img.shape[0] + text_size[1]) // 2
    
    # Add text to the image
    cv2.putText(img, text, (text_x, text_y), font, font_scale, (0, 0, 0), thickness)
    
    # Save the image
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    cv2.imwrite(output_path, img)
    print(f"Sample image created at {output_path}")
    
    return output_path

def main():
    """Main function to test text detection."""
    # Create output directory in temp folder
    import sys
    sys.path.append(os.path.join(os.path.dirname(os.path.dirname(__file__)), 'app'))
    try:
        from utils.file_utils import get_temp_subdirectory
        output_dir = get_temp_subdirectory('text_detection_output')
    except ImportError:
        # Fallback if import fails
        output_dir = "temp_text_detection/output"
        os.makedirs(output_dir, exist_ok=True)
    
    # Create a sample image
    sample_image_path = os.path.join(output_dir, "sample_text.jpg")
    create_sample_image(sample_image_path, "Hello World")
    
    # Test basic method
    print("\nTesting basic text detection method...")
    basic_regions = detect_text(sample_image_path, output_dir)
    
    # Check if EAST model exists
    east_model_path = "frozen_east_text_detection.pb"
    if os.path.exists(east_model_path):
        # Test EAST method
        print("\nTesting EAST text detection method...")
        east_regions = detect_text_east(sample_image_path, output_dir)
    else:
        print("\nEAST model not found. Skipping EAST method test.")
        print("Run 'python download_east_model.py' to download the EAST model.")
    
    print("\nTest completed. Check the output directory for annotated images.")

if __name__ == "__main__":
    main()
