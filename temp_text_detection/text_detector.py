#!/usr/bin/env python3
"""
Text Detection Script

This script detects text in an image and returns the coordinates of the text regions.
It uses OpenCV for image processing and Pytesseract for OCR.
"""

import cv2
import numpy as np
import pytesseract
from PIL import Image
import os
import argparse

def detect_text(image_path, output_dir=None, min_confidence=0.5):
    """
    Detect text in an image and return the coordinates of the text regions.

    Args:
        image_path (str): Path to the input image
        output_dir (str, optional): Directory to save output images. If None, no images are saved.
        min_confidence (float, optional): Minimum confidence threshold for text detection

    Returns:
        list: List of dictionaries containing text and coordinates
    """
    # Read the image
    img = cv2.imread(image_path)
    if img is None:
        raise ValueError(f"Could not read image at {image_path}")

    # Get image dimensions
    height, width, _ = img.shape

    # Create a copy for visualization
    img_copy = img.copy()

    # Convert to grayscale
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

    # Apply threshold to get binary image
    _, binary = cv2.threshold(gray, 150, 255, cv2.THRESH_BINARY_INV)

    # Apply morphological operations to enhance text regions
    kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (5, 5))
    binary = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)

    # Find contours
    contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

    # Filter contours based on area
    min_area = 100  # Minimum area to consider as text
    text_regions = []

    for i, contour in enumerate(contours):
        area = cv2.contourArea(contour)
        if area > min_area:
            # Get bounding box
            x, y, w, h = cv2.boundingRect(contour)

            # Extract the region of interest
            roi = gray[y:y+h, x:x+w]

            # Use Pytesseract to extract text
            text = pytesseract.image_to_string(roi, config='--psm 6').strip()

            # Only include if text is not empty
            if text:
                # Calculate center coordinates
                center_x = x + w // 2
                center_y = y + h // 2

                # Add to results
                text_regions.append({
                    'text': text,
                    'coordinates': {
                        'x1': x,
                        'y1': y,
                        'x2': x + w,
                        'y2': y + h,
                        'center_x': center_x,
                        'center_y': center_y,
                        'width': w,
                        'height': h
                    },
                    'confidence': 1.0  # Placeholder, we don't have actual confidence from this method
                })

                # Draw rectangle on the copy
                cv2.rectangle(img_copy, (x, y), (x + w, y + h), (0, 255, 0), 2)

                # Add text label
                cv2.putText(img_copy, text, (x, y - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)

    # Save the annotated image if output_dir is provided
    if output_dir:
        os.makedirs(output_dir, exist_ok=True)
        base_name = os.path.basename(image_path)
        output_path = os.path.join(output_dir, f"annotated_{base_name}")
        cv2.imwrite(output_path, img_copy)
        print(f"Annotated image saved to {output_path}")

    return text_regions

def detect_text_east(image_path, output_dir=None, min_confidence=0.5):
    """
    Detect text in an image using EAST text detector and return the coordinates.

    Args:
        image_path (str): Path to the input image
        output_dir (str, optional): Directory to save output images. If None, no images are saved.
        min_confidence (float, optional): Minimum confidence threshold for text detection

    Returns:
        list: List of dictionaries containing text and coordinates
    """
    # Check if EAST model exists
    east_model_paths = [
        "frozen_east_text_detection.pb",  # Current directory
        os.path.join(os.path.dirname(__file__), "frozen_east_text_detection.pb"),  # In temp_text_detection folder
        os.path.join(os.path.dirname(os.path.dirname(__file__)), "app/static/models/frozen_east_text_detection.pb")  # In app/static/models folder
    ]

    east_model_path = None
    for path in east_model_paths:
        if os.path.exists(path):
            east_model_path = path
            print(f"Found EAST model at: {east_model_path}")
            break

    if east_model_path is None:
        print("EAST model not found. Checking in current directory...")
        # Try to find the model in the current working directory
        current_dir = os.getcwd()
        for root, dirs, files in os.walk(current_dir):
            if "frozen_east_text_detection.pb" in files:
                east_model_path = os.path.join(root, "frozen_east_text_detection.pb")
                print(f"Found EAST model at: {east_model_path}")
                break

    if east_model_path is None:
        print("EAST model not found. Please download it from: https://github.com/oyyd/frozen_east_text_detection.pb")
        print("and place it in the temp_text_detection directory or app/static/models directory.")
        return []

    # Read the image
    img = cv2.imread(image_path)
    if img is None:
        raise ValueError(f"Could not read image at {image_path}")

    # Get image dimensions
    orig_height, orig_width, _ = img.shape

    # Set the new width and height for the EAST model (must be multiples of 32)
    new_width = 320
    new_height = 320

    # Calculate the ratio between original and new dimensions
    ratio_width = orig_width / float(new_width)
    ratio_height = orig_height / float(new_height)

    # Resize the image
    img_resized = cv2.resize(img, (new_width, new_height))

    # Create a copy for visualization
    img_copy = img.copy()

    # Prepare the image for the EAST model
    blob = cv2.dnn.blobFromImage(img_resized, 1.0, (new_width, new_height),
                                (123.68, 116.78, 103.94), swapRB=True, crop=False)

    # Load the EAST model
    net = cv2.dnn.readNet(east_model_path)

    # Set the output layers
    output_layers = ["feature_fusion/Conv_7/Sigmoid", "feature_fusion/concat_3"]

    # Forward pass
    net.setInput(blob)
    scores, geometry = net.forward(output_layers)

    # Decode the predictions
    rectangles = []
    confidences = []

    # Get dimensions of the scores volume
    rows, cols = scores.shape[2:4]

    # Loop over the rows
    for y in range(rows):
        # Extract the scores, followed by the geometrical data
        scores_data = scores[0, 0, y]
        x_data0 = geometry[0, 0, y]
        x_data1 = geometry[0, 1, y]
        x_data2 = geometry[0, 2, y]
        x_data3 = geometry[0, 3, y]
        angles_data = geometry[0, 4, y]

        # Loop over the columns
        for x in range(cols):
            # If our score does not have sufficient probability, ignore it
            if scores_data[x] < min_confidence:
                continue

            # Compute the offset factor as our resulting feature maps will
            # be 4x smaller than the input image
            offset_x = x * 4.0
            offset_y = y * 4.0

            # Extract the rotation angle for the prediction and compute the
            # sin and cosine
            angle = angles_data[x]
            cos = np.cos(angle)
            sin = np.sin(angle)

            # Use the geometry volume to derive the width and height of
            # the bounding box
            h = x_data0[x] + x_data2[x]
            w = x_data1[x] + x_data3[x]

            # Compute both the starting and ending (x, y)-coordinates for
            # the text prediction bounding box
            end_x = int(offset_x + (cos * x_data1[x]) + (sin * x_data2[x]))
            end_y = int(offset_y - (sin * x_data1[x]) + (cos * x_data2[x]))
            start_x = int(end_x - w)
            start_y = int(end_y - h)

            # Add the bounding box coordinates and probability score to
            # our respective lists
            rectangles.append((start_x, start_y, end_x, end_y))
            confidences.append(float(scores_data[x]))

    # Apply non-maximum suppression to suppress weak, overlapping bounding boxes
    indices = cv2.dnn.NMSBoxes(rectangles, confidences, min_confidence, 0.4)

    # Initialize the list to store text regions
    text_regions = []

    # Loop over the indices
    if len(indices) > 0:
        for i in indices.flatten():
            # Extract the bounding box coordinates
            start_x, start_y, end_x, end_y = rectangles[i]

            # Scale the bounding box coordinates based on the respective ratios
            start_x = int(start_x * ratio_width)
            start_y = int(start_y * ratio_height)
            end_x = int(end_x * ratio_width)
            end_y = int(end_y * ratio_height)

            # Ensure coordinates are within image boundaries
            start_x = max(0, start_x)
            start_y = max(0, start_y)
            end_x = min(orig_width, end_x)
            end_y = min(orig_height, end_y)

            # Calculate width and height
            w = end_x - start_x
            h = end_y - start_y

            # Extract the region of interest
            roi = img[start_y:end_y, start_x:end_x]

            # Skip if ROI is empty
            if roi.size == 0:
                continue

            # Convert to grayscale for better OCR
            roi_gray = cv2.cvtColor(roi, cv2.COLOR_BGR2GRAY)

            # Use Pytesseract to extract text
            text = pytesseract.image_to_string(roi_gray, config='--psm 6').strip()

            # Only include if text is not empty
            if text:
                # Calculate center coordinates
                center_x = start_x + w // 2
                center_y = start_y + h // 2

                # Add to results
                text_regions.append({
                    'text': text,
                    'coordinates': {
                        'x1': start_x,
                        'y1': start_y,
                        'x2': end_x,
                        'y2': end_y,
                        'center_x': center_x,
                        'center_y': center_y,
                        'width': w,
                        'height': h
                    },
                    'confidence': confidences[i]
                })

                # Draw rectangle on the copy
                cv2.rectangle(img_copy, (start_x, start_y), (end_x, end_y), (0, 255, 0), 2)

                # Add text label
                cv2.putText(img_copy, text, (start_x, start_y - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)

    # Save the annotated image if output_dir is provided
    if output_dir:
        os.makedirs(output_dir, exist_ok=True)
        base_name = os.path.basename(image_path)
        output_path = os.path.join(output_dir, f"annotated_east_{base_name}")
        cv2.imwrite(output_path, img_copy)
        print(f"Annotated image saved to {output_path}")

    return text_regions

def main():
    """Main function to parse arguments and run the text detection."""
    parser = argparse.ArgumentParser(description='Detect text in an image and get coordinates.')
    parser.add_argument('image_path', help='Path to the input image')
    parser.add_argument('--output_dir', help='Directory to save output images', default=None)
    parser.add_argument('--method', choices=['basic', 'east'], default='basic',
                        help='Text detection method to use (basic or EAST)')
    parser.add_argument('--min_confidence', type=float, default=0.5,
                        help='Minimum confidence threshold for text detection')

    args = parser.parse_args()

    # Set default output directory to temp folder if not specified
    if args.output_dir is None:
        import sys
        sys.path.append(os.path.join(os.path.dirname(os.path.dirname(__file__)), 'app'))
        try:
            from utils.file_utils import get_temp_subdirectory
            args.output_dir = get_temp_subdirectory('text_detection_output')
        except ImportError:
            # Fallback if import fails
            args.output_dir = 'temp_text_detection/output'

    try:
        if args.method == 'basic':
            text_regions = detect_text(args.image_path, args.output_dir, args.min_confidence)
        else:
            text_regions = detect_text_east(args.image_path, args.output_dir, args.min_confidence)

        # Print the results
        print("\nDetected Text Regions:")
        for i, region in enumerate(text_regions):
            print(f"\nRegion {i+1}:")
            print(f"  Text: {region['text']}")
            print(f"  Coordinates: (x1={region['coordinates']['x1']}, y1={region['coordinates']['y1']}, "
                  f"x2={region['coordinates']['x2']}, y2={region['coordinates']['y2']})")
            print(f"  Center: ({region['coordinates']['center_x']}, {region['coordinates']['center_y']})")
            print(f"  Dimensions: {region['coordinates']['width']}x{region['coordinates']['height']}")
            if 'confidence' in region:
                print(f"  Confidence: {region['confidence']:.2f}")

        print(f"\nTotal regions detected: {len(text_regions)}")

    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
