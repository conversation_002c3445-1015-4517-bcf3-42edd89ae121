#!/usr/bin/env python3
"""
Web Interface for Text Detection

This script provides a web interface for extracting text from images using AI.
"""

import gradio as gr
import os
from app import image_to_text

def process_image(image, prompt, save_output=False):
    """Process the image and extract text using AI."""
    if image is None:
        return "Please upload an image."
    
    # Save the uploaded image to a temporary file in temp directory
    import sys
    sys.path.append(os.path.join(os.path.dirname(os.path.dirname(__file__)), 'app'))
    try:
        from utils.file_utils import get_temp_subdirectory
        temp_dir = get_temp_subdirectory('text_detection_web')
    except ImportError:
        # Fallback if import fails
        temp_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "temp")
        os.makedirs(temp_dir, exist_ok=True)
    
    image_path = os.path.join(temp_dir, "uploaded_image.png")
    image.save(image_path)
    
    # Extract text from the image
    text = image_to_text(image_path, prompt)
    
    if text:
        # Save to output file if requested
        if save_output:
            output_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "output")
            os.makedirs(output_dir, exist_ok=True)
            output_path = os.path.join(output_dir, "extracted_text.txt")
            
            try:
                with open(output_path, 'w') as f:
                    f.write(text)
                return f"{text}\n\n(Text saved to {output_path})"
            except Exception as e:
                return f"{text}\n\n(Error saving to output file: {e})"
        
        return text
    else:
        return "Failed to extract text from the image."

# Create the Gradio interface
with gr.Blocks(title="AI Text Extraction Tool") as demo:
    gr.Markdown("# AI Text Extraction Tool")
    gr.Markdown("Upload an image and extract text using AI.")
    
    with gr.Row():
        with gr.Column(scale=1):
            image_input = gr.Image(type="pil", label="Upload Image")
            prompt_input = gr.Textbox(
                label="Prompt for AI", 
                placeholder="Enter prompt for the AI model",
                value="Extract all text exactly as shown"
            )
            save_checkbox = gr.Checkbox(label="Save output to file", value=False)
            extract_button = gr.Button("Extract Text", variant="primary")
        
        with gr.Column(scale=2):
            output = gr.Textbox(label="Extracted Text", lines=10)
    
    # Set up the button click event
    extract_button.click(
        fn=process_image,
        inputs=[image_input, prompt_input, save_checkbox],
        outputs=output
    )
    
    # Add some helpful information
    gr.Markdown("""
    ## How to Use
    
    1. **Upload an image** containing text
    2. **Enter a prompt** for the AI model (or use the default)
    3. **Click "Extract Text"** to process the image
    
    ## Tips
    
    - For better results, use images with clear, high-contrast text
    - You can customize the prompt to get different types of extraction
    - Check the "Save output to file" option to save the extracted text to a file
    """)

# Create necessary directories
os.makedirs(os.path.join(os.path.dirname(os.path.abspath(__file__)), "output"), exist_ok=True)
os.makedirs(os.path.join(os.path.dirname(os.path.abspath(__file__)), "temp"), exist_ok=True)

# Launch the app
if __name__ == "__main__":
    demo.launch(debug=True, share=False)
