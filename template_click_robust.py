#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Enhanced script for template matching and clicking with multiple fallback methods
Handles iOS Retina display scaling and provides multiple click strategies
"""

import os
import sys
import time
import cv2
import numpy as np
import traceback
import requests
import json
from airtest.core.api import *
from airtest.core.cv import Template
from ios_device import CustomIOSDevice

def try_multiple_click_methods(device, wda_url, x, y, padding=10):
    """Try multiple click methods and positions when standard click fails"""
    print(f"Trying multiple click methods at coordinates ({x}, {y})...")
    
    # Method 1: Standard device.touch
    print("\nMethod 1: Standard device.touch")
    result = device.touch((x, y))
    print(f"Standard touch result: {result}")
    if result:
        return True
    
    # Method 2: Try positions around the target
    print("\nMethod 2: Trying positions around the target")
    offset_positions = [
        (0, 0),        # Center
        (0, -padding), # Top
        (padding, 0),  # Right
        (0, padding),  # Bottom
        (-padding, 0), # Left
    ]
    
    for i, (dx, dy) in enumerate(offset_positions):
        pos_x, pos_y = x + dx, y + dy
        print(f"Trying position {i+1}: ({pos_x}, {pos_y})")
        result = device.touch((pos_x, pos_y))
        print(f"Result: {result}")
        if result:
            return True
        time.sleep(0.3)
    
    # Method 3: Direct WebDriverAgent REST API call
    print("\nMethod 3: Direct WebDriverAgent REST API call")
    try:
        tap_data = {"x": x, "y": y}
        response = requests.post(f"{wda_url}/wda/tap/0", json=tap_data)
        print(f"Direct API response: {response.status_code}")
        print(f"Response body: {response.text}")
        if response.status_code == 200:
            return True
    except Exception as e:
        print(f"Direct API call error: {str(e)}")
    
    # Method 4: Try tap with 2 fingers
    print("\nMethod 4: Two-finger tap")
    try:
        tap_data = {"x": x, "y": y}
        response = requests.post(f"{wda_url}/wda/tap/2", json=tap_data)
        print(f"Two-finger tap response: {response.status_code}")
        if response.status_code == 200:
            return True
    except Exception as e:
        print(f"Two-finger tap error: {str(e)}")
    
    # Method 5: Try long press
    print("\nMethod 5: Long press")
    try:
        press_data = {"x": x, "y": y, "duration": 0.5}
        response = requests.post(f"{wda_url}/wda/touchAndHold", json=press_data)
        print(f"Long press response: {response.status_code}")
        if response.status_code == 200:
            return True
    except Exception as e:
        print(f"Long press error: {str(e)}")
    
    # Method 6: Try with actions API
    print("\nMethod 6: Actions API")
    try:
        actions_data = {
            "actions": [
                {
                    "action": "tap",
                    "options": {
                        "x": x,
                        "y": y,
                        "count": 1
                    }
                }
            ]
        }
        response = requests.post(f"{wda_url}/actions", json=actions_data)
        print(f"Actions API response: {response.status_code}")
        if response.status_code == 200:
            return True
    except Exception as e:
        print(f"Actions API error: {str(e)}")
    
    # Method 7: Try to find and click element based on coordinates
    print("\nMethod 7: Find and click element at coordinates")
    try:
        # Get all elements on screen
        response = requests.post(
            f"{wda_url}/elements", 
            json={"using": "class name", "value": "XCUIElementTypeAny"}
        )
        
        if response.status_code == 200:
            elements = response.json().get("value", [])
            print(f"Found {len(elements)} elements on screen")
            
            # Find closest element to our coordinates
            closest_element = None
            closest_distance = float('inf')
            
            for element in elements:
                element_id = element.get("ELEMENT")
                
                # Get element rect
                rect_response = requests.get(f"{wda_url}/element/{element_id}/rect")
                if rect_response.status_code != 200:
                    continue
                    
                rect = rect_response.json().get("value", {})
                if not rect:
                    continue
                    
                # Calculate center point and distance
                center_x = rect.get("x", 0) + rect.get("width", 0) / 2
                center_y = rect.get("y", 0) + rect.get("height", 0) / 2
                
                # Calculate distance to our target coordinates
                distance = ((center_x - x) ** 2 + (center_y - y) ** 2) ** 0.5
                
                if distance < closest_distance:
                    closest_distance = distance
                    closest_element = element_id
            
            if closest_element and closest_distance < 50:  # Within 50px
                print(f"Found element close to target coordinates (distance: {closest_distance:.2f}px)")
                click_response = requests.post(f"{wda_url}/element/{closest_element}/click")
                print(f"Click element response: {click_response.status_code}")
                if click_response.status_code == 200:
                    return True
    except Exception as e:
        print(f"Element finding error: {str(e)}")
    
    print("All click methods failed")
    return False

def main():
    # Parse command line arguments
    if len(sys.argv) < 2:
        template_path = "1.png"
        print(f"No template path provided, using default: {template_path}")
    else:
        template_path = sys.argv[1]
    
    wda_url = sys.argv[2] if len(sys.argv) > 2 else "http://localhost:8100"
    
    try:
        # Check if template exists
        if not os.path.exists(template_path):
            print(f"Error: Template file '{template_path}' not found")
            return 1
            
        # Print template information
        template_img = cv2.imread(template_path)
        if template_img is None:
            print(f"Error: Could not read template file '{template_path}'")
            return 1
            
        template_height, template_width = template_img.shape[:2]
        print(f"Template dimensions: {template_width}x{template_height}")
        
        # Connect to device
        print(f"Connecting to device at {wda_url}...")
        device = CustomIOSDevice(wda_url=wda_url)
        
        # Get device resolution
        device_width, device_height = device.get_current_resolution()
        device_resolution = (device_width, device_height)
        print(f"Device screen dimensions: {device_width}x{device_height}")
        
        # Take screenshot for template matching
        print("Taking screenshot...")
        screen_file = "screen_for_template.png"
        device.snapshot(screen_file)
        
        # Get screenshot dimensions
        screen = cv2.imread(screen_file)
        screen_height, screen_width = screen.shape[:2]
        print(f"Screenshot dimensions: {screen_width}x{screen_height}")
        
        # Calculate scale factor between screenshot and device dimensions
        scale_x = device_width / screen_width
        scale_y = device_height / screen_height
        print(f"Scale factor between screenshot and device: x={scale_x:.2f}, y={scale_y:.2f}")
        
        # Try to find the template on screen using direct template matching
        print(f"Looking for template {template_path} on screen...")
        try:
            # Convert to grayscale for template matching
            screen_gray = cv2.cvtColor(screen, cv2.COLOR_BGR2GRAY)
            template_img_gray = cv2.cvtColor(template_img, cv2.COLOR_BGR2GRAY)
            
            # Perform template matching
            result = cv2.matchTemplate(screen_gray, template_img_gray, cv2.TM_CCOEFF_NORMED)
            min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
            
            print(f"Best match confidence: {max_val:.4f}")
            
            if max_val >= 0.7:  # Confidence threshold
                # Calculate center of matched area
                match_width, match_height = template_img_gray.shape[1], template_img_gray.shape[0]
                center_x = max_loc[0] + match_width // 2
                center_y = max_loc[1] + match_height // 2
                
                print(f"Template found at screenshot coordinates: ({center_x}, {center_y}) with confidence: {max_val:.4f}")
                
                # Create debug image showing match
                debug_img = screen.copy()
                cv2.rectangle(debug_img, max_loc, 
                            (max_loc[0] + match_width, max_loc[1] + match_height), 
                            (0, 255, 0), 2)
                cv2.circle(debug_img, (center_x, center_y), 5, (0, 0, 255), -1)
                cv2.putText(debug_img, f"Conf: {max_val:.2f}", 
                           (max_loc[0], max_loc[1] - 10),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
                
                # Convert screenshot coordinates to device coordinates 
                device_x = int(center_x * scale_x)
                device_y = int(center_y * scale_y)
                
                # Draw device coordinate point on debug image
                scaled_x = int(device_x / scale_x)
                scaled_y = int(device_y / scale_y)
                cv2.circle(debug_img, (scaled_x, scaled_y), 8, (255, 255, 0), -1)
                cv2.putText(debug_img, f"Device: ({device_x}, {device_y})", 
                           (scaled_x + 10, scaled_y),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 0), 2)
                           
                # Save debug image
                cv2.imwrite("debug_template_match.png", debug_img)
                
                print(f"Converting to device coordinates: ({device_x}, {device_y})")
                
                # Take before screenshot
                device.snapshot("before_click.png")
                
                # Try multiple click methods
                click_result = try_multiple_click_methods(device, wda_url, device_x, device_y)
                
                # Take a screenshot after clicking
                time.sleep(1)
                device.snapshot("after_click.png")
                print("Saved screenshots: before_click.png and after_click.png")
                
                return 0 if click_result else 1
            else:
                print(f"Template not found. Best match confidence {max_val:.4f} is below threshold.")
                return 1
                
        except Exception as e:
            print(f"Error finding template: {str(e)}")
            traceback.print_exc()
            return 1
            
    except Exception as e:
        print(f"Error: {str(e)}")
        traceback.print_exc()
        return 1
    finally:
        # Clean up temp files
        if os.path.exists("screen_for_template.png") and not os.environ.get('DEBUG_KEEP_TEMP'):
            os.remove("screen_for_template.png")

if __name__ == "__main__":
    sys.exit(main()) 