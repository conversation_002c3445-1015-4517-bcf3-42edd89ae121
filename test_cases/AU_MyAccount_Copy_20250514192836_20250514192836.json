{"name": "NZ- MyAccount", "created": "2025-06-20 16:01:49", "device_id": "********-00186C801E13C01E", "actions": [{"action_id": "pjFNt3w5Fr", "executionTime": "3396ms", "package_id": "env[appid]", "timestamp": *************, "type": "restartApp"}, {"action_id": "u6bRYZZFAv", "duration": 5, "executionTime": "5012ms", "time": 5, "timestamp": *************, "type": "wait"}, {"action_id": "xAPeBnVHrT", "executionTime": "4809ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "txtHomeAccountCtaSignIn", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "ly2oT3zqmf", "executionTime": "1133ms", "function_name": "alert_accept", "timestamp": *************, "type": "iosFunctions"}, {"action_id": "RLvvFEJpgz", "expanded": false, "test_case_id": "KmartSignin_Copy_20250513181428_20250513181428.json", "test_case_name": "Kmart-NZ-Signin", "test_case_steps_count": 6, "timestamp": *************, "type": "multiStep"}, {"action_id": "sl3Wk1gK8X", "executionTime": "2128ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "V59u3l1wkM", "executionTime": "1600ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[contains(@name,\"Manage your account\")]", "timeout": 10, "timestamp": *************, "type": "waitTill"}, {"action_id": "fDgFGQYpCw", "executionTime": "1584ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtMy orders\"]", "timeout": 10, "timestamp": *************, "type": "waitTill"}, {"action_id": "pFlYwTS53v", "executionTime": "2075ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtMy orders\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "Z6g3sGuHTp", "duration": 5, "executionTime": "5018ms", "time": 5, "timestamp": *************, "type": "wait"}, {"action_id": "7g6MFJSGIO", "executionTime": "2152ms", "image_filename": "order-link-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//XCUIElementTypeOther[@name=\"main\"]//XCUIElementTypeLink)[4]", "method": "locator", "threshold": 0.7, "timeout": 20, "timestamp": *************, "type": "tap"}, {"action_id": "0962MtId5t", "count": 3, "direction": "up", "duration": 300, "end_x": 50, "end_y": 30, "executionTime": "12209ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Email tax invoice\"]", "start_x": 50, "start_y": 70, "timestamp": 1750038291478, "type": "swipeTillVisible", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "GgQaBLWYkb", "executionTime": "2101ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Email tax invoice\"]", "method": "locator", "timeout": 10, "timestamp": 1746521169867, "type": "tap"}, {"action_id": "g0PE7Mofye", "executionTime": "3938ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "Print order details", "method": "locator", "timeout": 10, "timestamp": 1746521217340, "type": "tap"}, {"action_id": "YuuQe2KupX", "executionTime": "2418ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Cancel\"]", "method": "locator", "timeout": 10, "timestamp": 1746521262417, "type": "tap"}, {"action_id": "FAvQgIuHc1", "double_tap": false, "executionTime": "3176ms", "image_filename": "return-to-orders-btn.png", "method": "image", "text_to_find": "Return", "threshold": 0.7, "timeout": 30, "timestamp": 1746522414963, "type": "tapOnText"}, {"action_id": "6qZnk86hGg", "executionTime": "2058ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "PbfHAtFQPP", "executionTime": "2058ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "V59u3l1wkM", "executionTime": "1617ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[contains(@name,\"Manage your account\")]", "timeout": 10, "timestamp": *************, "type": "waitTill"}, {"action_id": "3hOTINBVMf", "executionTime": "2806ms", "text_to_find": "details", "timeout": 30, "timestamp": *************, "type": "tapOnText"}, {"action_id": "WwIZzJEW9W", "executionTime": "2136ms", "image_filename": "env[device-back-img]", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": *************, "type": "tap"}, {"action_id": "20qUCJgpE9", "double_tap": false, "executionTime": "2813ms", "text_to_find": "address", "timeout": 30, "timestamp": *************, "type": "tapOnText"}, {"action_id": "9MqlsILCgk", "executionTime": "2186ms", "image_filename": "env[device-back-img]", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": *************, "type": "tap"}, {"action_id": "napKDohf3Z", "double_tap": false, "executionTime": "2796ms", "text_to_find": "payment", "timeout": 30, "timestamp": *************, "type": "tapOnText"}, {"action_id": "M1IXnYddFx", "executionTime": "2323ms", "image_filename": "env[device-back-img]", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1750038436036, "type": "tap"}, {"action_id": "oETU9DzLi4", "executionTime": "2167ms", "image_filename": "env[device-back-img]", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1750038410894, "type": "tap"}, {"action_id": "EJkHvEQccu", "count": 1, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "2605ms", "interval": 0.5, "start_x": 50, "start_y": 70, "timestamp": 1748320334957, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "BracBsfa3Y", "double_tap": false, "executionTime": "2738ms", "text_to_find": "locator", "timeout": 30, "timestamp": 1746573912172, "type": "tapOnText"}, {"action_id": "BracBsfa3Y", "double_tap": false, "executionTime": "5964ms", "text_to_find": "Nearby", "timeout": 30, "timestamp": 1746573997116, "type": "tapOnText"}, {"action_id": "2FnAZDskt1", "condition": {"locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Allow While Using App\"]", "timeout": 20}, "condition_type": "exists", "executionTime": "20378ms", "then_action": {"locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Allow While Using App\"]", "method": "locator", "timeout": 20, "type": "tap"}, "timestamp": 1747217890741, "type": "ifElseSteps"}, {"action_id": "3CTsyFe28F", "executionTime": "5183ms", "method": "coordinates", "text": "0616", "timeout": 15, "timestamp": 1747218136058, "type": "tapAndType", "x": 29, "y": 262}, {"action_id": "nVWzLauG8N", "double_tap": false, "executionTime": "2029ms", "image_filename": "store-locator-nz.png", "method": "image", "text_to_find": "AUCKLAND", "threshold": 0.7, "timeout": 30, "timestamp": 1747218453342, "type": "tapOnText"}, {"action_id": "YuuQe2KupX", "executionTime": "2262ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"<PERSON>\"]", "method": "locator", "timeout": 10, "timestamp": 1747218235931, "type": "tap"}, {"action_id": "q70JSbqKNk", "executionTime": "2278ms", "image_filename": "env[device-back-img]", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1750038372825, "type": "tap"}, {"action_id": "BracBsfa3Y", "double_tap": false, "executionTime": "2749ms", "text_to_find": "Customer", "timeout": 30, "timestamp": 1746574406045, "type": "tapOnText"}, {"action_id": "P2OkZzbCB3", "executionTime": "2172ms", "image_filename": "env[device-back-img]", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1750038382905, "type": "tap"}, {"action_id": "BracBsfa3Y", "double_tap": false, "executionTime": "2762ms", "text_to_find": "out", "timeout": 30, "timestamp": 1746574489503, "type": "tapOnText"}], "labels": [], "updated": "2025-06-20 16:01:49", "test_case_id": "tc_a7e5e52427fa"}