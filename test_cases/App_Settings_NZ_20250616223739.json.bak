{"name": "App Settings NZ", "created": "2025-06-16 22:55:38", "device_id": "********-00186C801E13C01E", "actions": [{"action_id": "XEbZHdi0GT", "executionTime": "2206ms", "package_id": "env[appid]", "timestamp": *************, "type": "restartApp"}, {"action_id": "veukWo4573", "executionTime": "2410ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtHomeAccountCtaSignIn\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "rJ86z4njuR", "executionTime": "1131ms", "function_name": "alert_accept", "timestamp": *************, "type": "iosFunctions"}, {"action_id": "a12JYjIm66", "expanded": false, "test_case_id": "KmartSignin_Copy_20250513181428_20250513181428.json", "test_case_name": "Kmart-NZ-Signin", "test_case_steps_count": 6, "timestamp": *************, "type": "multiStep"}, {"action_id": "mIKA85kXaW", "executionTime": "32ms", "package_id": "com.apple.Preferences", "timestamp": *************, "type": "terminateApp"}, {"action_id": "LfyQctrEJn", "executionTime": "1207ms", "package_id": "com.apple.Preferences", "timestamp": *************, "type": "launchApp"}, {"action_id": "w1RV76df9x", "executionTime": "3310ms", "text_to_find": "Wi-Fi", "timeout": 30, "timestamp": *************, "type": "tapOnText"}, {"action_id": "jUCAk6GJc4", "executionTime": "1034ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeSwitch[@name=\"Wi‑Fi\"]", "method": "locator", "timeout": 10, "timestamp": 1749445081254, "type": "tap"}, {"action_id": "oSQ8sPdVOJ", "executionTime": "3220ms", "package_id": "env[appid]", "timestamp": 1749445437019, "type": "restartApp"}, {"action_id": "cokvFXhj4c", "executionTime": "235ms", "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"txtNo internet connection\"]", "timeout": 10, "timestamp": 1749445125565, "type": "exists"}, {"action_id": "3KNqlNy6Bj", "executionTime": "724ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"2 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1749445177888, "type": "tap"}, {"action_id": "eSr9EFlJek", "executionTime": "207ms", "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"txtNo internet connection\"]", "timeout": 10, "timestamp": 1749445183545, "type": "exists"}, {"action_id": "6xgrAWyfZ4", "executionTime": "652ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"3 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1749445193141, "type": "tap"}, {"action_id": "WoymrHdtrO", "executionTime": "214ms", "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"txtNo internet connection\"]", "timeout": 10, "timestamp": 1749445211742, "type": "exists"}, {"action_id": "UpUSVInizv", "executionTime": "681ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"4 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1749445217156, "type": "tap"}, {"action_id": "seQcUKjkSU", "executionTime": "176ms", "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"txtNo internet connection\"]", "timeout": 10, "timestamp": 1749445234048, "type": "exists"}, {"action_id": "LfyQctrEJn", "executionTime": "125ms", "package_id": "com.apple.Preferences", "timestamp": 1749445249154, "type": "launchApp"}, {"action_id": "GRwHMVK4sA", "executionTime": "762ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeSwitch[@name=\"Wi‑Fi\"]", "method": "locator", "timeout": 10, "timestamp": 1749445257372, "type": "tap"}, {"action_id": "V42eHtTRYW", "duration": 5, "executionTime": "5016ms", "time": 5, "timestamp": 1749445273934, "type": "wait"}, {"action_id": "hCCEvRtj1A", "executionTime": "3213ms", "package_id": "env[appid]", "timestamp": 1749445309230, "type": "restartApp"}, {"action_id": "UpUSVInizv", "executionTime": "681ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1749445687300, "type": "tap"}, {"action_id": "oqTdx3vL0d", "count": 1, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "interval": 0.5, "start_x": 50, "start_y": 70, "timestamp": 1749446128417, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "LcYLwUffqj", "text_to_find": "out", "timeout": 30, "timestamp": 1749445809311, "type": "tapOnText"}, {"action_id": "xVuuejtCFA", "package_id": "com.apple.mobilesafari", "timestamp": 1749445877653, "type": "restartApp"}, {"action_id": "0Q0fm6OTij", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"TabBarItemTitle\"]", "method": "locator", "timeout": 10, "timestamp": 1749445933995, "type": "tap"}, {"action_id": "ISpNHH3V9g", "enter": true, "function_name": "text", "text": "kmart nz", "timestamp": 1750077580713, "type": "iosFunctions"}, {"action_id": "fTdGMJ3NH3", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"https://www.kmart.co.nz\"]", "method": "locator", "timeout": 10, "timestamp": 1749446027317, "type": "tap"}, {"action_id": "3KNqlNy6Bj", "executionTime": "724ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"1 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1749473131022, "type": "tap"}, {"action_id": "OKiI82VdnE", "count": 2, "direction": "up", "double_tap": false, "duration": 1000, "end_x": 50, "end_y": 30, "interval": 0.5, "locator_type": "xpath", "locator_value": "(//XCUIElementTypeScrollView)[4]/following-sibling::XCUIElementTypeOther[1]", "start_x": 50, "start_y": 70, "text_to_find": "Find", "timeout": 30, "timestamp": 1749474098726, "type": "tapOnText", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "UZkF5rnoLo", "enter": true, "function_name": "text", "text": "notebook", "timestamp": 1750078189412, "type": "iosFunctions"}, {"action_id": "n57KEWjTea", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "method": "locator", "timeout": 10, "timestamp": 1749474131811, "type": "waitTill"}, {"action_id": "n57KEWjTea", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "method": "locator", "timeout": 10, "timestamp": 1750078521821, "type": "tap"}, {"action_id": "Iab9zCfpqO", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "Add to bag", "method": "locator", "timeout": 10, "timestamp": 1749474441855, "type": "tap"}, {"action_id": "c4T3INQkzn", "package_id": "env[appid]", "timestamp": 1749889997693, "type": "restartApp"}, {"action_id": "UpUSVInizv", "executionTime": "681ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"4 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1749474449613, "type": "tap"}, {"action_id": "DbM0d0m6rU", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Increase quantity\"]", "method": "locator", "timeout": 10, "timestamp": 1749473005084, "type": "tap"}, {"action_id": "IW6uAwdtiW", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Decrease quantity\"]", "method": "locator", "timeout": 10, "timestamp": 1749473072817, "type": "tap"}, {"action_id": "K0c1gL9UK1", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"<PERSON>move\")]", "method": "locator", "timeout": 10, "timestamp": 1749474457145, "type": "tap"}], "labels": [], "updated": "2025-06-16 22:55:38"}