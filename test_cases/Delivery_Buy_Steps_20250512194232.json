{"name": "Delivery Buy Steps", "created": "2025-06-25 19:25:02", "device_id": "00008120-00186C801E13C01E", "actions": [{"action_id": "aqBkqyVhrZ", "executionTime": "16354ms", "image_filename": "delivery-tab-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Delivery\"]", "threshold": 0.7, "timeout": 40, "timestamp": 1746427811120, "type": "waitTill"}, {"action_id": "hwdyCKFAUJ", "executionTime": "1814ms", "image_filename": "delivery-tab-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Delivery\"]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1746427830202, "type": "tap"}, {"action_id": "xAa049Qgls", "count": 3, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "20082ms", "interval": 5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Continue to details\"]", "start_x": 50, "start_y": 70, "threshold": 0.7, "timeout": 20, "timestamp": 1745486216977, "type": "swipeTillVisible", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "Q5A0cNaJ24", "executionTime": "3031ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Continue to details\"]", "method": "locator", "timeout": 10, "timestamp": 1745486308596, "type": "tap"}, {"action_id": "h9trcMrvxt", "executionTime": "3038ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"First Name\"]", "method": "locator", "timeout": 10, "timestamp": 1745486361281, "type": "tap"}, {"action_id": "CLMmkV1OIM", "delay": 500, "executionTime": "3392ms", "function_name": "text", "text": "First Name", "timestamp": 1745486374043, "type": "textClear"}, {"action_id": "p8rfQL9ara", "executionTime": "3153ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Last Name\"]", "method": "locator", "timeout": 10, "timestamp": 1745486401162, "type": "tap"}, {"action_id": "QvuueoTR8W", "delay": 500, "executionTime": "3368ms", "function_name": "text", "text": "Last Name", "timestamp": 1745486416273, "type": "textClear"}, {"action_id": "9B5MQGTmpP", "executionTime": "3080ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Email\"]", "method": "locator", "timeout": 10, "timestamp": 1745486441044, "type": "tap"}, {"action_id": "lWJtKSqlPS", "delay": 500, "executionTime": "3483ms", "function_name": "text", "text": "<EMAIL>", "timestamp": 1745486452706, "type": "textClear"}, {"action_id": "yi5EsHEFvc", "executionTime": "3080ms", "function_name": "text", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Mobile number\"]", "method": "locator", "timeout": 10, "timestamp": 1745486486775, "type": "tap"}, {"action_id": "SFj4Aa7RHQ", "delay": 500, "executionTime": "3252ms", "function_name": "text", "text": "0400000000", "timestamp": 1745486504243, "type": "textClear"}, {"action_id": "kDpsm2D3xt", "enter": true, "executionTime": "2985ms", "function_name": "text", "text": " ", "timestamp": 1745570305956, "type": "iosFunctions"}, {"action_id": "5ZzW1VVSzy", "double_tap": false, "executionTime": "2068ms", "image_filename": "delivery_addreess_input.png", "method": "image", "text_to_find": "address", "threshold": 0.7, "timeout": 30, "timestamp": 1745562034217, "type": "tapOnText"}, {"action_id": "sdqCYvk2Du", "method": "coordinates", "text": "env[deliver-address]", "timeout": 60, "timestamp": 1749460676763, "type": "tapAndType", "x": "env[delivery-addr-x]", "y": "env[delivery-addr-y]"}, {"action_id": "NcU6aex76k", "executionTime": "1807ms", "image_filename": "keyboard_done_iphoneSE.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"<PERSON>\"]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1746616991897, "type": "tap"}, {"action_id": "mMnRNh3NEd", "image_filename": "env[delivery-address-img]", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1749457829278, "type": "tap"}, {"action_id": "TTpwkHEyuE", "count": 2, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "interval": 2, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Continue to payment\"]", "start_x": 50, "start_y": 70, "timestamp": 1747044105004, "type": "swipeTillVisible", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "1Lirmyxkft", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Continue to payment\"]", "method": "locator", "timeout": 10, "timestamp": 1747044123748, "type": "tap"}, {"action_id": "6LQ5cq0f6N", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeOther[@name=\"Select a payment method\"]/XCUIElementTypeOther[2]/XCUIElementTypeOther[2]/XCUIElementTypeOther", "method": "locator", "timeout": 10, "timestamp": 1747044256988, "type": "tap"}, {"action_id": "CBBib3pFkq", "count": 2, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "interval": 2, "locator_type": "xpath", "locator_value": "//XCUIElementTypeLink[@name=\"PayPal\"]", "start_x": 50, "start_y": 70, "timestamp": 1747044349502, "type": "swipeTillVisible", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "ftA0OJvd0W", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeLink[@name=\"PayPal\"]", "method": "locator", "timeout": 10, "timestamp": 1747044370730, "type": "tap"}, {"action_id": "mfOWujfRpL", "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[contains(@name,\"PayPal\")]", "timeout": 10, "timestamp": 1747044439860, "type": "exists"}, {"action_id": "XLpUP3Wr93", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"close\"]", "method": "locator", "timeout": 10, "timestamp": 1747044533430, "type": "tap"}, {"action_id": "dkSs61jGvX", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeOther[@name=\"Select a payment method\"]/XCUIElementTypeOther[2]/XCUIElementTypeOther[3]/XCUIElementTypeOther", "method": "locator", "timeout": 10, "timestamp": 1747044584329, "type": "tap"}, {"action_id": "GN587Y6VBQ", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeLink[@name=\"Pay in 4\"]", "method": "locator", "timeout": 10, "timestamp": 1747044637222, "type": "tap"}, {"action_id": "wSdfNe4Kww", "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"Pay in 4 with PayPal\"]", "timeout": 10, "timestamp": 1747044696695, "type": "exists"}, {"action_id": "TzPItWbvDR", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"close\"]", "method": "locator", "timeout": 10, "timestamp": 1747044707774, "type": "tap"}, {"action_id": "YBT2MVclAv", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeOther[@name=\"Select a payment method\"]/XCUIElementTypeOther[2]/XCUIElementTypeOther[4]/XCUIElementTypeOther", "method": "locator", "timeout": 10, "timestamp": 1747044719683, "type": "tap"}, {"action_id": "9Pwdq32eUk", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Afterpay, available on orders between $70-$2,000.\"]", "method": "locator", "timeout": 10, "timestamp": 1747044771806, "type": "tap"}, {"action_id": "lSG7un0qKK", "locator_type": "xpath", "locator_value": "//XCUIElementTypeImage[@name=\"Afterpay\"]", "timeout": 10, "timestamp": 1747044824024, "type": "exists"}, {"action_id": "vYLhraWpQm", "executionTime": "1889ms", "image_filename": "banner-close-updated.png", "method": "image", "package_id": "au.com.kmart", "threshold": 0.7, "timeout": 20, "timestamp": 1745982487511, "type": "tap"}, {"action_id": "YqmO7h7VP0", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeOther[@name=\"Select a payment method\"]/XCUIElementTypeOther[2]/XCUIElementTypeOther[5]/XCUIElementTypeOther", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "UgjXUTZy7Z", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Afterpay, available on orders between $70-$2,000.\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "TAKgcEDqvz", "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"Sign in with your Zip account\"]", "timeout": 10, "timestamp": *************, "type": "exists"}, {"action_id": "vYLhraWpQm", "executionTime": "1889ms", "image_filename": "banner-close-updated.png", "method": "image", "package_id": "au.com.kmart", "threshold": 0.7, "timeout": 20, "timestamp": *************, "type": "tap"}, {"action_id": "gPYNwJ0HKo", "executionTime": "3312ms", "image_filename": "bag1-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "aqBkqyVhrZ", "executionTime": "16354ms", "image_filename": "delivery-tab-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Delivery\"]", "threshold": 0.7, "timeout": 40, "timestamp": *************, "type": "waitTill"}, {"action_id": "2bcxKJ2cPg", "count": 1, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"<PERSON>move\")]", "start_x": 50, "start_y": 70, "timestamp": 1748156595054, "type": "swipeTillVisible", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "a4pJa7EAyI", "executionTime": "5059ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"<PERSON>move\")]", "method": "locator", "timeout": 10, "timestamp": 1745490172397, "type": "tap"}, {"action_id": "q6kSH9e0MI", "executionTime": "2804ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"Continue shopping\"]", "method": "locator", "timeout": 10, "timestamp": 1745490217950, "type": "tap"}], "labels": [], "updated": "2025-06-25 19:25:02", "test_case_id": "tc_0c4e14da344c"}