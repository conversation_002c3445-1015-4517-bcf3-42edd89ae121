{"name": "Delivery  Buy", "created": "2025-06-09 11:49:00", "device_id": "00008120-00186C801E13C01E", "actions": [{"action_id": "oubgsUKJBQ", "double_tap": false, "image_filename": "env[homepage-edit-link-img]", "method": "image", "text_to_find": "Edit", "threshold": 0.7, "timeout": 30, "timestamp": 1749348717236, "type": "tapOnText"}, {"action_id": "u9AfEcXf4f", "executionTime": "11455ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "btnCurrentLocationButton", "timeout": 10, "timestamp": 1746447155354, "type": "waitTill"}, {"action_id": "McMjFpUsEi", "executionTime": "5400ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "btnCurrentLocationButton", "method": "locator", "timeout": 20, "timestamp": 1745995736295, "type": "tap"}, {"action_id": "17eoVQWSoT", "executionTime": "3658ms", "text_to_find": "Save", "timeout": 30, "timestamp": 1745995853138, "type": "tapOnText"}, {"action_id": "TQHQbNy7K8", "condition": {"locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Allow While Using App\"]", "timeout": 25}, "condition_type": "exists", "executionTime": "10728ms", "then_action": {"locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Allow While Using App\"]", "timeout": 25, "type": "clickElement"}, "timestamp": 1746004835860, "type": "ifElseSteps"}, {"action_id": "TQHQbNy7KY", "condition": {"locator_type": "accessibility_id", "locator_value": "btnUpdate", "timeout": 10}, "condition_type": "exists", "executionTime": "10728ms", "then_action": {"locator_type": "accessibility_id", "locator_value": "btnUpdate", "timeout": 10, "type": "clickElement"}, "timestamp": 1746004835860, "type": "ifElseSteps"}, {"action_id": "ad3p271EJD", "condition": {"locator_type": "xpath", "locator_value": "//XCUIElementTypeOther[@name=\"txtLocationTitle\"]/preceding-sibling::XCUIElementTypeButton[1]", "timeout": 20}, "condition_type": "exists", "then_action": {"type": "tap", "x": 0, "y": 0}, "timestamp": 1746940043121, "type": "ifElseSteps"}, {"action_id": "64XwxxXdVM", "double_tap": false, "executionTime": "1830ms", "image_filename": "homepage-search-se.png", "method": "image", "text_to_find": "Find", "threshold": 0.7, "timeout": 30, "timestamp": 1745980305734, "type": "tapOnText"}, {"action_id": "Xve5h7yYnZ", "executionTime": "3088ms", "function_name": "text", "text": "Uno card", "timestamp": 1745484826180, "type": "iosFunctions"}, {"action_id": "zkVRnc7eha", "executionTime": "2767ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Filter\"]", "timeout": 30, "timestamp": 1745485041367, "type": "waitTill"}, {"action_id": "rAie1aNECc", "executionTime": "6791ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "Add UNO Card Game - Red colour to bag Add", "method": "locator", "timeout": 10, "timestamp": 1745485077480, "type": "tap"}, {"action_id": "voY6NQoWRs", "executionTime": "3307ms", "image_filename": "bag1-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1745486173359, "type": "tap"}, {"action_id": "aqBkqyVhrZ", "executionTime": "16354ms", "image_filename": "delivery-tab-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Delivery\"]", "threshold": 0.7, "timeout": 15, "timestamp": 1746427811120, "type": "waitTill"}, {"action_id": "hwdyCKFAUJ", "executionTime": "1814ms", "image_filename": "delivery-tab-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Delivery\"]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1746427830202, "type": "tap"}, {"action_id": "xAa049Qgls", "count": 3, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "20082ms", "interval": 5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Continue to details\"]", "start_x": 50, "start_y": 70, "threshold": 0.7, "timeout": 20, "timestamp": 1745486216977, "type": "swipeTillVisible", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "Q5A0cNaJ24", "executionTime": "3031ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Continue to details\"]", "method": "locator", "timeout": 10, "timestamp": 1745486308596, "type": "tap"}, {"action_id": "h9trcMrvxt", "executionTime": "3038ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"First Name\"]", "method": "locator", "timeout": 10, "timestamp": 1745486361281, "type": "tap"}, {"action_id": "CLMmkV1OIM", "delay": 500, "executionTime": "3392ms", "function_name": "text", "text": "First Name", "timestamp": 1745486374043, "type": "textClear"}, {"action_id": "p8rfQL9ara", "executionTime": "3153ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Last Name\"]", "method": "locator", "timeout": 10, "timestamp": 1745486401162, "type": "tap"}, {"action_id": "QvuueoTR8W", "delay": 500, "executionTime": "3368ms", "function_name": "text", "text": "Last Name", "timestamp": 1745486416273, "type": "textClear"}, {"action_id": "9B5MQGTmpP", "executionTime": "3080ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Email\"]", "method": "locator", "timeout": 10, "timestamp": 1745486441044, "type": "tap"}, {"action_id": "lWJtKSqlPS", "delay": 500, "executionTime": "3483ms", "function_name": "text", "text": "<EMAIL>", "timestamp": 1745486452706, "type": "textClear"}, {"action_id": "yi5EsHEFvc", "executionTime": "3080ms", "function_name": "text", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Mobile number\"]", "method": "locator", "timeout": 10, "timestamp": 1745486486775, "type": "tap"}, {"action_id": "SFj4Aa7RHQ", "delay": 500, "executionTime": "3252ms", "function_name": "text", "text": "0400000000", "timestamp": 1745486504243, "type": "textClear"}, {"action_id": "kDpsm2D3xt", "enter": true, "executionTime": "2985ms", "function_name": "text", "text": " ", "timestamp": 1745570305956, "type": "iosFunctions"}, {"action_id": "5ZzW1VVSzy", "double_tap": false, "executionTime": "2068ms", "image_filename": "delivery_addreess_input.png", "method": "image", "text_to_find": "address", "threshold": 0.7, "timeout": 30, "timestamp": 1745562034217, "type": "tapOnText"}, {"action_id": "RmtvsSn1f9", "executionTime": "5707ms", "method": "coordinates", "text": "305 238 Flinders", "timeout": 60, "timestamp": 1745562450150, "type": "tapAndType", "x": 54, "y": 314}, {"action_id": "NcU6aex76k", "executionTime": "1807ms", "image_filename": "keyboard_done_iphoneSE.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"<PERSON>\"]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1746616991897, "type": "tap"}, {"action_id": "R4dG3ONPQA", "image_filename": "env[delivery-address-img]", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1749349107703, "type": "tap"}, {"action_id": "W4WZVSkkUa", "count": 3, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "9682ms", "interval": 0.5, "locator_type": "xpath", "locator_value": " //XCUIElementTypeButton[@name=\"Continue to payment\"]", "start_x": 50, "start_y": 70, "threshold": 0.7, "timeout": 20, "timestamp": 1745489862127, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "vYLhraWpQm", "executionTime": "1889ms", "image_filename": "banner-close-updated.png", "method": "image", "package_id": "au.com.kmart", "threshold": 0.7, "timeout": 20, "timestamp": 1745982487511, "type": "tap"}, {"action_id": "gPYNwJ0HKo", "executionTime": "3312ms", "image_filename": "bag1-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1745490084698, "type": "tap"}, {"action_id": "a4pJa7EAyI", "executionTime": "5059ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"<PERSON>move\")]", "method": "locator", "timeout": 10, "timestamp": 1745490172397, "type": "tap"}, {"action_id": "q6kSH9e0MI", "executionTime": "2804ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"Continue shopping\"]", "method": "locator", "timeout": 10, "timestamp": 1745490217950, "type": "tap"}], "labels": [], "updated": "2025-06-09 11:49:00", "test_case_id": "tc_df1997700d70"}