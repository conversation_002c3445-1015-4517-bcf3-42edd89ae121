{"name": "Delivery & CNC", "created": "2025-06-27 08:08:59", "device_id": "********-00186C801E13C01E", "actions": [{"action_id": "HotUJOd6oB", "executionTime": "3542ms", "package_id": "env[appid]", "timestamp": *************, "type": "restartApp"}, {"action_id": "rkL0oz4kiL", "executionTime": "9925ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "txtHomeAccountCtaSignIn", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "yUJyVO5Wev", "executionTime": "1260ms", "function_name": "alert_accept", "timestamp": *************, "type": "iosFunctions"}, {"action_id": "3caMBvQX7k", "executionTime": "3294ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Email\"]", "timeout": 30, "timestamp": *************, "type": "waitTill"}, {"action_id": "El6k4IPZly", "expanded": false, "test_case_id": "KmartProdSignin_Copy_20250501144222_20250501144222.json", "test_case_name": "Kmart-Signin", "test_case_steps_count": 8, "timestamp": *************, "type": "multiStep"}, {"action_id": "ZBXCQNlT8z", "double_tap": false, "text_to_find": "Find", "timeout": 30, "timestamp": *************, "type": "tapOnText"}, {"action_id": "sc2KH9bG6H", "executionTime": "3710ms", "function_name": "text", "text": "Uno card", "timestamp": *************, "type": "iosFunctions"}, {"action_id": "nAB6Q8LAdv", "executionTime": "3513ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Filter\"]", "timeout": 30, "timestamp": 1745485041367, "type": "waitTill"}, {"action_id": "FnrbyHq7bU", "executionTime": "7785ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "Add UNO Card Game - Red colour to bag Add", "method": "locator", "timeout": 10, "timestamp": 1745485077480, "type": "tap"}, {"action_id": "jY0oPjKbuS", "executionTime": "2490ms", "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "timeout": 10, "timestamp": 1746102129685, "type": "exists"}, {"action_id": "F1olhgKhUt", "executionTime": "3976ms", "image_filename": "bag1-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1746100706297, "type": "tap"}, {"action_id": "uM5FOSrU5U", "executionTime": "2715ms", "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Click & Collect\"]", "timeout": 30, "timestamp": 1746102183980, "type": "exists"}, {"action_id": "qjj0i3rcUh", "executionTime": "2113ms", "image_filename": "cnc-tab-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Click & Collect\"]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1746100742115, "type": "tap"}, {"action_id": "Qbg9bipTGs", "count": 1, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "4366ms", "interval": 0.5, "locator_type": "accessibilityid", "locator_value": "Remove UNO Card Game", "start_x": 50, "start_y": 70, "threshold": 0.7, "timeout": 20, "timestamp": 1746102698568, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "FKZs2qCWoU", "executionTime": "3732ms", "text_to_find": "Brunswick", "timeout": 30, "timestamp": 1746099725928, "type": "tapOnText"}, {"action_id": "7SpDO20tS2", "duration": 10, "executionTime": "10020ms", "time": 10, "timestamp": 1746154851726, "type": "wait"}, {"action_id": "Q0fomJIDoQ", "executionTime": "2421ms", "image_filename": "banner-close-updated.png", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1746100313636, "type": "tap"}, {"action_id": "lWIRxRm6HE", "executionTime": "4034ms", "image_filename": "bag1-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1746102819296, "type": "tap"}, {"action_id": "qjj0i3rcUh", "executionTime": "2113ms", "image_filename": "cnc-tab-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Click & Collect\"]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1749347993660, "type": "tap"}, {"action_id": "Qbg9bipTGs", "count": 1, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "4544ms", "interval": 0.5, "locator_type": "accessibilityid", "locator_value": "Remove UNO Card Game", "start_x": 50, "start_y": 70, "threshold": 0.7, "timeout": 20, "timestamp": 1748090484758, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "pr9o8Zsm5p", "executionTime": "3838ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"<PERSON>move\")]", "method": "locator", "timeout": 10, "timestamp": 1746102873316, "type": "tap"}, {"action_id": "8umPSX0vrr", "executionTime": "2090ms", "image_filename": "banner-close-updated.png", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1746704751098, "type": "tap"}, {"action_id": "k3mu9Mt7Ec", "executionTime": "3925ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1746100354856, "type": "tap"}, {"action_id": "Ob26qqcA0p", "count": 2, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "7597ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtSign out\"]", "start_x": 50, "start_y": 70, "threshold": 0.7, "timeout": 20, "timestamp": 1746100402404, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "OyUowAaBzD", "executionTime": "3486ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtSign out\"]", "method": "locator", "timeout": 10, "timestamp": 1746100379226, "type": "tap"}, {"action_id": "cKNu2QoRC1", "executionTime": "3689ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]", "method": "locator", "timeout": 20, "timestamp": 1746426742221, "type": "tap"}, {"action_id": "aI4Cfo88Pv", "expanded": false, "test_case_id": "Delivery__Buy_20250505165058.json", "test_case_name": "Delivery  Buy", "test_case_steps_count": 34, "timestamp": 1750158297873, "type": "multiStep"}, {"type": "cleanupSteps", "timestamp": 1750975736777, "test_case_id": "Kmart_AU_Cleanup_20250626204013.json", "test_case_name": "Kmart_AU_Cleanup", "test_case_steps_count": 0, "action_id": "7Mk9sdbHip"}], "labels": [], "updated": "2025-06-27 08:08:59"}