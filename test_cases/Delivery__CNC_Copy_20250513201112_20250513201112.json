{"name": "Delivery & CNC- NZ", "created": "2025-06-16 11:59:02", "device_id": "********-00186C801E13C01E", "actions": [{"action_id": "HotUJOd6oB", "executionTime": "2446ms", "package_id": "env[appid]", "timestamp": *************, "type": "restartApp"}, {"action_id": "rkL0oz4kiL", "executionTime": "7535ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "txtHomeAccountCtaSignIn", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "yUJyVO5Wev", "executionTime": "1233ms", "function_name": "alert_accept", "timestamp": *************, "type": "iosFunctions"}, {"action_id": "3caMBvQX7k", "executionTime": "2919ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Email\"]", "timeout": 30, "timestamp": *************, "type": "waitTill"}, {"type": "multiStep", "timestamp": *************, "test_case_id": "KmartSignin_Copy_20250513181428_20250513181428.json", "test_case_name": "Kmart-NZ-Signin", "test_case_steps_count": 6, "expanded": false, "action_id": "SVt620PG1t"}, {"action_id": "rqLJpAP0mA", "executionTime": "1785ms", "image_filename": "homepage-search-se.png", "method": "image", "threshold": 0.7, "timeout": 30, "timestamp": *************, "type": "tapOnText", "text_to_find": "Find", "double_tap": false}, {"action_id": "13YG4jrM9E", "enter": true, "executionTime": "3212ms", "function_name": "text", "text": "P_43250042", "timestamp": 1747131473521, "type": "iosFunctions"}, {"action_id": "nAB6Q8LAdv", "executionTime": "2415ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Filter\"]", "timeout": 30, "timestamp": 1745485041367, "type": "waitTill"}, {"action_id": "FnrbyHq7bU", "executionTime": "3092ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::*[1]//XCUIElementTypeButton)[1]", "method": "locator", "timeout": 10, "timestamp": 1745485077480, "type": "tap"}, {"action_id": "jY0oPjKbuS", "executionTime": "1984ms", "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "timeout": 10, "timestamp": 1746102129685, "type": "exists"}, {"action_id": "F1olhgKhUt", "executionTime": "3124ms", "image_filename": "bag1-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1746100706297, "type": "tap"}, {"action_id": "uM5FOSrU5U", "executionTime": "2546ms", "locator_type": "image", "locator_value": "cnc-tab-se.png", "timeout": 10, "timestamp": 1746102183980, "type": "exists"}, {"action_id": "qjj0i3rcUh", "executionTime": "1833ms", "image_filename": "cnc-tab-se.png", "method": "image", "threshold": 0.7, "timeout": 30, "timestamp": 1746100742115, "type": "tapOnText", "text_to_find": "Collect", "double_tap": false}, {"action_id": "Qbg9bipTGs", "count": 1, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "3919ms", "interval": 0.5, "locator_type": "accessibilityid", "locator_value": "Remove UNO Card Game", "start_x": 50, "start_y": 70, "threshold": 0.7, "timeout": 20, "timestamp": 1746102698568, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "pr9o8Zsm5p", "executionTime": "15378ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"<PERSON>move\")]", "method": "locator", "timeout": 10, "timestamp": 1746102873316, "type": "tap"}, {"action_id": "8umPSX0vrr", "executionTime": "1733ms", "image_filename": "banner-close-updated.png", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1746704751098, "type": "tap"}, {"action_id": "k3mu9Mt7Ec", "executionTime": "3139ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1746100354856, "type": "tap"}, {"action_id": "Ob26qqcA0p", "count": 2, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "6814ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtSign out\"]", "start_x": 50, "start_y": 70, "threshold": 0.7, "timeout": 20, "timestamp": 1746100402404, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "OyUowAaBzD", "executionTime": "3003ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtSign out\"]", "method": "locator", "timeout": 10, "timestamp": 1746100379226, "type": "tap"}, {"action_id": "cKNu2QoRC1", "executionTime": "3171ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]", "method": "locator", "timeout": 20, "timestamp": 1746426742221, "type": "tap"}, {"action_id": "rqLJpAP0mA", "executionTime": "2130ms", "image_filename": "homepage-search-se.png", "method": "image", "threshold": 0.7, "timeout": 30, "timestamp": 1747132282219, "type": "tapOnText", "text_to_find": "Find", "double_tap": false}, {"action_id": "13YG4jrM9E", "enter": true, "executionTime": "3219ms", "function_name": "text", "text": "P_43250042", "timestamp": 1747132294490, "type": "iosFunctions"}, {"action_id": "nAB6Q8LAdv", "executionTime": "2496ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Filter\"]", "timeout": 30, "timestamp": 1747132311829, "type": "waitTill"}, {"action_id": "FnrbyHq7bU", "executionTime": "3113ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::*[1]//XCUIElementTypeButton)[1]", "method": "locator", "timeout": 10, "timestamp": 1747132328659, "type": "tap"}, {"action_id": "jY0oPjKbuS", "executionTime": "3020ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "method": "locator", "timeout": 20, "timestamp": 1747132339669, "type": "tap"}, {"type": "multiStep", "timestamp": 1750039036122, "test_case_id": "Delivery_Buy_Steps_Copy_20250513195816_20250513195816.json", "test_case_name": "Delivery Buy Step NZ", "test_case_steps_count": 33, "expanded": false, "action_id": "MuX1dfl3aB"}], "labels": [], "updated": "2025-06-16 11:59:02", "test_case_id": "tc_d4520def5806"}