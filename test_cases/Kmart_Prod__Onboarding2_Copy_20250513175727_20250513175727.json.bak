{"name": "Kmart NZ Onboarding", "created": "2025-05-13 18:06:47", "device_id": "00008030-00020C123E60402E", "actions": [{"action_id": "UaInjZvYnF", "package_id": "com.apple.TestFlight", "timestamp": 1745984719978, "type": "terminateApp"}, {"action_id": "3IFuXit8Zu", "package_id": "nz.com.kmart", "timestamp": 1745292775600, "type": "uninstallApp"}, {"action_id": "Qt0hAlg3mH", "package_id": "com.apple.TestFlight", "timestamp": 1745292807279, "type": "launchApp"}, {"action_id": "YmiPg9cEeJ", "image_filename": "kmartau-install-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeOther[@name=\"6740797708\"]/XCUIElementTypeButton/XCUIElementTypeButton", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1745672870365, "type": "tap"}, {"action_id": "ktysmyM2xx", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeOther[@name=\"6740797708\"]/XCUIElementTypeButton/XCUIElementTypeButton[@name=\"Open\"]", "method": "locator", "timeout": 30, "timestamp": 1745292852008, "type": "waitTill"}, {"action_id": "CIa1yzRquQ", "duration": 15, "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeOther[@name=\"6740797708\"]/XCUIElementTypeButton/XCUIElementTypeButton[@name=\"Open\"]", "method": "locator", "time": 15, "timeout": 10, "timestamp": 1745292879370, "type": "tap"}, {"action_id": "6BlY5jNebw", "duration": 5, "package_id": "nz.com.kmart", "time": 5, "timestamp": 1745293018633, "type": "restartApp"}, {"action_id": "YLdCGXoOgw", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"btnOnboardingScreenActionButton\"]", "method": "locator", "timeout": 10, "timestamp": 1745293083992, "type": "tap"}, {"action_id": "dRt8f23ykA", "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Allow\"]", "timeout": 10, "timestamp": 1745293134273, "type": "exists"}, {"action_id": "rTEfpjy5eR", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Allow\"]", "method": "locator", "timeout": 10, "timestamp": 1745293198430, "type": "tap"}, {"action_id": "w6Zy5PcOTF", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"btnOnboardingScreenActionButton\"]", "method": "locator", "timeout": 30, "timestamp": *************, "type": "tapOnText", "text_to_find": "manually", "double_tap": false}, {"action_id": "Cao4NMFQi9", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"btnMayBeLater\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "ChqObopqO6", "duration": 15, "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "txtHomeAccountCtaSignIn", "time": 15, "timeout": 60, "timestamp": *************, "type": "waitTill"}, {"action_id": "LcOZtL3ShH", "locator_type": "xpath", "locator_value": "//XCUIElementTypeOther[@name=\"Shop Sylvia Park Nz  Deliver 1010 Edit\"]", "timeout": 10, "timestamp": *************, "type": "exists"}, {"action_id": "wE3h8Kb2Nr", "image_filename": "account-tab.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": *************, "type": "tap", "x": 355, "y": 788}, {"action_id": "JVqYBLEyG7", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtPush Notifications\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "UvgW3dYtsG", "locator_type": "xpath", "locator_value": "//XCUIElementTypeOther[@name=\"Allow notifications\"]", "timeout": 10, "timestamp": *************, "type": "exists"}], "updated": "2025-05-13 18:06:47"}