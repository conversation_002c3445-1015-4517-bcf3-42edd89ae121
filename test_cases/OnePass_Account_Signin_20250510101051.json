{"name": "OnePass Account Signin", "created": "2025-06-08 22:14:07", "device_id": "********-00020C123E60402E", "actions": [{"action_id": "RCYxT9YD8u", "executionTime": "5372ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Email\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "uOie3uMGU2", "function_name": "text", "text": "env[op-uname]", "enter": true, "timestamp": *************, "type": "iosFunctions"}, {"action_id": "K7yV3GGsgr", "executionTime": "5184ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeSecureTextField[@name=\"Password\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"type": "iosFunctions", "timestamp": *************, "function_name": "text", "text": "env[op-pwd]", "enter": true, "action_id": "7maa5AKju8"}, {"action_id": "tH68tAo0l0", "executionTime": "3414ms", "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]", "timeout": 15, "timestamp": 1745666406257, "type": "exists"}], "labels": [], "updated": "2025-06-08 22:14:07", "test_case_id": "tc_43f36189a589"}