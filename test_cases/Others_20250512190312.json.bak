{"name": "Others", "created": "2025-06-27 16:09:41", "device_id": "00008120-00186C801E13C01E", "actions": [{"action_id": "H9fy9qcFbZ", "executionTime": "3238ms", "package_id": "env[appid]", "timestamp": 1746597492636, "type": "restartApp"}, {"action_id": "Dzn2Q7JTe0", "executionTime": "2422ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"btnBarcodeScanner\"]", "method": "locator", "timeout": 20, "timestamp": 1747038761695, "type": "tap"}, {"action_id": "RlDZFks4Lc", "executionTime": "477ms", "function_name": "alert_accept", "timestamp": 1747038830836, "type": "iosFunctions"}, {"action_id": "F4NGh9HrLw", "executionTime": "1282ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeOther[@name=\"Barcode Scanner\"]", "method": "locator", "timeout": 10, "timestamp": 1746830724911, "type": "exists"}, {"action_id": "RbNtEW6N9T", "executionTime": "1297ms", "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"imgHelp\"]", "text_to_find": "Living", "timeout": 10, "timestamp": 1746830828429, "type": "exists"}, {"action_id": "xUbWFa8Ok2", "double_tap": false, "executionTime": "1311ms", "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"txtPosition barcode lengthwise within rectangle frame to view helpful product information\"]", "text_to_find": "Shop", "timeout": 10, "timestamp": 1746830873534, "type": "exists"}, {"action_id": "74XW7x54ad", "executionTime": "2644ms", "image_filename": "env[device-back-img]", "method": "image", "threshold": 0.7, "timeout": 30, "timestamp": 1749457134837, "type": "tap"}, {"action_id": "F4NGh9HrLw", "executionTime": "2376ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1746833833911, "type": "tap"}, {"action_id": "eHLWiRoqqS", "count": 1, "direction": "up", "double_tap": false, "duration": 300, "end_x": 50, "end_y": 30, "executionTime": "2248ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtTrack My Order\"]", "method": "locator", "start_x": 50, "start_y": 70, "text_to_find": "Track", "timeout": 10, "timestamp": 1746835476969, "type": "tap", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "OmKfD9iBjD", "double_tap": false, "executionTime": "2223ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Order number\"]", "method": "locator", "text_to_find": "Order", "timeout": 10, "timestamp": 1746835134218, "type": "tap"}, {"action_id": "7YbjwQH1Jc", "executionTime": "1576ms", "text": "env[searchorder]", "timestamp": 1747039240064, "type": "text"}, {"action_id": "kAQ1yIIw3h", "executionTime": "2358ms", "fallback_type": "coordinates", "fallback_x": 98, "fallback_y": 308, "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Email Address\"]", "method": "locator", "timeout": 30, "timestamp": 1746831616342, "type": "tap"}, {"action_id": "XJv08Gkucs", "executionTime": "1762ms", "text": "env[uname-op]", "timestamp": 1747039307941, "type": "text"}, {"action_id": "aNN0yYFLEd", "executionTime": "2355ms", "function_name": "text", "image_filename": "prodcut-share-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Search for order\"]", "method": "locator", "threshold": 0.7, "timeout": 30, "timestamp": 1746831868369, "type": "tap"}, {"action_id": "83tV9A4NOn", "executionTime": "1453ms", "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"refunded\"]", "timeout": 20, "timestamp": *************, "type": "exists"}, {"action_id": "VJJ3EXXotU", "executionTime": "2198ms", "image_filename": "env[device-back-img]", "method": "image", "threshold": 0.7, "timeout": 30, "timestamp": *************, "type": "tap"}, {"action_id": "gekNSY5O2E", "double_tap": false, "executionTime": "2218ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtMoreAccountCtaSignIn\"]", "method": "locator", "text_to_find": "Sign", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "s0WyiD1w0B", "executionTime": "1118ms", "function_name": "alert_accept", "image_filename": "banner-close-updated.png", "method": "image", "threshold": 0.7, "timeout": 30, "timestamp": *************, "type": "iosFunctions"}, {"action_id": "u928vFzSni", "executionTime": "2625ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Email\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "pe9W6tZdXT", "enter": true, "executionTime": "3141ms", "function_name": "text", "text": "env[uname-op]", "timestamp": *************, "type": "iosFunctions"}, {"action_id": "d6vTfR4Y0D", "executionTime": "2599ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeSecureTextField[@name=\"Password\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "kWPRvuo7kk", "enter": true, "executionTime": "2908ms", "function_name": "text", "text": "env[pwd-op]", "timestamp": *************, "type": "iosFunctions"}, {"action_id": "ShJSdXvmVL", "count": 3, "direction": "up", "duration": 300, "end_x": 50, "end_y": 30, "executionTime": "6633ms", "interval": 1, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtMy OnePass Account\"]", "start_x": 50, "start_y": 70, "timeout": 20, "timestamp": *************, "type": "exists", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "pk2DLZFBmx", "executionTime": "2294ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtMy OnePass Account\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "DhWa2PCBXE", "executionTime": "1352ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"txtOnePassSubscritionBox\"]", "method": "locator", "timeout": 20, "timestamp": *************, "type": "exists"}, {"action_id": "GEMv6goQtW", "executionTime": "2227ms", "image_filename": "env[device-back-img]", "method": "image", "threshold": 0.7, "timeout": 30, "timestamp": *************, "type": "tap"}, {"action_id": "inrxgdWzXr", "double_tap": false, "executionTime": "2943ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "Learn more about <PERSON><PERSON>", "method": "locator", "text_to_find": "receipts", "timeout": 30, "timestamp": *************, "type": "tapOnText"}, {"action_id": "inrxgdWzXr", "double_tap": false, "executionTime": "3146ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "Learn more about <PERSON><PERSON>", "method": "locator", "text_to_find": "Store", "timeout": 30, "timestamp": 1747039844368, "type": "tapOnText"}, {"action_id": "P4b2BITpCf", "executionTime": "1535ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//XCUIElementTypeOther[contains(@name,\"Store receipts\")]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther//XCUIElementTypeLink)[1]", "method": "locator", "timeout": 20, "timestamp": 1746832583435, "type": "exists"}, {"action_id": "zdh8hKYC1a", "executionTime": "2470ms", "image_filename": "deviceback-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//XCUIElementTypeOther[contains(@name,\"Store receipts\")]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther//XCUIElementTypeLink)[1]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1746832601515, "type": "tap"}, {"action_id": "q6cKxgMAIn", "executionTime": "1951ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeOther[@name=\"<PERSON><PERSON><PERSON> Receipt\"]/XCUIElementTypeOther[2]", "method": "locator", "timeout": 20, "timestamp": 1746832657816, "type": "exists"}, {"action_id": "XjclKOaCTh", "executionTime": "3131ms", "image_filename": "keyboard_done_iphoneSE.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"<PERSON>\"]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1746832702071, "type": "tap"}, {"action_id": "U48qCNydwd", "executionTime": "3239ms", "package_id": "env[appid]", "timestamp": 1747042054721, "type": "restartApp"}, {"action_id": "UoH0wdtcLk", "double_tap": false, "executionTime": "3790ms", "image_filename": "homepage_editbtn-se.png", "method": "image", "text_to_find": "Edit", "threshold": 0.7, "timeout": 30, "timestamp": 1747042435305, "type": "tapOnText"}, {"action_id": "jmKjclMUWT", "executionTime": "3116ms", "text_to_find": "current", "timeout": 30, "timestamp": 1747042463732, "type": "tapOnText"}, {"action_id": "5hClb2pKKx", "condition": {"locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"btnUpdate\"]", "timeout": 20}, "condition_type": "exists", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"btnUpdate\"]", "method": "locator", "then_action": {"locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"btnUpdate\"]", "method": "locator", "timeout": 20, "type": "tap"}, "timeout": 10, "timestamp": 1747047863724, "type": "ifElseSteps"}, {"action_id": "tWq2Qzn22D", "executionTime": "2247ms", "image_filename": "env[device-back-img]", "method": "image", "threshold": 0.7, "timeout": 30, "timestamp": 1749457147347, "type": "tap"}, {"action_id": "Jtn2FK4THX", "double_tap": false, "executionTime": "4007ms", "image_filename": "homepage-search-se.png", "method": "image", "text_to_find": "Find", "threshold": 0.7, "timeout": 30, "timestamp": 1747042224985, "type": "tapOnText"}, {"action_id": "qG4RkNac30", "enter": true, "executionTime": "2532ms", "function_name": "text", "text": "P_42691341", "timestamp": 1749110826538, "type": "iosFunctions"}, {"action_id": "8XWyF2kgwW", "executionTime": "1857ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Filter\"]", "timeout": 40, "timestamp": 1748149052897, "type": "waitTill"}, {"action_id": "CcFsA41sKp", "executionTime": "2360ms", "image_filename": "quickadd-to-bag-btn.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1748088918053, "type": "tap"}, {"action_id": "O8XvoFFGEB", "executionTime": "2345ms", "image_filename": "env[atg-pdp]", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1749460096609, "type": "tap"}, {"action_id": "4eEEGs1x8i", "condition": {"locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Save my location\"]", "timeout": 10}, "condition_type": "exists", "executionTime": "10382ms", "then_action": {"locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Save my location\"]", "method": "locator", "timeout": 10, "type": "tap"}, "timestamp": 1747042562636, "type": "ifElseSteps"}, {"action_id": "U48qCNydwd", "executionTime": "3251ms", "package_id": "env[appid]", "timestamp": 1747046318725, "type": "restartApp"}, {"action_id": "F4NGh9HrLw", "executionTime": "2510ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1747042715213, "type": "tap"}, {"action_id": "ZxObWodIp8", "expanded": false, "test_case_id": "Delivery_Buy_Steps_20250512194232.json", "test_case_name": "Delivery Buy Steps", "test_case_steps_count": 41, "timestamp": 1749458010692, "type": "multiStep"}, {"action_id": "F4NGh9HrLw", "executionTime": "2388ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1747041764396, "type": "tap"}, {"action_id": "qHdMgerbTE", "count": 1, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "3085ms", "interval": 0.5, "start_x": 50, "start_y": 70, "timestamp": 1748313766307, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "2p13JoJbbA", "executionTime": "2286ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtSign out\"]", "method": "locator", "timeout": 10, "timestamp": 1746834873588, "type": "tap"}, {"action_id": "x4yLCZHaCR", "executionTime": "1066ms", "package_id": "env[appid]", "timestamp": 1746834909467, "type": "terminateApp"}, {"type": "cleanupSteps", "timestamp": 1751004578210, "test_case_id": "Kmart_AU_Cleanup_20250626204013.json", "test_case_name": "Kmart_AU_Cleanup", "test_case_steps_count": 0, "action_id": "OMgc2gHHyq"}], "labels": [], "updated": "2025-06-27 16:09:41"}