{"name": "Postcode Flow", "created": "2025-06-20 09:58:24", "device_id": null, "actions": [{"action_id": "H9fy9qcFbZ", "executionTime": "3237ms", "package_id": "env[appid]", "timestamp": *************, "type": "restartApp"}, {"action_id": "Y8vz7AJD1i", "executionTime": "5294ms", "fallback_locators": [{"locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtHomeAccountCtaSignIn\"]"}], "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "txtHomeAccountCtaSignIn", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "2xC5fLfLe8", "executionTime": "1152ms", "function_name": "alert_accept", "timestamp": *************, "type": "iosFunctions"}, {"action_id": "Azb1flbIJJ", "executionTime": "2090ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Email\"]", "timeout": 30, "timestamp": *************, "type": "waitTill"}, {"action_id": "C3UHsKxa5P", "expanded": false, "test_case_id": "KmartProdSignin_Copy_20250501144222_20250501144222.json", "test_case_name": "Kmart-Signin", "test_case_steps_count": 6, "timestamp": *************, "type": "multiStep"}, {"action_id": "m0956RsrdM", "executionTime": "1877ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeOther[contains(@name,\"Deliver\")]", "timeout": 20, "timestamp": *************, "type": "waitTill"}, {"action_id": "QMXBlswP6H", "double_tap": false, "executionTime": "2392ms", "image_filename": "homepage_editbtn-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeOther[contains(@name,\"Deliver\")]", "method": "image", "text_to_find": "Edit", "threshold": 0.7, "timeout": 20, "timestamp": 1746143899898, "type": "tap"}, {"action_id": "8WCusTZ8q9", "executionTime": "3978ms", "fallback_locators": [{"locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Search suburb or postcode\"]"}], "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "Search suburb or postcode", "method": "locator", "timeout": 10, "timestamp": 1746144003528, "type": "tap"}, {"action_id": "kbdEPCPYod", "delay": 500, "executionTime": "3232ms", "text": "HAYMARKET", "timestamp": 1746144035427, "type": "textClear"}, {"action_id": "mw9GQ4mzRE", "double_tap": false, "executionTime": "3281ms", "image_filename": "HAYMARKET-SE.png", "method": "image", "text_to_find": "2000", "threshold": 0.7, "timeout": 30, "timestamp": 1746144235322, "type": "tapOnText"}, {"action_id": "Sl6eiqZkRm", "executionTime": "3035ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "btnSaveOrContinue", "timeout": 20, "timestamp": 1746608048465, "type": "waitTill"}, {"action_id": "E2jpN7BioW", "double_tap": false, "executionTime": "3200ms", "text_to_find": "Save", "timeout": 30, "timestamp": 1746144262142, "type": "tapOnText"}, {"action_id": "Xqj9EIVEfg", "condition": {"locator_type": "accessibility_id", "locator_value": "btnUpdate", "timeout": 10}, "condition_type": "exists", "executionTime": "5053ms", "then_action": {"locator_type": "accessibility_id", "locator_value": "btnUpdate", "timeout": 10, "type": "clickElement"}, "timestamp": 1746144409595, "type": "ifElseSteps"}, {"action_id": "70iOOakiG7", "double_tap": false, "executionTime": "3999ms", "image_filename": "homepage-search-se.png", "method": "image", "text_to_find": "Find", "threshold": 0.7, "timeout": 30, "timestamp": 1745980305734, "type": "tapOnText"}, {"action_id": "IupxLP2Jsr", "executionTime": "2550ms", "function_name": "text", "text": "Uno card", "timestamp": 1745484826180, "type": "iosFunctions"}, {"action_id": "C6JHhLdWTv", "executionTime": "2019ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Filter\"]", "timeout": 10, "timestamp": 1745485041367, "type": "waitTill"}, {"action_id": "VkUKQbf1Qt", "double_tap": false, "executionTime": "3938ms", "image_filename": "homepage_editbtn-se.png", "method": "image", "text_to_find": "Edit", "threshold": 0.7, "timeout": 30, "timestamp": 1746144823467, "type": "tapOnText"}, {"action_id": "kiM0WyWE9I", "executionTime": "3106ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "btnCurrentLocationButton", "method": "locator", "timeout": 20, "timestamp": 1746144885596, "type": "waitTill"}, {"action_id": "GYRHQr7TWx", "double_tap": false, "executionTime": "3154ms", "text_to_find": "current", "timeout": 30, "timestamp": 1746608514468, "type": "tapOnText"}, {"action_id": "M3dXqigqRv", "executionTime": "3095ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "btnSaveOrContinue", "timeout": 20, "timestamp": 1746608171409, "type": "waitTill"}, {"action_id": "pKjXoj4mNg", "double_tap": false, "executionTime": "3155ms", "text_to_find": "Save", "timeout": 30, "timestamp": 1746144919296, "type": "tapOnText"}, {"action_id": "73NABkfWyY", "executionTime": "3772ms", "locator_type": "text", "locator_value": "Sanctuary", "timeout": 20, "timestamp": 1746145022497, "type": "exists"}, {"action_id": "foVGMl9wvu", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "timeout": 10, "timestamp": 1750340203147, "type": "waitTill"}, {"action_id": "trBISwJ8eZ", "executionTime": "2591ms", "image_filename": "Unocard-product-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1746145054848, "type": "tap"}, {"action_id": "letbbewlnA", "executionTime": "2306ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"SKU :\"]", "timeout": 20, "timestamp": 1746608939366, "type": "waitTill"}, {"action_id": "lnjoz8hHUU", "double_tap": false, "executionTime": "3896ms", "image_filename": "homepage_editbtn-se.png", "method": "image", "text_to_find": "Edit", "threshold": 0.7, "timeout": 30, "timestamp": 1746145090019, "type": "tapOnText"}, {"action_id": "WmNWcsWVHv", "executionTime": "3934ms", "fallback_locators": [{"locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Search suburb or postcode\"]"}], "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "Search suburb or postcode", "method": "locator", "timeout": 10, "timestamp": 1746145246389, "type": "tap"}, {"action_id": "uZHvvAzVfx", "delay": 500, "executionTime": "3232ms", "text": "HAYMARKET", "timestamp": 1746145223768, "type": "textClear"}, {"action_id": "H0ODFz7sWJ", "double_tap": false, "executionTime": "3311ms", "image_filename": "HAYMARKET-SE.png", "method": "image", "text_to_find": "2000", "threshold": 0.7, "timeout": 30, "timestamp": 1746145274227, "type": "tapOnText"}, {"action_id": "hr0IVckpYI", "executionTime": "3081ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "btnSaveOrContinue", "timeout": 20, "timestamp": 1746608116661, "type": "waitTill"}, {"action_id": "ORI6ZFMBK1", "double_tap": false, "executionTime": "3169ms", "text_to_find": "Save", "timeout": 30, "timestamp": 1746145295421, "type": "tapOnText"}, {"action_id": "eRCmRhc3re", "executionTime": "3719ms", "locator_type": "text", "locator_value": "Broadway", "timeout": 20, "timestamp": 1746145322752, "type": "exists"}, {"action_id": "Jf2wJyOphY", "executionTime": "5505ms", "fallback_locators": [{"locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Add to bag\"]"}], "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "Add to bag", "method": "locator", "timeout": 10, "timestamp": 1746145623307, "type": "tap"}, {"action_id": "q8oldD8uZt", "executionTime": "1816ms", "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "timeout": 10, "timestamp": 1746145659635, "type": "exists"}, {"action_id": "94ikwhIEE2", "executionTime": "2811ms", "image_filename": "bag1-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1746145693144, "type": "tap"}, {"action_id": "dF3hpprg71", "executionTime": "1689ms", "image_filename": "cnc-tab-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeOther[@name=\"Delivery options\"]/XCUIElementTypeButton[3]", "threshold": 0.7, "timeout": 10, "timestamp": 1746170955302, "type": "waitTill"}, {"action_id": "3gJsiap2Ds", "executionTime": "2386ms", "image_filename": "cnc-tab-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Click & Collect\"]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1746169580270, "type": "tap"}, {"action_id": "uArzgeZYf7", "executionTime": "1897ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeOther[contains(@value,\"Nearby suburb or postcode\")]", "method": "locator", "timeout": 20, "timestamp": 1746609385619, "type": "waitTill"}, {"action_id": "G4A3KBlXHq", "executionTime": "3501ms", "text_to_find": "Nearby", "timeout": 30, "timestamp": 1746145831229, "type": "tapOnText"}, {"action_id": "QpBLC6BStn", "executionTime": "4551ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "delete", "method": "locator", "timeout": 10, "timestamp": 1746145953013, "type": "tap"}, {"action_id": "Wld5Urg70o", "executionTime": "5221ms", "method": "coordinates", "text": "3000", "timeout": 15, "timestamp": 1749463807799, "type": "tapAndType", "x": "env[delivery-addr-x]", "y": "env[delivery-addr-y]"}, {"action_id": "ZWpYNcpbFA", "double_tap": false, "executionTime": "26578ms", "image_filename": "MELBOURNE_SE.png", "method": "image", "text_to_find": "VIC", "threshold": 0.7, "timeout": 30, "timestamp": 1746146079046, "type": "tapOnText"}, {"action_id": "bkU728TrRF", "executionTime": "13544ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "Done", "method": "locator", "timeout": 10, "timestamp": 1746146113726, "type": "tap"}, {"action_id": "s8h8VDUIOC", "count": 1, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "3273ms", "interval": 0.5, "locator_type": "accessibilityid", "locator_value": "Remove UNO Card Game", "start_x": 50, "start_y": 70, "threshold": 0.7, "timeout": 20, "timestamp": 1746146260557, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "eVytJrry9x", "executionTime": "2640ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"<PERSON>move\")]", "method": "locator", "timeout": 10, "timestamp": 1746146287245, "type": "tap"}, {"action_id": "I4gwigwXSj", "executionTime": "1761ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"Continue shopping\"]", "timeout": 30, "timestamp": 1746592230567, "type": "waitTill"}, {"action_id": "Tebej51pT2", "executionTime": "2221ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"Continue shopping\"]", "method": "locator", "timeout": 10, "timestamp": 1746146348304, "type": "tap"}, {"action_id": "0f2FSZYjWq", "executionTime": "3091ms", "locator_type": "text", "locator_value": "Melbourne", "timeout": 20, "timestamp": 1746146563188, "type": "exists"}, {"action_id": "F4NGh9HrLw", "executionTime": "2830ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1746146617728, "type": "tap"}, {"action_id": "mWeLQtXiL6", "count": 2, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "6039ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtSign out\"]", "start_x": 50, "start_y": 70, "threshold": 0.7, "timeout": 20, "timestamp": 1746146644650, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "xyHVihJMBi", "executionTime": "2264ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtSign out\"]", "method": "locator", "timeout": 10, "timestamp": 1746146665049, "type": "tap"}, {"action_id": "X3LJ5j4T7e", "hook_data": {"image_filename": "banner-close-updated.png", "log_details": "Image: banner-close-updated.png", "method": "image", "threshold": 0.7, "timeout": 20}, "hook_type": "tap", "timestamp": 1746798035285, "type": "hookAction"}], "labels": [], "updated": "2025-06-20 09:58:24"}