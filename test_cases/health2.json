{"name": "health2", "description": "Test case for Apple Health app", "created": "2025-06-17T14:00:00.000Z", "updated": "2025-06-17T14:00:00.000Z", "labels": ["testing", "labels"], "actions": [{"type": "launchApp", "action_id": "UppP3ZuqY6", "name": "Launch app: com.apple.Health", "bundleId": "com.apple.Health", "package": "com.apple.Health", "timeout": 10}, {"type": "addLog", "action_id": "Successful", "name": "Add Log: Launched App Successfully (with screenshot)", "message": "Launched App Successfully", "take_screenshot": true}, {"type": "clickElement", "action_id": "XCUIElemen", "name": "Click element: xpath=//XCUIElementTypeStaticText[@name=\"Edit\"]", "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"Edit\"]", "timeout": 10}, {"type": "addLog", "action_id": "screenshot", "name": "Add Log: Edit link is clicked (with screenshot)", "message": "Edit link is clicked", "take_screenshot": true}, {"type": "clickElement", "action_id": "XCUIElemen2", "name": "Click element: xpath= //XCUIElementTypeButton[@name=\"Done\"]", "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"<PERSON>\"]", "timeout": 10}, {"type": "addLog", "action_id": "screenshot2", "name": "Add Log: Done link is clicked (with screenshot)", "message": "Done link is clicked", "take_screenshot": true}, {"type": "wait", "action_id": "ag29wsBP24", "name": "Wait for 1000 ms", "duration": 1000}, {"type": "terminateApp", "action_id": "vjBGuN5y9x", "name": "Terminate app: com.apple.Health", "bundleId": "com.apple.Health", "package": "com.apple.Health", "timeout": 10}, {"type": "addLog", "action_id": "screenshot3", "name": "Add Log: App is closed (with screenshot)", "message": "App is closed", "take_screenshot": true}], "test_case_id": "tc_21812abd58dd"}