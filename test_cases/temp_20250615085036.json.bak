{"name": "AU - Performance", "created": "2025-06-27 08:07:29", "device_id": "00008120-00186C801E13C01E", "actions": [{"action_id": "OR0SKKnFxy", "executionTime": "3368ms", "package_id": "env[appid]", "timestamp": 1749945555174, "type": "restartApp"}, {"action_id": "SqDiBhmyOG", "executionTime": "2446ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1749943707923, "type": "tap"}, {"action_id": "g17Boaefhg", "executionTime": "3025ms", "text_to_find": "Help", "timeout": 30, "timestamp": 1749944026170, "type": "tapOnText"}, {"action_id": "DhFJzlme9K", "double_tap": false, "executionTime": "2761ms", "text_to_find": "FAQ", "timeout": 30, "timestamp": 1749944058180, "type": "tapOnText"}, {"action_id": "t6L5vWfBYM", "count": 6, "direction": "right", "duration": 300, "end_x": 50, "end_y": 30, "executionTime": "27395ms", "interval": 0.5, "start_x": 50, "start_y": 70, "timestamp": 1749944110469, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "I0tM87Yjhc", "executionTime": "3420ms", "text_to_find": "click", "timeout": 30, "timestamp": 1749944178932, "type": "tapOnText"}, {"action_id": "MTRbUlaRvI", "executionTime": "3044ms", "text_to_find": "1800", "timeout": 30, "timestamp": 1749944204798, "type": "tapOnText"}, {"action_id": "RHEU77LRMw", "double_tap": false, "executionTime": "2766ms", "text_to_find": "+61", "timeout": 30, "timestamp": 1749944260788, "type": "tapOnText"}, {"action_id": "VqSa9z9R2Q", "executionTime": "110ms", "package_id": "env[appid]", "timestamp": 1749944381353, "type": "launchApp"}, {"action_id": "7xs3GiydGF", "executionTime": "2224ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1749944408274, "type": "tap"}, {"action_id": "6G6P3UE7Uy", "executionTime": "4264ms", "text_to_find": "Find", "timeout": 30, "timestamp": 1749944417423, "type": "tapOnText"}, {"action_id": "IL6kON0uQ9", "enter": true, "executionTime": "2552ms", "function_name": "text", "text": "kids toys", "timestamp": 1749944478911, "type": "iosFunctions"}, {"action_id": "aqs7O0Yq2p", "expanded": false, "test_case_id": "Click_Paginations_20250611234450.json", "test_case_name": "Click_Paginations", "test_case_steps_count": 10, "timestamp": 1749954595333, "type": "multiStep"}, {"action_id": "5e4LeoW1YU", "executionTime": "3483ms", "package_id": "env[appid]", "timestamp": 1749958538246, "type": "restartApp"}, {"action_id": "KyyS139agr", "executionTime": "6169ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 2 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1749958565710, "type": "tap"}, {"action_id": "zNRPvs2cC4", "executionTime": "3207ms", "text_to_find": "Toys", "timeout": 30, "timestamp": 1749958583078, "type": "tapOnText"}, {"action_id": "eGQ7VrKUSo", "executionTime": "2943ms", "text_to_find": "Age", "timeout": 30, "timestamp": 1749958601936, "type": "tapOnText"}, {"action_id": "dYEtjrv6lz", "executionTime": "2704ms", "text_to_find": "Months", "timeout": 30, "timestamp": 1749958624780, "type": "tapOnText"}, {"action_id": "NkybTKfs2U", "count": 1, "direction": "up", "duration": 2000, "end_x": 90, "end_y": 50, "executionTime": "7689ms", "interval": 0.5, "start_x": 5, "start_y": 50, "timestamp": 1749958806016, "type": "swipe", "vector_end": [0.9, 0.5], "vector_start": [0.05, 0.5]}, {"action_id": "To7bij5MnF", "count": 1, "direction": "up", "duration": 2000, "end_x": 90, "end_y": 50, "executionTime": "4342ms", "interval": 0.5, "start_x": 5, "start_y": 50, "timestamp": *************, "type": "swipe", "vector_end": [0.9, 0.5], "vector_start": [0.05, 0.5]}, {"action_id": "WEB5St2Mb7", "executionTime": "2279ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "SPE01N6pyp", "executionTime": "5696ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "txtHomeAccountCtaSignIn", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "kQJbqm7uCi", "executionTime": "1333ms", "function_name": "alert_accept", "timestamp": *************, "type": "iosFunctions"}, {"action_id": "TV4kJIIV9v", "executionTime": "4189ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Email\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "OUT2ASweb6", "enter": true, "executionTime": "3162ms", "function_name": "text", "text": "env[uname]", "timestamp": *************, "type": "iosFunctions"}, {"action_id": "7g2LmvjtEZ", "executionTime": "2668ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeSecureTextField[@name=\"Password\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "qkZ5KShdEU", "enter": true, "executionTime": "3030ms", "function_name": "text", "text": "env[pwd]", "timestamp": 1749959325022, "type": "iosFunctions"}, {"action_id": "GTXmST3hEA", "executionTime": "1541ms", "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]", "timeout": 30, "timestamp": 1749959340082, "type": "exists"}, {"action_id": "ewuLtuqVuo", "executionTime": "4229ms", "text_to_find": "Find", "timeout": 30, "timestamp": 1749959489189, "type": "tapOnText"}, {"action_id": "RuPGkdCdah", "enter": true, "executionTime": "2780ms", "function_name": "text", "text": "enn[cooker-id]", "timestamp": 1749959531162, "type": "iosFunctions"}, {"action_id": "ksCBjJiwHZ", "executionTime": "7294ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Filter\"]", "timeout": 10, "timestamp": 1749959553346, "type": "waitTill"}, {"action_id": "xmelRkcdVx", "executionTime": "2652ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "method": "locator", "timeout": 10, "timestamp": 1749959577226, "type": "tap"}, {"action_id": "0bnBNoqPt8", "executionTime": "9913ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"SKU :\"]", "timeout": 10, "timestamp": 1749959593634, "type": "waitTill"}, {"action_id": "HZT2s0AzX7", "executionTime": "3583ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"(\"]/following-sibling::*[1]", "method": "locator", "timeout": 10, "timestamp": 1750323423267, "type": "tap"}, {"action_id": "QkaF93zxUg", "executionTime": "2906ms", "locator_type": "xpath", "locator_value": "(//XCUIElementTypeStaticText[@name=\"Value\"])[1]", "timeout": 10, "timestamp": 1750323606702, "type": "exists"}, {"action_id": "Ef6OumM2eS", "executionTime": "4194ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"to wishlist\")]", "method": "locator", "timeout": 10, "timestamp": 1749959612145, "type": "tap"}, {"action_id": "OKCHAK6HCJ", "executionTime": "4347ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 3 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1749959627955, "type": "tap"}, {"action_id": "x4Mid4HQ0Z", "count": 1, "direction": "left", "duration": 300, "end_x": 30, "end_y": 20, "executionTime": "2498ms", "interval": 0.5, "start_x": 90, "start_y": 20, "timestamp": 1749959799044, "type": "swipe", "vector_end": [0.3, 0.2], "vector_start": [0.9, 0.2]}, {"action_id": "RDQCFIxjA0", "count": 1, "direction": "left", "duration": 300, "end_x": 30, "end_y": 20, "executionTime": "2186ms", "interval": 0.5, "start_x": 90, "start_y": 20, "timestamp": 1749959805826, "type": "swipe", "vector_end": [0.3, 0.2], "vector_start": [0.9, 0.2]}, {"action_id": "wguGCt7OoB", "executionTime": "4085ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1750323885443, "type": "tap"}, {"action_id": "ylslyLAYKb", "count": 1, "direction": "left", "duration": 300, "end_x": 50, "end_y": 30, "executionTime": "2728ms", "interval": 0.5, "start_x": 50, "start_y": 70, "timestamp": 1749959866252, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "RbD937Xbte", "executionTime": "2846ms", "text_to_find": "out", "timeout": 30, "timestamp": 1749959881521, "type": "tapOnText"}, {"action_id": "OKCHAK6HCJ", "executionTime": "2841ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1750381062536, "type": "tap"}, {"action_id": "ipT2XD9io6", "executionTime": "3150ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtJoinTodayButton\"]", "method": "locator", "text_to_find": "Join", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "VfTTTtrliQ", "executionTime": "1148ms", "function_name": "alert_accept", "timestamp": *************, "type": "iosFunctions"}, {"action_id": "UqgDn5CuPY", "executionTime": "2766ms", "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"Create account\"]", "timeout": 15, "timestamp": *************, "type": "exists"}, {"action_id": "JLAJhxPdsl", "executionTime": "3666ms", "text_to_find": "Cancel", "timeout": 30, "timestamp": *************, "type": "tapOnText"}, {"action_id": "arH1CZCPXh", "executionTime": "3458ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "txtHomeAccountCtaSignIn", "timeout": 10, "timestamp": *************, "type": "waitTill"}, {"action_id": "0YgZZfWdYY", "expanded": false, "steps_loaded": true, "test_case_id": "Search_and_Add_Notebooks_20250620103536.json", "test_case_name": "Search and Add (Notebooks)", "test_case_steps": [{"action_id": "r9Ef3eOmlj", "text_to_find": "Find", "timeout": 30, "timestamp": *************, "type": "tapOnText"}, {"action_id": "8S8UskeUvp", "enter": true, "function_name": "text", "text": "Notebook", "timestamp": *************, "type": "iosFunctions"}, {"action_id": "qSYJYPHhYJ", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Filter\"]", "timeout": 30, "timestamp": *************, "type": "waitTill"}, {"action_id": "u7kJ2m8mFs", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::*[1]//XCUIElementTypeButton)[1]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "fpJSA5zFxF", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1750379828258, "type": "tap"}, {"action_id": "bCXOxC2J7X", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Delivery\"]", "timeout": 30, "timestamp": 1750379913061, "type": "waitTill"}], "test_case_steps_count": 6, "timestamp": 1750381445187, "type": "multiStep"}, {"action_id": "rPQ5EkTza1", "executionTime": "3447ms", "text_to_find": "Click", "timeout": 30, "timestamp": 1750381476570, "type": "tapOnText"}, {"action_id": "93bAew9Y4Y", "double_tap": false, "executionTime": "5149ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//XCUIElementTypeButton[contains(@name,\"store details\")])[1]", "method": "locator", "text_to_find": "Store", "timeout": 10, "timestamp": 1750381507288, "type": "tap"}, {"action_id": "njiHWyVooT", "executionTime": "4206ms", "image_filename": "banner-close-updated.png", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1750381675953, "type": "tap"}, {"action_id": "NW6M15JbAy", "count": 5, "direction": "up", "duration": 500, "end_x": 50, "end_y": 30, "executionTime": "16594ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Bags for Click & Collect orders\"]", "start_x": 50, "start_y": 70, "timestamp": 1750381747699, "type": "swipeTillVisible", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "2QEdm5WM18", "executionTime": "3856ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Bags for Click & Collect orders\"]", "method": "locator", "timeout": 10, "timestamp": 1750381780684, "type": "tap"}, {"action_id": "r0FfJ85LFM", "executionTime": "2369ms", "image_filename": "banner-close-updated.png", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1750381789146, "type": "tap"}, {"action_id": "8hCPyY2zPt", "executionTime": "2950ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"About KHub Stores\"]", "method": "locator", "timeout": 10, "timestamp": 1750381813348, "type": "tap"}, {"action_id": "ceF4VRTJlO", "executionTime": "2370ms", "image_filename": "banner-close-updated.png", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1750381904959, "type": "tap"}, {"action_id": "dJNRgTXoqs", "count": 1, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 70, "executionTime": "3589ms", "interval": 0.5, "start_x": 50, "start_y": 30, "timestamp": 1750382217101, "type": "swipe", "vector_end": [0.5, 0.7], "vector_start": [0.5, 0.3]}, {"action_id": "z1CfcW4xYT", "executionTime": "3647ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"<PERSON>move\")]", "method": "locator", "timeout": 10, "timestamp": 1750382292209, "type": "tap"}, {"action_id": "AEnFqnkOa1", "executionTime": "2879ms", "image_filename": "banner-close-updated.png", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": *************, "type": "tap"}, {"action_id": "OKCHAK6HCJ", "executionTime": "3541ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "ipT2XD9io6", "executionTime": "14088ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtMoreAccountJoinTodayButton\"]", "method": "locator", "text_to_find": "Join", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "VfTTTtrliQ", "executionTime": "813ms", "function_name": "alert_accept", "timestamp": *************, "type": "iosFunctions"}, {"action_id": "UqgDn5CuPY", "executionTime": "8240ms", "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"Create account\"]", "timeout": 15, "timestamp": *************, "type": "exists"}, {"action_id": "25UEKPIknm", "executionTime": "1061ms", "package_id": "env[appid]", "timestamp": *************, "type": "terminateApp"}, {"type": "cleanupSteps", "timestamp": *************, "test_case_id": "Kmart_AU_Cleanup_20250626204013.json", "test_case_name": "Kmart_AU_Cleanup", "test_case_steps_count": 0, "action_id": "2DPx8ODEvv"}], "labels": [], "updated": "2025-06-27 08:07:29"}