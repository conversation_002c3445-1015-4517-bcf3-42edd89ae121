<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Multistep Loading</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>Test Multistep Action Loading</h1>
        <div id="test-results" class="mt-4"></div>
        <div id="action-container" class="mt-4"></div>
    </div>

    <script>
        // Test function to load and display a multistep action
        async function testMultistepLoading() {
            const resultsDiv = document.getElementById('test-results');
            const containerDiv = document.getElementById('action-container');
            
            resultsDiv.innerHTML = '<div class="alert alert-info">Starting multistep loading test...</div>';
            
            try {
                // Load the test case with multistep actions
                const response = await fetch('/api/test_cases/load/Delivery__CNC_Copy_20250513201112_20250513201112.json');
                const data = await response.json();
                
                if (data.status === 'success' && data.test_case) {
                    const multistepActions = data.test_case.actions.filter(action => action.type === 'multiStep');
                    
                    resultsDiv.innerHTML = `
                        <div class="alert alert-success">
                            ✅ Successfully loaded test case: ${data.test_case.name}<br>
                            📊 Found ${multistepActions.length} multistep actions
                        </div>
                    `;
                    
                    // Test each multistep action
                    for (let i = 0; i < multistepActions.length; i++) {
                        const action = multistepActions[i];
                        
                        resultsDiv.innerHTML += `
                            <div class="alert alert-info">
                                🔍 Testing multistep action ${i + 1}: ${action.test_case_name}
                            </div>
                        `;
                        
                        // Create a container for this action
                        const actionDiv = document.createElement('div');
                        actionDiv.className = 'border p-3 mb-3';
                        actionDiv.innerHTML = `
                            <h5>Multistep Action: ${action.test_case_name}</h5>
                            <div class="multistep-container">
                                <div class="text-center py-2">
                                    <div class="spinner-border spinner-border-sm text-primary" role="status"></div>
                                    <span class="ms-2">Loading test case steps...</span>
                                </div>
                            </div>
                        `;
                        
                        containerDiv.appendChild(actionDiv);
                        
                        // Test loading the nested test case
                        try {
                            const nestedResponse = await fetch(`/api/test_cases/load/${action.test_case_id}`);
                            const nestedData = await nestedResponse.json();
                            
                            if (nestedData.status === 'success' && nestedData.test_case) {
                                const steps = nestedData.test_case.actions || [];
                                
                                // Replace the loading spinner with the actual steps
                                const multistepContainer = actionDiv.querySelector('.multistep-container');
                                multistepContainer.innerHTML = `
                                    <div class="alert alert-success">
                                        ✅ Loaded ${steps.length} steps successfully
                                    </div>
                                    <ul class="list-group">
                                        ${steps.map((step, index) => `
                                            <li class="list-group-item">
                                                <span class="badge bg-secondary me-2">${index + 1}</span>
                                                <span class="badge bg-primary me-2">${step.type}</span>
                                                ${step.type === 'multiStep' ? `Execute: ${step.test_case_name}` : 'Regular action'}
                                            </li>
                                        `).join('')}
                                    </ul>
                                `;
                                
                                resultsDiv.innerHTML += `
                                    <div class="alert alert-success">
                                        ✅ Successfully loaded ${steps.length} steps for: ${action.test_case_name}
                                    </div>
                                `;
                            } else {
                                throw new Error(nestedData.error || 'Failed to load nested test case');
                            }
                        } catch (error) {
                            resultsDiv.innerHTML += `
                                <div class="alert alert-danger">
                                    ❌ Failed to load steps for: ${action.test_case_name}<br>
                                    Error: ${error.message}
                                </div>
                            `;
                        }
                    }
                    
                    resultsDiv.innerHTML += `
                        <div class="alert alert-info">
                            🏁 Test completed. Check above for results.
                        </div>
                    `;
                } else {
                    throw new Error(data.error || 'Failed to load test case');
                }
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="alert alert-danger">
                        ❌ Test failed: ${error.message}
                    </div>
                `;
            }
        }
        
        // Run the test when the page loads
        document.addEventListener('DOMContentLoaded', testMultistepLoading);
    </script>
</body>
</html>
