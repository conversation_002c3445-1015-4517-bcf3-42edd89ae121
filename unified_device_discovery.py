"""
Unified device discovery module that aggregates devices from both iOS and Android platforms
"""
import subprocess
import logging
import sys
from pathlib import Path

logger = logging.getLogger(__name__)

def get_ios_devices():
    """Get all connected iOS devices"""
    devices = []
    try:
        # Get iOS devices using idevice_id
        idevice_output = subprocess.run(
            ['idevice_id', '-l'],
            capture_output=True,
            text=True,
            check=True
        ).stdout

        # Parse idevice_id output
        for line in idevice_output.strip().split('\n'):
            if line.strip():
                device_id = line.strip()

                # Get device name
                try:
                    device_name = subprocess.run(
                        ['idevicename', '-u', device_id],
                        capture_output=True,
                        text=True
                    ).stdout.strip()

                    # Get iOS version
                    ios_version = subprocess.run(
                        ['ideviceinfo', '-u', device_id, '-k', 'ProductVersion'],
                        capture_output=True,
                        text=True
                    ).stdout.strip()

                    # Get device model
                    device_model = subprocess.run(
                        ['ideviceinfo', '-u', device_id, '-k', 'ProductType'],
                        capture_output=True,
                        text=True
                    ).stdout.strip()

                    devices.append({
                        'id': device_id,
                        'udid': device_id,
                        'name': device_name or f"iOS Device ({device_id})",
                        'platform': 'iOS',
                        'osVersion': ios_version,
                        'model': device_model,
                        'status': 'Online',
                        'type': 'real'
                    })
                except Exception as e:
                    # Add with limited info
                    devices.append({
                        'id': device_id,
                        'udid': device_id,
                        'name': f"iOS Device ({device_id})",
                        'platform': 'iOS',
                        'osVersion': 'Unknown',
                        'model': 'Unknown',
                        'status': 'Online',
                        'type': 'real'
                    })
    except Exception as e:
        logger.warning(f"Error getting iOS devices: {e}")

    return devices

def get_android_devices():
    """Get all connected Android devices"""
    devices = []
    try:
        # Get Android devices using adb devices
        adb_output = subprocess.run(
            ['adb', 'devices', '-l'],
            capture_output=True,
            text=True,
            check=True
        ).stdout

        # Parse adb devices output
        lines = adb_output.strip().split('\n')[1:]  # Skip header line
        for line in lines:
            if line.strip() and 'device' in line:
                parts = line.split()
                if len(parts) >= 2:
                    device_id = parts[0]
                    status = parts[1]
                    
                    if status == 'device':  # Only include connected devices
                        # Get device name and properties
                        try:
                            # Get device model
                            device_model = subprocess.run(
                                ['adb', '-s', device_id, 'shell', 'getprop', 'ro.product.model'],
                                capture_output=True,
                                text=True
                            ).stdout.strip()

                            # Get Android version
                            android_version = subprocess.run(
                                ['adb', '-s', device_id, 'shell', 'getprop', 'ro.build.version.release'],
                                capture_output=True,
                                text=True
                            ).stdout.strip()

                            # Get device manufacturer
                            manufacturer = subprocess.run(
                                ['adb', '-s', device_id, 'shell', 'getprop', 'ro.product.manufacturer'],
                                capture_output=True,
                                text=True
                            ).stdout.strip()

                            device_name = f"{manufacturer} {device_model}".strip()

                            devices.append({
                                'id': device_id,
                                'udid': device_id,
                                'name': device_name or f"Android Device ({device_id})",
                                'platform': 'Android',
                                'osVersion': android_version,
                                'model': device_model,
                                'manufacturer': manufacturer,
                                'status': 'Online',
                                'type': 'real'
                            })
                        except Exception as e:
                            # Add with limited info
                            devices.append({
                                'id': device_id,
                                'udid': device_id,
                                'name': f"Android Device ({device_id})",
                                'platform': 'Android',
                                'osVersion': 'Unknown',
                                'model': 'Unknown',
                                'manufacturer': 'Unknown',
                                'status': 'Online',
                                'type': 'real'
                            })
    except Exception as e:
        logger.warning(f"Error getting Android devices: {e}")

    return devices

def get_all_devices():
    """
    Get all connected devices from both iOS and Android platforms
    
    Returns:
        list: List of device dictionaries with platform information
    """
    logger.info("Discovering devices from all platforms...")
    
    all_devices = []
    
    # Get iOS devices
    ios_devices = get_ios_devices()
    logger.info(f"Found {len(ios_devices)} iOS devices")
    all_devices.extend(ios_devices)
    
    # Get Android devices
    android_devices = get_android_devices()
    logger.info(f"Found {len(android_devices)} Android devices")
    all_devices.extend(android_devices)
    
    logger.info(f"Total devices found: {len(all_devices)}")
    
    # Sort devices by platform and name for consistent ordering
    all_devices.sort(key=lambda x: (x['platform'], x['name']))
    
    return all_devices

def get_device_by_id(device_id):
    """
    Get a specific device by ID from all platforms
    
    Args:
        device_id (str): The device ID to search for
        
    Returns:
        dict: Device information or None if not found
    """
    all_devices = get_all_devices()
    for device in all_devices:
        if device['id'] == device_id:
            return device
    return None

def get_devices_by_platform(platform):
    """
    Get devices filtered by platform
    
    Args:
        platform (str): Platform name ('iOS' or 'Android')
        
    Returns:
        list: List of devices for the specified platform
    """
    all_devices = get_all_devices()
    return [device for device in all_devices if device['platform'].lower() == platform.lower()]

def is_ios_device(device_id):
    """
    Check if a device ID belongs to an iOS device
    
    Args:
        device_id (str): Device ID to check
        
    Returns:
        bool: True if iOS device, False otherwise
    """
    device = get_device_by_id(device_id)
    return device and device['platform'] == 'iOS'

def is_android_device(device_id):
    """
    Check if a device ID belongs to an Android device
    
    Args:
        device_id (str): Device ID to check
        
    Returns:
        bool: True if Android device, False otherwise
    """
    device = get_device_by_id(device_id)
    return device and device['platform'] == 'Android'

def get_device_platform(device_id):
    """
    Get the platform of a device by its ID
    
    Args:
        device_id (str): Device ID to check
        
    Returns:
        str: Platform name ('iOS', 'Android') or None if not found
    """
    device = get_device_by_id(device_id)
    return device['platform'] if device else None
