"""
Unified routes for device management across iOS and Android platforms
"""
from flask import Blueprint, jsonify, request
from unified_device_discovery import get_all_devices, get_device_by_id, get_device_platform
import logging

logger = logging.getLogger(__name__)

unified_bp = Blueprint('unified', __name__, url_prefix='/api/unified')

@unified_bp.route('/devices', methods=['GET'])
def get_unified_devices():
    """Get all connected devices from both iOS and Android platforms"""
    try:
        devices = get_all_devices()
        return jsonify({
            "devices": devices,
            "total": len(devices),
            "platforms": {
                "iOS": len([d for d in devices if d['platform'] == 'iOS']),
                "Android": len([d for d in devices if d['platform'] == 'Android'])
            }
        })
    except Exception as e:
        logger.error(f"Error getting unified devices: {e}")
        return jsonify({"error": str(e)}), 500

@unified_bp.route('/devices/<device_id>', methods=['GET'])
def get_unified_device(device_id):
    """Get a specific device by ID from any platform"""
    try:
        device = get_device_by_id(device_id)
        if device:
            return jsonify(device)
        else:
            return jsonify({"error": "Device not found"}), 404
    except Exception as e:
        logger.error(f"Error getting device {device_id}: {e}")
        return jsonify({"error": str(e)}), 500

@unified_bp.route('/devices/<device_id>/platform', methods=['GET'])
def get_device_platform_info(device_id):
    """Get platform information for a specific device"""
    try:
        platform = get_device_platform(device_id)
        if platform:
            return jsonify({
                "device_id": device_id,
                "platform": platform,
                "backend_url": f"http://localhost:8080" if platform == 'iOS' else f"http://localhost:8081"
            })
        else:
            return jsonify({"error": "Device not found"}), 404
    except Exception as e:
        logger.error(f"Error getting platform for device {device_id}: {e}")
        return jsonify({"error": str(e)}), 500

@unified_bp.route('/devices/scan', methods=['POST'])
def scan_unified_devices():
    """Scan for available devices on all platforms"""
    try:
        devices = get_all_devices()
        return jsonify({
            "devices": devices,
            "total": len(devices),
            "platforms": {
                "iOS": len([d for d in devices if d['platform'] == 'iOS']),
                "Android": len([d for d in devices if d['platform'] == 'Android'])
            },
            "message": f"Scan completed. Found {len(devices)} devices."
        })
    except Exception as e:
        logger.error(f"Error scanning devices: {e}")
        return jsonify({"error": str(e)}), 500

@unified_bp.route('/session/start', methods=['POST'])
def start_unified_session():
    """Start a session with automatic platform routing"""
    try:
        data = request.json
        device_id = data.get('device_id')
        
        if not device_id:
            return jsonify({"error": "Device ID is required"}), 400
        
        # Get device platform
        platform = get_device_platform(device_id)
        if not platform:
            return jsonify({"error": "Device not found"}), 404
        
        # Route to appropriate backend
        backend_port = 8080 if platform == 'iOS' else 8081
        
        return jsonify({
            "device_id": device_id,
            "platform": platform,
            "backend_port": backend_port,
            "backend_url": f"http://localhost:{backend_port}",
            "message": f"Session should be started on {platform} backend (port {backend_port})"
        })
        
    except Exception as e:
        logger.error(f"Error starting unified session: {e}")
        return jsonify({"error": str(e)}), 500

@unified_bp.route('/platforms', methods=['GET'])
def get_platform_info():
    """Get information about available platforms and their backends"""
    return jsonify({
        "platforms": {
            "iOS": {
                "name": "iOS",
                "backend_port": 8080,
                "backend_url": "http://localhost:8080",
                "entry_point": "run.py",
                "description": "iOS device automation using XCUITest and WebDriverAgent"
            },
            "Android": {
                "name": "Android", 
                "backend_port": 8081,
                "backend_url": "http://localhost:8081",
                "entry_point": "run_android.py",
                "description": "Android device automation using UiAutomator2 and ADB"
            }
        },
        "unified": {
            "discovery_endpoint": "/api/unified/devices",
            "session_routing": "/api/unified/session/start"
        }
    })

@unified_bp.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint for the unified service"""
    try:
        devices = get_all_devices()
        return jsonify({
            "status": "healthy",
            "timestamp": "2024-01-01T00:00:00Z",
            "devices_available": len(devices),
            "platforms": {
                "iOS": len([d for d in devices if d['platform'] == 'iOS']),
                "Android": len([d for d in devices if d['platform'] == 'Android'])
            }
        })
    except Exception as e:
        return jsonify({
            "status": "unhealthy",
            "error": str(e)
        }), 500
