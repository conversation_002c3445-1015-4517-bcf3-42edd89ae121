I've fixed multiple issues with the action execution system:

1. **Test Case Dropdowns Fixed**: 
   - Fixed Cleanup Steps and Multi Step action dropdowns not loading test cases
   - Updated JavaScript code to properly fetch and display test cases
   - Fixed API response handling and property name mismatches

2. **Unknown Action Type Errors Fixed**:
   - The main issue was that the Player class had hardcoded action types and didn't forward unknown actions to the ActionFactory
   - Modified both iOS and Android Player classes to forward unknown action types (like 'cleanupSteps', 'info', 'multiStep') to the ActionFactory
   - The ActionFactory already has all these actions registered properly

3. **Multi Step Actions Not Executing**:
   - The issue was the same - Player class wasn't forwarding 'multiStep' actions to ActionFactory
   - Now Multi Step actions in If/Else conditions should execute properly instead of just showing success

The fixes ensure that:
- All registered action types in ActionFactory (cleanupSteps, info, multiStep, etc.) are now accessible
- Test case dropdowns load properly with all available test cases
- Actions execute correctly instead of failing with "Unknown action type" errors

Please test the Cleanup Steps, INFO actions, and Multi Step actions in If/Else conditions to verify they now work correctly.
