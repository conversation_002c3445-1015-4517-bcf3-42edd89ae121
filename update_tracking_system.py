#!/usr/bin/env python3
"""
Mobile App Automation Tool - Tracking System Update Script

This script updates the application code to use the new unique ID system
for improved test case tracking and status consistency.

Features:
- Updates database tracking functions to use test_case_id instead of filename
- Modifies execution tracking to use test_execution_id
- Updates report generation to use unique IDs
- Ensures backward compatibility

Usage:
    python update_tracking_system.py [--platform ios|android|both] [--dry-run]
"""

import os
import sys
import re
import argparse
import logging
from pathlib import Path

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('tracking_system_update.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class TrackingSystemUpdater:
    """Updates the application code to use unique ID system"""
    
    def __init__(self, platform='both', dry_run=False):
        self.platform = platform
        self.dry_run = dry_run
        self.platforms = self._get_platforms()
        
    def _get_platforms(self):
        """Get list of platforms to process"""
        if self.platform == 'both':
            return ['ios', 'android']
        return [self.platform]
    
    def _get_platform_paths(self, platform):
        """Get platform-specific paths"""
        if platform == 'ios':
            return {
                'app_dir': 'app',
                'utils_dir': 'app/utils',
                'static_dir': 'app/static',
                'db_path': 'data/test_execution.db'
            }
        else:  # android
            return {
                'app_dir': 'app_android',
                'utils_dir': 'app_android/utils',
                'static_dir': 'app_android/static',
                'db_path': 'data/test_execution_port_8081.db'
            }
    
    def update_database_functions(self, platform):
        """Update database functions to use unique IDs"""
        paths = self._get_platform_paths(platform)
        database_file = os.path.join(paths['utils_dir'], 'database.py')
        
        if not os.path.exists(database_file):
            logger.warning(f"Database file not found for {platform}: {database_file}")
            return False
        
        logger.info(f"Updating database functions for {platform}")
        
        try:
            with open(database_file, 'r') as f:
                content = f.read()
            
            # Update track_test_execution function to use test_case_id
            updated_content = self._update_track_test_execution(content)
            
            # Update get_test_execution_status to use test_case_id
            updated_content = self._update_get_test_execution_status(updated_content)
            
            # Update execution tracking queries to use unique IDs
            updated_content = self._update_execution_queries(updated_content)
            
            if not self.dry_run:
                with open(database_file, 'w') as f:
                    f.write(updated_content)
                logger.info(f"Updated database functions for {platform}")
            else:
                logger.info(f"DRY RUN: Would update database functions for {platform}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error updating database functions for {platform}: {e}")
            return False
    
    def _update_track_test_execution(self, content):
        """Update track_test_execution function to use test_case_id"""
        # Add test_case_id parameter to function signature
        pattern = r'def track_test_execution\(([^)]+)\):'
        replacement = r'def track_test_execution(\1, test_case_id=None, test_execution_id=None):'
        content = re.sub(pattern, replacement, content)
        
        # Update the function to use test_case_id in database operations
        # Look for existing WHERE clauses that use filename
        filename_where_pattern = r'WHERE suite_id = \? AND test_idx = \? AND filename = \?'
        id_where_replacement = r'WHERE suite_id = ? AND test_idx = ? AND (test_case_id = ? OR filename = ?)'
        content = re.sub(filename_where_pattern, id_where_replacement, content)
        
        # Update INSERT statements to include new fields
        insert_pattern = r'INSERT INTO execution_tracking\s*\([^)]+\)\s*VALUES\s*\([^)]+\)'
        def update_insert(match):
            insert_stmt = match.group(0)
            if 'test_case_id' not in insert_stmt:
                # Add test_case_id and test_execution_id to the INSERT
                insert_stmt = insert_stmt.replace(
                    'execution_result)',
                    'execution_result, test_case_id, test_execution_id)'
                )
                insert_stmt = insert_stmt.replace(
                    'execution_result_str',
                    'execution_result_str, test_case_id, test_execution_id'
                )
            return insert_stmt
        
        content = re.sub(insert_pattern, update_insert, content)
        
        return content
    
    def _update_get_test_execution_status(self, content):
        """Update get_test_execution_status to use test_case_id"""
        # Update function signature
        pattern = r'def get_test_execution_status\(([^)]+)\):'
        replacement = r'def get_test_execution_status(\1, test_case_id=None):'
        content = re.sub(pattern, replacement, content)
        
        # Update queries to prefer test_case_id over filename
        query_pattern = r"SELECT \* FROM execution_tracking WHERE suite_id = \? AND test_idx = \? AND filename = \?"
        query_replacement = r"SELECT * FROM execution_tracking WHERE suite_id = ? AND (test_case_id = ? OR (test_case_id IS NULL AND filename = ?))"
        content = re.sub(query_pattern, query_replacement, content)
        
        return content
    
    def _update_execution_queries(self, content):
        """Update execution tracking queries to use unique IDs"""
        # Update get_execution_tracking_for_suite to order by test_execution_id
        order_pattern = r'ORDER BY et\.test_idx, et\.step_idx, et\.end_time DESC, et\.id DESC'
        order_replacement = r'ORDER BY et.test_execution_id DESC, et.test_idx, et.step_idx, et.end_time DESC, et.id DESC'
        content = re.sub(order_pattern, order_replacement, content)
        
        return content
    
    def update_report_generators(self, platform):
        """Update report generators to use unique IDs"""
        paths = self._get_platform_paths(platform)
        
        # Update reportGenerator.py
        report_gen_file = os.path.join(paths['utils_dir'], 'reportGenerator.py')
        if os.path.exists(report_gen_file):
            self._update_report_generator_file(report_gen_file, platform)
        
        # Update build_data_json.py
        build_data_file = os.path.join(paths['utils_dir'], 'build_data_json.py')
        if os.path.exists(build_data_file):
            self._update_build_data_json_file(build_data_file, platform)
        
        return True
    
    def _update_report_generator_file(self, file_path, platform):
        """Update reportGenerator.py to use unique IDs"""
        try:
            with open(file_path, 'r') as f:
                content = f.read()
            
            # Update update_test_data_with_execution_results to use test_execution_id
            pattern = r'def update_test_data_with_execution_results\(([^)]+)\):'
            if pattern not in content:
                # Function might have different name, look for the actual function
                pattern = r'# Create a mapping of action_id to execution result'
                if pattern in content:
                    # Add comment about using most recent execution results
                    replacement = '''# Create a mapping of action_id to execution result
        # Since execution_data is ordered by test_execution_id DESC, end_time DESC, 
        # the first occurrence of each action_id will be the most recent execution status'''
                    content = content.replace(pattern, replacement)
            
            if not self.dry_run:
                with open(file_path, 'w') as f:
                    f.write(content)
                logger.info(f"Updated report generator for {platform}")
            else:
                logger.info(f"DRY RUN: Would update report generator for {platform}")
                
        except Exception as e:
            logger.error(f"Error updating report generator for {platform}: {e}")
    
    def _update_build_data_json_file(self, file_path, platform):
        """Update build_data_json.py to use unique IDs"""
        try:
            with open(file_path, 'r') as f:
                content = f.read()
            
            # Add test_execution_id to the data structure
            pattern = r"'timestamp': datetime\.now\(\)\.strftime\('%Y-%m-%d %H:%M:%S'\)"
            replacement = r"'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),\n            'test_execution_id': suite_id"
            content = re.sub(pattern, replacement, content)
            
            if not self.dry_run:
                with open(file_path, 'w') as f:
                    f.write(content)
                logger.info(f"Updated build_data_json for {platform}")
            else:
                logger.info(f"DRY RUN: Would update build_data_json for {platform}")
                
        except Exception as e:
            logger.error(f"Error updating build_data_json for {platform}: {e}")
    
    def update_frontend_code(self, platform):
        """Update frontend JavaScript code to use unique IDs"""
        paths = self._get_platform_paths(platform)
        js_dir = os.path.join(paths['static_dir'], 'js')
        
        if not os.path.exists(js_dir):
            logger.warning(f"JavaScript directory not found for {platform}: {js_dir}")
            return False
        
        # Update main.js to use test_case_id in retry functionality
        main_js = os.path.join(js_dir, 'main.js')
        if os.path.exists(main_js):
            self._update_main_js(main_js, platform)
        
        # Update execution-manager.js to use unique IDs
        exec_manager_js = os.path.join(js_dir, 'execution-manager.js')
        if os.path.exists(exec_manager_js):
            self._update_execution_manager_js(exec_manager_js, platform)
        
        return True
    
    def _update_main_js(self, file_path, platform):
        """Update main.js to use test_case_id"""
        try:
            with open(file_path, 'r') as f:
                content = f.read()
            
            # Update retry functionality to include test_case_id
            pattern = r'testCaseName: testCaseName,'
            replacement = r'testCaseName: testCaseName,\n                testCaseId: testCaseId || null,'
            content = re.sub(pattern, replacement, content)
            
            if not self.dry_run:
                with open(file_path, 'w') as f:
                    f.write(content)
                logger.info(f"Updated main.js for {platform}")
            else:
                logger.info(f"DRY RUN: Would update main.js for {platform}")
                
        except Exception as e:
            logger.error(f"Error updating main.js for {platform}: {e}")
    
    def _update_execution_manager_js(self, file_path, platform):
        """Update execution-manager.js to use unique IDs"""
        try:
            with open(file_path, 'r') as f:
                content = f.read()
            
            # Add test_execution_id tracking
            pattern = r'this\.currentExecutionId = null;'
            replacement = r'this.currentExecutionId = null;\n        this.testExecutionId = null;'
            content = re.sub(pattern, replacement, content)
            
            if not self.dry_run:
                with open(file_path, 'w') as f:
                    f.write(content)
                logger.info(f"Updated execution-manager.js for {platform}")
            else:
                logger.info(f"DRY RUN: Would update execution-manager.js for {platform}")
                
        except Exception as e:
            logger.error(f"Error updating execution-manager.js for {platform}: {e}")
    
    def run_update(self):
        """Run the complete tracking system update"""
        logger.info("Starting tracking system update process")
        logger.info(f"Platform(s): {', '.join(self.platforms)}")
        logger.info(f"Dry run: {self.dry_run}")
        
        success = True
        for platform in self.platforms:
            logger.info(f"\n=== Updating {platform.upper()} platform ===")
            
            # Update database functions
            if not self.update_database_functions(platform):
                logger.error(f"Failed to update database functions for {platform}")
                success = False
                continue
            
            # Update report generators
            if not self.update_report_generators(platform):
                logger.error(f"Failed to update report generators for {platform}")
                success = False
                continue
            
            # Update frontend code
            if not self.update_frontend_code(platform):
                logger.error(f"Failed to update frontend code for {platform}")
                success = False
                continue
            
            logger.info(f"Successfully updated tracking system for {platform}")
        
        if success:
            logger.info("\n=== TRACKING SYSTEM UPDATE COMPLETED SUCCESSFULLY ===")
            logger.info("All platforms have been updated to use the unique ID system")
        else:
            logger.error("\n=== TRACKING SYSTEM UPDATE FAILED ===")
            logger.error("Some platforms failed to update. Check logs for details.")
        
        return success


def main():
    """Main entry point for the update script"""
    parser = argparse.ArgumentParser(
        description='Mobile App Automation Tool - Tracking System Update Script'
    )
    
    parser.add_argument(
        '--platform',
        choices=['ios', 'android', 'both'],
        default='both',
        help='Platform to update (default: both)'
    )
    
    parser.add_argument(
        '--dry-run',
        action='store_true',
        help='Perform a dry run without making changes'
    )
    
    parser.add_argument(
        '--verbose',
        action='store_true',
        help='Enable verbose logging'
    )
    
    args = parser.parse_args()
    
    # Set logging level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Run update
    updater = TrackingSystemUpdater(platform=args.platform, dry_run=args.dry_run)
    
    if updater.run_update():
        logger.info("Tracking system update completed successfully")
        return 0
    else:
        logger.error("Tracking system update failed")
        return 1


if __name__ == '__main__':
    sys.exit(main())
