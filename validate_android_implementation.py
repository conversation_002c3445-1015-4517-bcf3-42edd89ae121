#!/usr/bin/env python3
"""
Validation script for Android implementation
Tests that all components are properly set up
"""
import os
import sys
import subprocess
import importlib.util
from pathlib import Path

def check_file_exists(file_path, description):
    """Check if a file exists"""
    if os.path.exists(file_path):
        print(f"✅ {description}: {file_path}")
        return True
    else:
        print(f"❌ {description}: {file_path} - NOT FOUND")
        return False

def check_directory_exists(dir_path, description):
    """Check if a directory exists"""
    if os.path.isdir(dir_path):
        print(f"✅ {description}: {dir_path}")
        return True
    else:
        print(f"❌ {description}: {dir_path} - NOT FOUND")
        return False

def check_import(module_path, description):
    """Check if a module can be imported"""
    try:
        spec = importlib.util.spec_from_file_location("test_module", module_path)
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        print(f"✅ {description}: Import successful")
        return True
    except Exception as e:
        print(f"❌ {description}: Import failed - {e}")
        return False

def check_command(command, description):
    """Check if a command is available"""
    try:
        result = subprocess.run(command, capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"✅ {description}: Available")
            return True
        else:
            print(f"❌ {description}: Command failed")
            return False
    except Exception as e:
        print(f"❌ {description}: Not available - {e}")
        return False

def main():
    """Main validation function"""
    print("🔍 Validating Android Implementation...")
    print("=" * 50)
    
    issues = []
    
    # Check folder structure
    print("\n📁 Folder Structure:")
    if not check_directory_exists("app", "iOS app folder"):
        issues.append("iOS app folder missing")
    if not check_directory_exists("app_android", "Android app folder"):
        issues.append("Android app folder missing")
    
    # Check entry points
    print("\n🚀 Entry Points:")
    if not check_file_exists("run.py", "iOS entry point"):
        issues.append("iOS entry point missing")
    if not check_file_exists("run_android.py", "Android entry point"):
        issues.append("Android entry point missing")
    
    # Check configuration files
    print("\n⚙️ Configuration Files:")
    if not check_file_exists("config.py", "iOS configuration"):
        issues.append("iOS configuration missing")
    if not check_file_exists("config_android.py", "Android configuration"):
        issues.append("Android configuration missing")
    
    # Check unified components
    print("\n🔗 Unified Components:")
    if not check_file_exists("unified_device_discovery.py", "Unified device discovery"):
        issues.append("Unified device discovery missing")
    if not check_file_exists("session_router.py", "Session router"):
        issues.append("Session router missing")
    if not check_file_exists("unified_routes.py", "Unified routes"):
        issues.append("Unified routes missing")
    
    # Check Android-specific files
    print("\n🤖 Android-Specific Files:")
    android_files = [
        ("app_android/actions/android_functions_action.py", "Android Functions action"),
        ("app_android/routes/devices.py", "Android device routes"),
        ("app_android/utils/appium_device_controller.py", "Android device controller"),
        ("app_android/app.py", "Android Flask app")
    ]
    
    for file_path, description in android_files:
        if not check_file_exists(file_path, description):
            issues.append(f"{description} missing")
    
    # Check if iOS-specific files were removed from Android
    print("\n🍎 iOS Files Removed from Android:")
    ios_files_to_remove = [
        "app_android/actions/ios_functions_action.py",
        "app_android/utils/ios_device.py"
    ]
    
    for file_path in ios_files_to_remove:
        if os.path.exists(file_path):
            print(f"❌ iOS file still exists in Android: {file_path}")
            issues.append(f"iOS file not removed from Android: {file_path}")
        else:
            print(f"✅ iOS file properly removed from Android: {file_path}")
    
    # Check external dependencies
    print("\n🛠️ External Dependencies:")
    if not check_command(["adb", "version"], "ADB (Android Debug Bridge)"):
        issues.append("ADB not available")
    
    # Try to check iOS tools (may not be available on all systems)
    check_command(["idevice_id", "--version"], "iOS device tools (optional)")
    
    # Check Python imports
    print("\n🐍 Python Imports:")
    if not check_import("config_android.py", "Android configuration import"):
        issues.append("Android configuration import failed")
    
    if not check_import("unified_device_discovery.py", "Unified device discovery import"):
        issues.append("Unified device discovery import failed")
    
    # Summary
    print("\n" + "=" * 50)
    if issues:
        print(f"❌ Validation completed with {len(issues)} issues:")
        for i, issue in enumerate(issues, 1):
            print(f"   {i}. {issue}")
        print("\n🔧 Please resolve these issues before using the Android implementation.")
        return False
    else:
        print("✅ All validation checks passed!")
        print("\n🎉 Android implementation is ready to use!")
        print("\nNext steps:")
        print("1. Start iOS backend: python run.py --port 8080")
        print("2. Start Android backend: python run_android.py --port 8081")
        print("3. Connect iOS and Android devices")
        print("4. Open web UI and test both platforms")
        return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
